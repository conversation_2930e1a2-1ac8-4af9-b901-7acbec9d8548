"use client";
import { AlertDialogUI } from "@/components/alertDialog";
import { PaginationUI } from "@/components/pagination";
import Tables from "@/components/tables/Tables";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import { maskCep, maskCpf, maskPhone } from "@/lib/mask";
import ClienteService from "@/lib/services/clienteService";
import {
  Filter,
  Grid3X3,
  List,
  Pencil,
  Plus,
  Search,
  Trash2,
  Users,
  X,
} from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

function ClienteContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [clientes, setClientes] = useState([]);
  const { toast } = useToast();
  const [showDialog, setShowDialog] = useState(false);
  const [confirmCallback, setConfirmCallback] = useState(null);
  const [totalPage, setTotalPage] = useState(0);
  const [loading, setLoading] = useState(false);

  // Novos estados para filtros
  const [filtroNome, setFiltroNome] = useState("");
  const [filtroCpf, setFiltroCpf] = useState("");
  const [filtroTelefone, setFiltroTelefone] = useState("");
  const [filtroEmail, setFiltroEmail] = useState("");

  // Estado para controlar visualização (table ou cards) - inicia com cards
  const [viewMode, setViewMode] = useState("cards"); // "table" ou "cards"

  const columns = [
    { headerName: "Nome", field: "nome" },
    {
      headerName: "CPF",
      field: "cpf",
      renderCell: (params) => maskCpf(params.row.cpf),
    },
    {
      headerName: "Telefone",
      field: "telefone",
      renderCell: (params) => maskPhone(params.row.telefone),
    },
    { headerName: "E-mail", field: "email" },
    {
      headerName: "CEP",
      field: "cep",
      renderCell: (params) => maskCep(params.row.cep),
    },
    { headerName: "Endereco", field: "endereco" },
    { headerName: "Cidade", field: "cidade" },
    { headerName: "Estado", field: "estado" },
    {
      headerName: "Ações",
      field: "acoes",
      renderCell: (params) => (
        <div className="flex justify-center gap-2">
          <Button
            size="sm"
            onClick={() => editarCliente(params.row.id)}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white transition-all duration-200 transform hover:scale-105 border-0"
          >
            <Pencil className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            onClick={() => deletarCliente(params.row.id)}
            className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white transition-all duration-200 transform hover:scale-105 border-0"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ];

  const editarCliente = (id) => {
    router.push(`/admin/cliente/editar/${id}`);
  };

  const deletarCliente = async (id) => {
    setShowDialog(true);
    setConfirmCallback(() => async () => {
      setLoading(true);
      const clienteService = new ClienteService();
      const deletar = await clienteService.DeletarCliente(id);
      if (!deletar) {
        setShowDialog(false);
        setLoading(false);
        return toast({
          title: "Erro",
          description: "Erro ao deletar cliente",
          variant: "destructive",
        });
      }

      toast({
        title: "Sucesso",
        description: "Cliente deletado com sucesso",
      });
      setShowDialog(false);
      setLoading(false);
      fetchClientes();
    });
  };

  const aplicarFiltros = () => {
    setLoading(true);
    const params = new URLSearchParams(searchParams);

    // Adicionar filtros aos parâmetros
    if (filtroNome) params.set("nome", filtroNome);
    else params.delete("nome");

    if (filtroCpf) {
      // Remover formatação do CPF antes de enviar
      const cpfSemFormatacao = filtroCpf.replace(/\D/g, "");
      params.set("cpf", cpfSemFormatacao);
    } else {
      params.delete("cpf");
    }

    if (filtroTelefone) {
      // Remover formatação do telefone antes de enviar
      const telefoneSemFormatacao = filtroTelefone.replace(/\D/g, "");
      params.set("telefone", telefoneSemFormatacao);
    } else {
      params.delete("telefone");
    }

    if (filtroEmail) params.set("email", filtroEmail);
    else params.delete("email");

    // Resetar para página 1 ao filtrar
    params.set("page", 1);

    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  const limparFiltros = () => {
    setFiltroNome("");
    setFiltroCpf("");
    setFiltroTelefone("");
    setFiltroEmail("");

    const params = new URLSearchParams();
    params.set("page", 1);
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  const fetchClientes = async (params) => {
    setLoading(true);
    const clienteService = new ClienteService();

    // Converter o objeto URLSearchParams para string de consulta
    const queryString = params.toString();

    const clientes = await clienteService.ListarClientes(queryString);
    if (!clientes) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao buscar clientes",
        variant: "destructive",
      });
    }
    setClientes(clientes.items);
    setTotalPage(clientes.totalPages);
    setLoading(false);
  };

  // Componente para renderizar cards de clientes
  const ClienteCard = ({ cliente }) => (
    <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
            <Users className="h-6 w-6 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-900">{cliente.nome}</h3>
            <p className="text-sm text-gray-600">Cliente #{cliente.id}</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button
            size="sm"
            onClick={() => editarCliente(cliente.id)}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white transition-all duration-200 transform hover:scale-105 border-0"
          >
            <Pencil className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            onClick={() => deletarCliente(cliente.id)}
            className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white transition-all duration-200 transform hover:scale-105 border-0"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex items-center space-x-2 text-gray-600">
          <Users className="h-4 w-4" />
          <span className="text-sm">{maskCpf(cliente.cpf)}</span>
        </div>
        <div className="flex items-center space-x-2 text-gray-600">
          <Search className="h-4 w-4" />
          <span className="text-sm">{maskPhone(cliente.telefone)}</span>
        </div>
        <div className="flex items-center space-x-2 text-gray-600">
          <Filter className="h-4 w-4" />
          <span className="text-sm">{cliente.email}</span>
        </div>
        <div className="flex items-center space-x-2 text-gray-600">
          <Plus className="h-4 w-4" />
          <span className="text-sm">
            {cliente.endereco}, {cliente.cidade} - {cliente.estado}
          </span>
        </div>
        <div className="flex items-center space-x-2 text-gray-600">
          <Plus className="h-4 w-4" />
          <span className="text-sm">CEP: {maskCep(cliente.cep)}</span>
        </div>
      </div>
    </div>
  );

  useEffect(() => {
    fetchClientes(searchParams);
  }, [searchParams]);

  const currentDate = new Date().toLocaleDateString("pt-BR", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <div className="min-h-screen p-6">
      <AlertDialogUI
        title="Confirmação de exclusão"
        description="Deseja realmente deletar este cliente?"
        showDialog={showDialog}
        setShowDialog={setShowDialog}
        onConfirm={confirmCallback}
      />

      {/* Header moderno */}
      <div className="mb-8 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Gestão de Clientes
              </h1>
              <p className="text-gray-600 mt-2 text-lg">
                {currentDate} • Gerencie seus clientes cadastrados
              </p>
            </div>
            <div className="mt-4 lg:mt-0 flex items-center space-x-4">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-4 py-2 rounded-xl flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span className="font-medium">{clientes.length} Clientes</span>
              </div>
              <Link href="/admin/cliente/novo">
                <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-6 py-3 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center space-x-2">
                  <Plus className="h-5 w-5" />
                  <span>Novo Cliente</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Seção de filtros moderna */}
      <div className="mb-8 animate-slideInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl">
                <Filter className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">
                  Filtros de Busca
                </h2>
                <p className="text-gray-600 text-sm">
                  Refine sua pesquisa de clientes
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="group">
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Nome do Cliente
              </label>
              <Input
                placeholder="Digite o nome..."
                value={filtroNome}
                onChange={(e) => setFiltroNome(e.target.value)}
                className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200"
              />
            </div>
            <div className="group">
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                CPF
              </label>
              <Input
                placeholder="000.000.000-00"
                value={filtroCpf}
                onChange={(e) => setFiltroCpf(maskCpf(e.target.value))}
                className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200"
              />
            </div>
            <div className="group">
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Telefone
              </label>
              <Input
                placeholder="(00) 00000-0000"
                value={filtroTelefone}
                onChange={(e) => setFiltroTelefone(maskPhone(e.target.value))}
                className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200"
              />
            </div>
            <div className="group">
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                E-mail
              </label>
              <Input
                placeholder="<EMAIL>"
                value={filtroEmail}
                onChange={(e) => setFiltroEmail(e.target.value)}
                className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200"
              />
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-3">
            <Button
              variant="outline"
              onClick={limparFiltros}
              className="border-gray-300 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2"
            >
              <X className="w-4 h-4" />
              <span>Limpar Filtros</span>
            </Button>
            <Button
              onClick={aplicarFiltros}
              className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white transition-all duration-200 transform hover:scale-105 flex items-center space-x-2"
            >
              <Search className="w-4 h-4" />
              <span>Aplicar Filtros</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="animate-fadeInUp">
        {loading ? (
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-12">
            <div className="flex flex-col justify-center items-center space-y-4">
              <div className="p-4 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                <Spinner className="text-white" size="large" />
              </div>
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Carregando clientes...
                </h3>
                <p className="text-gray-600">
                  Aguarde enquanto buscamos os dados
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">
                      Lista de Clientes
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {clientes.length} cliente
                      {clientes.length !== 1 ? "s" : ""} encontrado
                      {clientes.length !== 1 ? "s" : ""}
                    </p>
                  </div>
                </div>

                {/* Botões de alternância de visualização */}
                <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
                  <Button
                    size="sm"
                    variant={viewMode === "cards" ? "default" : "ghost"}
                    onClick={() => setViewMode("cards")}
                    className={`transition-all duration-200 h-8 ${
                      viewMode === "cards"
                        ? "bg-gradient-to-r from-emerald-500 to-teal-500 text-white shadow-lg"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <Grid3X3 className="h-3 w-3 mr-1" />
                    Cards
                  </Button>
                  <Button
                    size="sm"
                    variant={viewMode === "table" ? "default" : "ghost"}
                    onClick={() => setViewMode("table")}
                    className={`transition-all duration-200 h-8 ${
                      viewMode === "table"
                        ? "bg-white shadow-sm text-gray-900"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <List className="h-3 w-3 mr-1" />
                    Tabela
                  </Button>
                </div>
              </div>
            </div>

            {clientes.length === 0 ? (
              <div className="p-12 text-center">
                <div className="flex flex-col items-center space-y-4">
                  <div className="p-4 bg-gray-100 rounded-full">
                    <Users className="h-12 w-12 text-gray-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Nenhum cliente encontrado
                    </h3>
                    <p className="text-gray-600">
                      Não há clientes cadastrados ou que correspondam aos
                      filtros aplicados.
                    </p>
                  </div>
                  <Link href="/admin/cliente/novo">
                    <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white">
                      <Plus className="h-4 w-4 mr-2" />
                      Cadastrar Primeiro Cliente
                    </Button>
                  </Link>
                </div>
              </div>
            ) : viewMode === "cards" ? (
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 animate-fadeInUp">
                  {clientes.map((cliente, index) => (
                    <div
                      key={cliente.id}
                      className="animate-fadeInUp"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <ClienteCard cliente={cliente} />
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Tables data={clientes} columns={columns} />
              </div>
            )}

            {totalPage > 1 && (
              <div className="p-6 border-t border-gray-200 bg-gray-50/50">
                <div className="flex justify-end items-center">
                  <PaginationUI totalPage={totalPage} />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default function Cliente() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen p-6">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-12">
            <div className="flex flex-col justify-center items-center space-y-4">
              <div className="p-4 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                <Spinner className="text-white" size="large" />
              </div>
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Carregando clientes...
                </h3>
                <p className="text-gray-600">
                  Aguarde enquanto buscamos os dados
                </p>
              </div>
            </div>
          </div>
        </div>
      }
    >
      <ClienteContent />
    </Suspense>
  );
}
