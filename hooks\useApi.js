import { useState, useCallback } from 'react';
import { LoadingState, ErrorType } from '@/types';
import { ApiError } from '@/utils/api';

export const useApi = (apiFunction) => {
  const [state, setState] = useState({
    data: null,
    loading: LoadingState.IDLE,
    error: null
  });

  const execute = useCallback(async (...args) => {
    setState(prev => ({
      ...prev,
      loading: LoadingState.LOADING,
      error: null
    }));

    try {
      const result = await apiFunction(...args);
      
      setState({
        data: result,
        loading: LoadingState.SUCCESS,
        error: null
      });

      return result;
    } catch (error) {
      const apiError = error instanceof ApiError ? error : new ApiError(
        error.message || 'Erro desconhecido',
        0,
        ErrorType.UNKNOWN
      );

      setState({
        data: null,
        loading: LoadingState.ERROR,
        error: apiError
      });

      throw apiError;
    }
  }, [apiFunction]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: LoadingState.IDLE,
      error: null
    });
  }, []);

  return {
    ...state,
    execute,
    reset,
    isLoading: state.loading === LoadingState.LOADING,
    isSuccess: state.loading === LoadingState.SUCCESS,
    isError: state.loading === LoadingState.ERROR,
    isIdle: state.loading === LoadingState.IDLE
  };
};

export const useAsyncOperation = () => {
  const [operations, setOperations] = useState({});

  const executeOperation = useCallback(async (key, operation) => {
    setOperations(prev => ({
      ...prev,
      [key]: { loading: true, error: null }
    }));

    try {
      const result = await operation();
      
      setOperations(prev => ({
        ...prev,
        [key]: { loading: false, error: null }
      }));

      return result;
    } catch (error) {
      const apiError = error instanceof ApiError ? error : new ApiError(
        error.message || 'Erro desconhecido',
        0,
        ErrorType.UNKNOWN
      );

      setOperations(prev => ({
        ...prev,
        [key]: { loading: false, error: apiError }
      }));

      throw apiError;
    }
  }, []);

  const getOperationState = useCallback((key) => {
    return operations[key] || { loading: false, error: null };
  }, [operations]);

  const isOperationLoading = useCallback((key) => {
    return getOperationState(key).loading;
  }, [getOperationState]);

  const getOperationError = useCallback((key) => {
    return getOperationState(key).error;
  }, [getOperationState]);

  const clearOperation = useCallback((key) => {
    setOperations(prev => {
      const newOperations = { ...prev };
      delete newOperations[key];
      return newOperations;
    });
  }, []);

  const clearAllOperations = useCallback(() => {
    setOperations({});
  }, []);

  return {
    executeOperation,
    getOperationState,
    isOperationLoading,
    getOperationError,
    clearOperation,
    clearAllOperations
  };
};

export const usePagination = (initialPageSize = 10) => {
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: initialPageSize,
    totalItems: 0,
    totalPages: 0
  });

  const setPage = useCallback((page) => {
    setPagination(prev => ({
      ...prev,
      currentPage: Math.max(1, Math.min(page, prev.totalPages))
    }));
  }, []);

  const setPageSize = useCallback((size) => {
    setPagination(prev => ({
      ...prev,
      pageSize: size,
      currentPage: 1
    }));
  }, []);

  const setTotalItems = useCallback((total) => {
    setPagination(prev => ({
      ...prev,
      totalItems: total,
      totalPages: Math.ceil(total / prev.pageSize)
    }));
  }, []);

  const nextPage = useCallback(() => {
    setPage(pagination.currentPage + 1);
  }, [pagination.currentPage, setPage]);

  const previousPage = useCallback(() => {
    setPage(pagination.currentPage - 1);
  }, [pagination.currentPage, setPage]);

  const goToFirstPage = useCallback(() => {
    setPage(1);
  }, [setPage]);

  const goToLastPage = useCallback(() => {
    setPage(pagination.totalPages);
  }, [pagination.totalPages, setPage]);

  const reset = useCallback(() => {
    setPagination({
      currentPage: 1,
      pageSize: initialPageSize,
      totalItems: 0,
      totalPages: 0
    });
  }, [initialPageSize]);

  return {
    ...pagination,
    setPage,
    setPageSize,
    setTotalItems,
    nextPage,
    previousPage,
    goToFirstPage,
    goToLastPage,
    reset,
    hasNextPage: pagination.currentPage < pagination.totalPages,
    hasPreviousPage: pagination.currentPage > 1,
    startIndex: (pagination.currentPage - 1) * pagination.pageSize,
    endIndex: Math.min(pagination.currentPage * pagination.pageSize, pagination.totalItems)
  };
};
