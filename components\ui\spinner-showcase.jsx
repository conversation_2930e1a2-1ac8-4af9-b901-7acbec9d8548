import React, { useState } from 'react';
import { Spinner } from './spinner';
import { Button } from './button';

/**
 * Showcase do novo design do Spinner
 * Para demonstrar todas as variantes e tamanhos
 */
export function SpinnerShowcase() {
  const [activeDemo, setActiveDemo] = useState('default');

  return (
    <div className="p-8 space-y-8 bg-gradient-to-br from-slate-50 to-slate-100 min-h-screen">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-slate-800">
          Beautiful Professional Spinner
        </h1>
        <p className="text-slate-600">
          Redesigned with modern aesthetics and smooth animations
        </p>
      </div>

      {/* Controles */}
      <div className="flex justify-center gap-2">
        {['default', 'contained', 'glass'].map((variant) => (
          <Button
            key={variant}
            variant={activeDemo === variant ? 'default' : 'outline'}
            size="sm"
            onClick={() => setActiveDemo(variant)}
            className="capitalize"
          >
            {variant}
          </Button>
        ))}
      </div>

      {/* Demonstração dos tamanhos */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Small */}
        <div className="text-center space-y-4">
          <h3 className="text-lg font-semibold text-slate-700">Small</h3>
          <div className="flex justify-center p-8 bg-white rounded-xl shadow-sm">
            <Spinner 
              size="small" 
              variant={activeDemo}
              message="Loading..."
            />
          </div>
        </div>

        {/* Medium */}
        <div className="text-center space-y-4">
          <h3 className="text-lg font-semibold text-slate-700">Medium</h3>
          <div className="flex justify-center p-8 bg-white rounded-xl shadow-sm">
            <Spinner 
              size="medium" 
              variant={activeDemo}
              message="Processing data..."
            />
          </div>
        </div>

        {/* Large */}
        <div className="text-center space-y-4">
          <h3 className="text-lg font-semibold text-slate-700">Large</h3>
          <div className="flex justify-center p-8 bg-white rounded-xl shadow-sm">
            <Spinner 
              size="large" 
              variant={activeDemo}
              message="Please wait while we load your content..."
            />
          </div>
        </div>
      </div>

      {/* Exemplos de uso */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-slate-800 text-center">
          Usage Examples
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Em botão */}
          <div className="bg-white p-6 rounded-xl shadow-sm space-y-4">
            <h4 className="font-semibold text-slate-700">In Button</h4>
            <Button disabled className="w-full">
              <Spinner size="small" className="mr-2" />
              Saving changes...
            </Button>
          </div>

          {/* Loading de página */}
          <div className="bg-white p-6 rounded-xl shadow-sm space-y-4">
            <h4 className="font-semibold text-slate-700">Page Loading</h4>
            <div className="h-32 flex items-center justify-center border-2 border-dashed border-slate-200 rounded-lg">
              <Spinner 
                variant="contained" 
                size="medium"
                message="Loading dashboard..."
              />
            </div>
          </div>
        </div>
      </div>

      {/* Características */}
      <div className="bg-white p-6 rounded-xl shadow-sm">
        <h3 className="text-xl font-bold text-slate-800 mb-4">
          ✨ Design Features
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
          <div className="space-y-1">
            <div className="font-medium text-emerald-600">🎨 Beautiful Design</div>
            <div className="text-slate-600">Modern gradients and shadows</div>
          </div>
          <div className="space-y-1">
            <div className="font-medium text-emerald-600">⚡ Smooth Animations</div>
            <div className="text-slate-600">GPU-accelerated transitions</div>
          </div>
          <div className="space-y-1">
            <div className="font-medium text-emerald-600">🎯 Multiple Effects</div>
            <div className="text-slate-600">Spin, glow, and pulse effects</div>
          </div>
          <div className="space-y-1">
            <div className="font-medium text-emerald-600">📱 Responsive</div>
            <div className="text-slate-600">Adapts to any container</div>
          </div>
          <div className="space-y-1">
            <div className="font-medium text-emerald-600">♿ Accessible</div>
            <div className="text-slate-600">Full ARIA support</div>
          </div>
          <div className="space-y-1">
            <div className="font-medium text-emerald-600">🔄 Compatible</div>
            <div className="text-slate-600">Works with existing code</div>
          </div>
        </div>
      </div>
    </div>
  );
}
