"use client";
import { AlertDialogUI } from "@/components/alertDialog";
import { PaginationUI } from "@/components/pagination";
import Tables from "@/components/tables/Tables";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import { maskCpf, maskPhone } from "@/lib/mask";
import FuncionarioService from "@/lib/services/funcionarioService";
import {
  Filter,
  Grid3X3,
  List,
  Pencil,
  Plus,
  Search,
  Trash2,
  Users,
  X,
} from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

export default function Funcionario() {
  return (
    <Suspense fallback={<Spinner />}>
      <Content />
    </Suspense>
  );
}

function Content() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [funcionarios, setFuncionarios] = useState([]);
  const { toast } = useToast();
  const [showDialog, setShowDialog] = useState(false);
  const [confirmCallback, setConfirmCallback] = useState(null);
  const [totalPage, setTotalPage] = useState(0);
  const [loading, setLoading] = useState(false);

  // Estados para filtros e visualização
  const [filtroNome, setFiltroNome] = useState("");
  const [filtroCpf, setFiltroCpf] = useState("");
  const [filtroTelefone, setFiltroTelefone] = useState("");
  const [filtroEmail, setFiltroEmail] = useState("");
  const [viewMode, setViewMode] = useState("cards"); // "table" ou "cards"

  const columns = [
    { headerName: "Nome", field: "nomeCompleto" },
    {
      headerName: "CPF",
      field: "cpf",
      renderCell: (params) => maskCpf(params.row.cpf),
    },
    {
      headerName: "Telefone",
      field: "telefone",
      renderCell: (params) => maskPhone(params.row.telefone),
    },
    { headerName: "E-mail", field: "email" },
    {
      headerName: "Ações",
      field: "acoes",
      renderCell: (params) => (
        <div className="flex justify-center gap-2">
          <Button
            size="sm"
            onClick={() => editarFuncionario(params.row.id)}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white transition-all duration-200 transform hover:scale-105 border-0"
          >
            <Pencil className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            onClick={() => deletarFuncionario(params.row.id)}
            className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white transition-all duration-200 transform hover:scale-105 border-0"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ];

  const editarFuncionario = (id) => {
    router.push(`/admin/funcionario/editar/${id}`);
  };

  const deletarFuncionario = async (id) => {
    setShowDialog(true);
    setConfirmCallback(() => async () => {
      setLoading(true);
      const funcionarioService = new FuncionarioService();
      const deletar = await funcionarioService.DeletarFuncionario(id);

      if (!deletar) {
        setShowDialog(false);
        setLoading(false);
        return toast({
          title: "Erro",
          description: "Erro ao deletar funcionário",
          variant: "destructive",
        });
      }

      toast({
        title: "Sucesso",
        description: "Funcionário deletado com sucesso",
      });

      setShowDialog(false);
      setLoading(false);
      fetchFuncionarios(searchParams);
    });
  };

  // Função para aplicar filtros
  const aplicarFiltros = () => {
    setLoading(true);
    const params = new URLSearchParams(searchParams);

    // Adicionar filtros aos parâmetros
    if (filtroNome) params.set("nome", filtroNome);
    else params.delete("nome");

    if (filtroCpf) {
      // Remover formatação do CPF antes de enviar
      const cpfSemFormatacao = filtroCpf.replace(/\D/g, "");
      params.set("cpf", cpfSemFormatacao);
    } else {
      params.delete("cpf");
    }

    if (filtroTelefone) {
      // Remover formatação do telefone antes de enviar
      const telefoneSemFormatacao = filtroTelefone.replace(/\D/g, "");
      params.set("telefone", telefoneSemFormatacao);
    } else {
      params.delete("telefone");
    }

    if (filtroEmail) params.set("email", filtroEmail);
    else params.delete("email");

    // Resetar para página 1 ao filtrar
    params.set("page", 1);

    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  // Função para limpar filtros
  const limparFiltros = () => {
    setFiltroNome("");
    setFiltroCpf("");
    setFiltroTelefone("");
    setFiltroEmail("");

    const params = new URLSearchParams();
    params.set("page", 1);
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  const fetchFuncionarios = async (params) => {
    setLoading(true);
    const funcionarioService = new FuncionarioService();

    // Converter o objeto URLSearchParams para string de consulta
    const queryString = params.toString();

    const funcionarios = await funcionarioService.listarFuncionarios(
      queryString
    );
    if (!funcionarios) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao buscar funcionários",
        variant: "destructive",
      });
    }
    setFuncionarios(funcionarios.items);
    setTotalPage(funcionarios.totalPages);
    setLoading(false);
  };

  // Carregar filtros da URL quando a página é carregada
  useEffect(() => {
    const nome = searchParams.get("nome");
    const cpf = searchParams.get("cpf");
    const telefone = searchParams.get("telefone");
    const email = searchParams.get("email");

    if (nome) setFiltroNome(nome);
    if (cpf) setFiltroCpf(maskCpf(cpf));
    if (telefone) setFiltroTelefone(maskPhone(telefone));
    if (email) setFiltroEmail(email);
  }, []);

  useEffect(() => {
    fetchFuncionarios(searchParams);
  }, [searchParams]);

  const currentDate = new Date().toLocaleDateString("pt-BR", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <div className="min-h-screen p-4">
      <AlertDialogUI
        title="Confirmação de exclusão"
        description="Deseja realmente deletar este funcionário?"
        showDialog={showDialog}
        setShowDialog={setShowDialog}
        onConfirm={confirmCallback}
      />

      {/* Header moderno */}
      <div className="mb-4 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Gestão de Funcionários
              </h1>
              <p className="text-gray-600 text-sm">
                {currentDate} • Gerencie seus funcionários cadastrados
              </p>
            </div>
            <div className="mt-2 lg:mt-0 flex items-center space-x-4">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-lg flex items-center space-x-2 text-sm">
                <Users className="h-4 w-4" />
                <span>{funcionarios.length} Funcionários</span>
              </div>
              <Link href="/admin/funcionario/novo">
                <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center space-x-2">
                  <Plus className="h-4 w-4" />
                  <span>Novo Funcionário</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Seção de filtros moderna */}
      <div className="mb-4 animate-slideInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <div className="p-1 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg">
                <Filter className="h-5 w-5 text-white" />
              </div>
              <div>
                <h2 className="text-base font-bold text-gray-900">
                  Filtros Avançados
                </h2>
                <p className="text-gray-600 text-xs">
                  Refine sua pesquisa de funcionários
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div className="group">
              <Input
                placeholder="Filtrar por nome"
                value={filtroNome}
                onChange={(e) => setFiltroNome(e.target.value)}
                className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
              />
            </div>
            <div className="group">
              <Input
                placeholder="Filtrar por CPF"
                value={filtroCpf}
                onChange={(e) => setFiltroCpf(maskCpf(e.target.value))}
                className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
              />
            </div>
            <div className="group">
              <Input
                placeholder="Filtrar por telefone"
                value={filtroTelefone}
                onChange={(e) => setFiltroTelefone(maskPhone(e.target.value))}
                className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
              />
            </div>
            <div className="group">
              <Input
                placeholder="Filtrar por e-mail"
                value={filtroEmail}
                onChange={(e) => setFiltroEmail(e.target.value)}
                className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
              />
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-2">
            <Button
              variant="outline"
              onClick={limparFiltros}
              className="border-gray-300 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2 h-9"
            >
              <X className="w-4 h-4" />
              <span>Limpar Filtros</span>
            </Button>
            <Button
              onClick={aplicarFiltros}
              className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white transition-all duration-200 transform hover:scale-105 flex items-center space-x-2 h-9"
            >
              <Search className="w-4 h-4" />
              <span>Aplicar Filtros</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="animate-fadeInUp">
        {loading ? (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
            <div className="flex flex-col justify-center items-center space-y-3">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                <Spinner className="text-white" size="medium" />
              </div>
              <div className="text-center">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  Carregando funcionários...
                </h3>
                <p className="text-gray-600 text-sm">
                  Aguarde enquanto buscamos os dados
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">
                      Lista de Funcionários
                    </h3>
                    <p className="text-gray-600 text-xs">
                      {funcionarios.length} funcionário
                      {funcionarios.length !== 1 ? "s" : ""} encontrado
                      {funcionarios.length !== 1 ? "s" : ""}
                    </p>
                  </div>
                </div>

                {/* Botões de alternância de visualização */}
                <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
                  <Button
                    size="sm"
                    variant={viewMode === "cards" ? "default" : "ghost"}
                    onClick={() => setViewMode("cards")}
                    className={`transition-all duration-200 h-8 ${
                      viewMode === "cards"
                        ? "bg-gradient-to-r from-emerald-500 to-teal-500 text-white shadow-lg"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <Grid3X3 className="h-3 w-3 mr-1" />
                    Cards
                  </Button>
                  <Button
                    size="sm"
                    variant={viewMode === "table" ? "default" : "ghost"}
                    onClick={() => setViewMode("table")}
                    className={`transition-all duration-200 h-8 ${
                      viewMode === "table"
                        ? "bg-white shadow-sm text-gray-900"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <List className="h-3 w-3 mr-1" />
                    Tabela
                  </Button>
                </div>
              </div>
            </div>

            {funcionarios.length === 0 ? (
              <div className="p-8 text-center">
                <div className="flex flex-col items-center space-y-3">
                  <div className="p-3 bg-gray-100 rounded-full">
                    <Users className="h-8 w-8 text-gray-400" />
                  </div>
                  <div>
                    <h3 className="text-base font-medium text-gray-900 mb-1">
                      Nenhum funcionário encontrado
                    </h3>
                    <p className="text-gray-600 text-sm">
                      Não há funcionários cadastrados ou que correspondam aos
                      filtros aplicados.
                    </p>
                  </div>
                  <Link href="/admin/funcionario/novo">
                    <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white">
                      <Plus className="h-4 w-4 mr-2" />
                      Cadastrar Primeiro Funcionário
                    </Button>
                  </Link>
                </div>
              </div>
            ) : (
              <div className="p-4">
                {viewMode === "cards" ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {funcionarios.map((funcionario, index) => (
                      <div
                        key={funcionario.id}
                        className="bg-white/60 backdrop-blur-sm rounded-lg border border-gray-200 p-4 hover:shadow-lg transition-all duration-200 animate-slideInUp"
                        style={{ animationDelay: `${index * 0.1}s` }}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <div className="p-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg">
                              <Users className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900 text-sm">
                                {funcionario.nomeCompleto}
                              </h4>
                              <p className="text-gray-600 text-xs">
                                {funcionario.email}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2 mb-4">
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-gray-500">CPF:</span>
                            <span className="font-medium text-gray-900">
                              {maskCpf(funcionario.cpf)}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-gray-500">Telefone:</span>
                            <span className="font-medium text-gray-900">
                              {maskPhone(funcionario.telefone)}
                            </span>
                          </div>
                        </div>

                        <div className="flex justify-end space-x-2">
                          <Button
                            size="sm"
                            onClick={() => editarFuncionario(funcionario.id)}
                            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white transition-all duration-200 transform hover:scale-105 border-0 h-8"
                          >
                            <Pencil className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => deletarFuncionario(funcionario.id)}
                            className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white transition-all duration-200 transform hover:scale-105 border-0 h-8"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Tables data={funcionarios} columns={columns} />
                )}
              </div>
            )}

            {totalPage > 1 && (
              <div className="p-4 border-t border-gray-200 bg-gray-50/50">
                <div className="flex justify-end items-center">
                  <PaginationUI totalPage={totalPage} />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
