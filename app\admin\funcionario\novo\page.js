"use client";
import Back from "@/components/back";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { funcionarioSchema } from "@/lib/schema/zod";
import { useState } from "react";
import ClienteService from "@/lib/services/clienteService";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { Spinner } from "@/components/ui/spinner";
import FuncionarioForm from "@/components/forms/funcionarioForm";
import FuncionarioService from "@/lib/services/funcionarioService";


export default function Cliente() {
    const { register, handleSubmit, setValue, control, formState: { errors } } = useForm({
        resolver: zodResolver(funcionarioSchema),
    });

    const [loading, setLoading] = useState(false);
    const { toast } = useToast();
    const router = useRouter();

    const onSubmit = async (data) => {
        setLoading(true);
        const funcionarioService = new FuncionarioService();
        data.cpf = data.cpf.replace(/\D/g, "");
        data.telefone = data.telefone.replace(/\D/g, "");
        const funcionario = await funcionarioService.CadastrarFuncionario(data);
        if (!funcionario[0].status) {
            setLoading(false);
            return toast({
                title: "Erro",
                description: funcionario[0].message,
                variant: "destructive"
            })
        }
        toast({
            title: "Sucesso",
            description: funcionario[0].message,
        });
        router.push("/admin/funcionario");
        setLoading(false);
    }

    return (
        <div className="container max-w-6xl justify-center items-center mx-auto p-6">
            <div className="mb-8">
                <div className="flex items-center gap-2">
                    <Back icon={<ArrowLeft className="h-4 w-4" />} text="Voltar" href="/admin/cliente" />
                </div>
            </div>
            <div className="mb-8 flex justify-between items-center">
                <div>
                    <h1 className="mt-4 text-3xl font-bold">
                        Novo Funcionário
                    </h1>
                    <p className="text-muted-foreground">
                        Preencha os campos abaixo para cadastrar um funcionário.
                    </p>
                </div>
            </div>
            <div>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <FuncionarioForm control={control} register={register} errors={errors} setValue={setValue} />
                    <Button type="submit" className="mt-4 w-24" disabled={loading}>
                        {loading ? <Spinner /> : "Cadastrar"}
                    </Button>
                </form>
            </div>
        </div>
    );
}