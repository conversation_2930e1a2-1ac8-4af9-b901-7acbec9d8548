import httpClient from "../http/httpClient";

export default class VisualizarMapaService {
    async GerarColeta(data) {
        const response = await httpClient.post("utils/generate-hexagons", data);
        if (response.status !== 200) {
            return false;
        }
        const d = await response.json();
        return d;
    }

    async CadastrarColeta(data) {
        const response = await httpClient.post("visualizar-mapa/salvar", data, true);
        if (response.status !== 200) {
            return false;
        }
        return true;
    }

    async ListarColetas(page) {
        const response = await httpClient.get(`visualizar-mapa?${page}`, true);
        if (response.status !== 200) {
            return false;
        }
        const data = await response.json();
        return data;
    }

    async DeletarMapa(id) {
        const response = await httpClient.delete(`visualizar-mapa/${id}`, null, true);
        if (response.status !== 200) {
            return false;
        }
        return true;
    }

    async ObterColeta(id) {
        const response = await httpClient.get(`visualizar-mapa/${id}`, true);
        if (response.status !== 200) {
            return false;
        }
        const data = await response.json();
        return data;
    }
}