"use client";
import { ErrorBoundary } from "@/components/base/ErrorBoundary";
import { FullPageLoader } from "@/components/base/LoadingSpinner";
import { PageLayout } from "@/components/layout/PageLayout";
import { CollectionInfoCard } from "@/components/map/CollectionInfoCard";
import { ColorConfigurationModal } from "@/components/map/ColorConfigurationModal";
import { MapVisualizationCard } from "@/components/map/MapVisualizationCard";
import { useMapVisualization } from "@/hooks/useMapVisualization";
import { use } from "react";

const MapVisualizationPage = ({ params }) => {
  const { id } = use(params);
  const {
    geoJsonData,
    data,
    minerais,
    selectedMineral,
    mineralColorConfig,
    loading,
    error,
    modals,
    getMineralStats,
    setSelectedMineral,
    openModal,
    closeModal,
    saveColorConfiguration,
  } = useMapVisualization(id);

  const handleGeneratePDF = () => {
    // TODO: Implement PDF generation
    console.log("Generate PDF for collection:", id);
  };

  if (loading) {
    return <FullPageLoader message="Carregando dados da visualização..." />;
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive mb-4">
            Erro ao carregar dados
          </h1>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <PageLayout>
        <PageLayout.Header
          title="Resultado da Análise"
          description="Visualize os dados coletados e análises realizadas no mapa interativo."
          backButton={{
            onClick: () => window.history.back(),
            text: "Voltar",
          }}
        />

        <PageLayout.Content maxWidth="6xl">
          <PageLayout.Grid columns={1} gap={6}>
            <CollectionInfoCard
              geoJsonData={geoJsonData}
              onGeneratePDF={handleGeneratePDF}
            />

            <MapVisualizationCard
              geoJsonData={geoJsonData}
              selectedMineral={selectedMineral}
              minerais={minerais}
              data={data}
              mineralColorConfig={mineralColorConfig}
              getMineralStats={getMineralStats}
              onMineralChange={setSelectedMineral}
              onConfigureColors={() => openModal("colorConfig")}
              loading={loading}
            />
          </PageLayout.Grid>
        </PageLayout.Content>

        <ColorConfigurationModal
          open={modals.colorConfig}
          onOpenChange={() => closeModal("colorConfig")}
          selectedMineral={selectedMineral}
          onSave={saveColorConfiguration}
        />
      </PageLayout>
    </ErrorBoundary>
  );
};

export default MapVisualizationPage;
