"use client";
import { useEffect, useState } from "react";
import Back from "@/components/back";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";


import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Spinner } from "@/components/ui/spinner";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowLeft, Settings, BarChart3 } from "lucide-react";
import RelatorioService from "@/lib/services/relatorioService";
import { useToast } from "@/hooks/use-toast";
import MapaContainer from "@/components/forms/MapaContainer";
import { use } from "react";
import VisualizarMapaService from "@/lib/services/visualizarMapa";
import ConfiguracaoPersonalizadaService from "@/lib/services/configuracaoPersonalizadaService";
import Histogram from "@/components/charts/Histogram";
import HistogramControls from "@/components/charts/HistogramControls";

export default function VisualizarMapa({ params }) {
    const { id } = use(params);
    const [isDataModalOpen, setIsDataModalOpen] = useState(false);
    const [data, setData] = useState([]);
    const [columns, setColumns] = useState([]);
    const [minerais, setMinerais] = useState([]);
    const [selectedMineral, setSelectedMineral] = useState("");
    const [isColorConfigOpen, setIsColorConfigOpen] = useState(false);
    const [isHistogramOpen, setIsHistogramOpen] = useState(false);
    const [histogramBins, setHistogramBins] = useState(10);
    const [histogramColor, setHistogramColor] = useState('#3b82f6');
    const [mineralColorConfig, setMineralColorConfig] = useState([]);
    const [geoJsonData, setGeoJsonData] = useState({});
    const [loading, setLoading] = useState(true);
    const { toast } = useToast();

    const fecthGeoJson = async (id) => {
        try {
            const visualizarmapa = new VisualizarMapaService();
            const relatorio = await visualizarmapa.ObterColeta(id);

            // Verificar se relatorio e geojson existem
            if (!relatorio || !relatorio.geojson || !relatorio.geojson.pontos) {
                console.error("Dados do mapa incompletos:", relatorio);
                throw new Error("Dados do mapa incompletos ou inválidos");
            }

            try {
                // Tentar fazer o parse do JSON
                const pontosString = relatorio.geojson.pontos;

                // Verificar se o JSON está completo (verificação básica)
                if (!pontosString.endsWith('}') && !pontosString.endsWith(']')) {
                    console.warn("JSON possivelmente truncado:", pontosString.substring(pontosString.length - 100));
                }

                // Tentar fazer o parse mesmo assim
                relatorio.geojson.pontos = JSON.parse(pontosString);

                // Verificar se o objeto parseado tem a estrutura esperada
                if (!relatorio.geojson.pontos.type || !relatorio.geojson.pontos.features) {
                    console.error("Estrutura do GeoJSON inválida:", relatorio.geojson.pontos);
                    throw new Error("Estrutura do GeoJSON inválida");
                }

                setGeoJsonData(relatorio);
            } catch (jsonError) {
                console.error("Erro ao fazer parse do JSON:", jsonError);
                console.error("Primeiros 100 caracteres:", relatorio.geojson.pontos.substring(0, 100));
                console.error("Últimos 100 caracteres:", relatorio.geojson.pontos.substring(relatorio.geojson.pontos.length - 100));

                // Criar um GeoJSON vazio para evitar erros no mapa
                relatorio.geojson.pontos = {
                    type: "FeatureCollection",
                    features: []
                };

                setGeoJsonData(relatorio);

                toast({
                    title: "Aviso",
                    description: "Dados do mapa estão corrompidos. Algumas informações podem não ser exibidas corretamente.",
                    variant: "warning"
                });
            }
        } catch (error) {
            console.error("Erro ao carregar dados do mapa:", error);
            toast({
                title: "Erro",
                description: "Erro ao carregar dados do mapa: " + (error.message || "Erro desconhecido"),
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    }

    const fecthRelatorio = async (id) => {
        try {
            const relatorioService = new RelatorioService();
            const relatorio = await relatorioService.ObterRelatorio(id);

            if (relatorio?.jsonRelatorio) {
                try {
                    // Tentar fazer o parse do JSON
                    const jsonString = relatorio.jsonRelatorio;

                    // Verificar se o JSON está completo (verificação básica)
                    if (!jsonString.endsWith('}') && !jsonString.endsWith(']') && !jsonString.endsWith('"}') && !jsonString.endsWith('"]')) {
                        console.warn("JSON do relatório possivelmente truncado:", jsonString.substring(jsonString.length - 100));
                    }

                    const parsedData = JSON.parse(jsonString);

                    // Verificar se o objeto parseado tem a estrutura esperada
                    if (!Array.isArray(parsedData) || parsedData.length === 0) {
                        console.error("Estrutura do relatório inválida:", parsedData);
                        throw new Error("Estrutura do relatório inválida");
                    }

                    setData(parsedData);

                    const headers = Object.keys(parsedData[0]);
                    const possibleMinerals = headers.filter(header => typeof parsedData[0][header] === 'number');

                    const columnHeaders = headers.map(key => ({
                        accessorKey: key,
                        header: key,
                    }));

                    setColumns(columnHeaders);
                    setMinerais(possibleMinerals);
                    if (!selectedMineral && possibleMinerals.length > 0) {
                        setSelectedMineral(possibleMinerals[0]);
                    }
                } catch (jsonError) {
                    console.error("Erro ao fazer parse do JSON do relatório:", jsonError);
                    console.error("Primeiros 100 caracteres:", relatorio.jsonRelatorio.substring(0, 100));
                    console.error("Últimos 100 caracteres:", relatorio.jsonRelatorio.substring(relatorio.jsonRelatorio.length - 100));

                    toast({
                        title: "Aviso",
                        description: "Dados do relatório estão corrompidos. Algumas informações podem não ser exibidas corretamente.",
                        variant: "warning"
                    });

                    // Definir dados vazios para evitar erros
                    setData([]);
                    setColumns([]);
                    setMinerais([]);
                }
            } else {
                console.warn("Relatório não contém dados JSON:", relatorio);
            }
        } catch (error) {
            console.error("Erro ao carregar relatório:", error);
            toast({
                title: "Erro",
                description: "Erro ao carregar dados do relatório: " + (error.message || "Erro desconhecido"),
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };


    // Função para carregar configurações de cores do banco de dados
    const fetchColorConfigurations = async () => {
        try {
            const configService = new ConfiguracaoPersonalizadaService();
            const configs = await configService.listarConfiguracoes();

            // Filtrar configurações globais
            const globalConfigs = configs.filter(config => config.nome === "global");

            if (globalConfigs.length > 0) {
                // Converter para o formato usado pelo componente
                const formattedConfigs = globalConfigs.map(config => ({
                    minimo: config.limiteInferior.toString(),
                    maximo: config.limiteSuperior.toString(),
                    cor: config.corHex
                }));

                // Atualizar o estado com as configurações carregadas
                setMineralColorConfig(formattedConfigs);

                console.log("Configurações de cores carregadas:", formattedConfigs);
            } else {
                console.log("Nenhuma configuração de cores global encontrada");
            }
        } catch (error) {
            console.error("Erro ao carregar configurações de cores:", error);
        }
    };

    useEffect(() => {
        fecthGeoJson(id);
        fecthRelatorio(id);

        // Carregar configurações de cores
        fetchColorConfigurations();
    }, [id]);




    return (
        <div className="container mx-auto p-6 space-y-6">
            <div className="mb-8">
                <div className="flex items-center gap-2">
                    <Back
                        icon={<ArrowLeft className="h-4 w-4" />}
                        text="Voltar"
                        href="/admin/visualizarmapa"
                    />
                </div>
            </div>
            {/* Header */}
            <div className="flex justify-between items-center">
                <h1 className="text-3xl font-bold">Resultado da Analise</h1>
            </div>

            {/* Conteúdo Principal */}
            <div className="grid gap-6">
                {/* Card de Informações */}
                <Card className="mb-4">
                    <CardHeader>
                        <CardTitle>Informações da Coleta</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-muted-foreground">
                                    Responsável
                                </p>
                                <p className="font-medium">
                                    {geoJsonData?.usuarioResp?.nomeCompleto}
                                </p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-muted-foreground">
                                    Tipo de Coleta
                                </p>
                                <p className="font-medium">{geoJsonData?.tipoColeta}</p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-muted-foreground">
                                    Tipo de Análise
                                </p>
                                <div className="flex flex-wrap gap-1">
                                    {Array.isArray(geoJsonData?.tipoAnalise) ? (
                                        geoJsonData.tipoAnalise.map((tipo, index) => (
                                            <Badge
                                                key={index}
                                                variant="secondary"
                                                className="font-medium"
                                            >
                                                {tipo}
                                            </Badge>
                                        ))
                                    ) : geoJsonData?.tipoAnalise ? (
                                        <Badge variant="secondary" className="font-medium">
                                            {geoJsonData.tipoAnalise}
                                        </Badge>
                                    ) : (
                                        <span className="text-gray-400">Nenhum</span>
                                    )}
                                </div>
                            </div>
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-muted-foreground">
                                    Área do Talhão
                                </p>
                                <p className="font-medium">{geoJsonData?.talhao?.area} ha</p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-muted-foreground">
                                    Nome do Talhão
                                </p>
                                <p className="font-medium">{geoJsonData?.talhao?.nome}</p>
                            </div>
                            {geoJsonData?.observacao && (
                                <div className="space-y-1">
                                    <p className="text-sm font-medium text-muted-foreground">
                                        Observação
                                    </p>
                                    <p className="font-medium">{geoJsonData.observacao}</p>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Card do Mapa */}
                <Card>
                    {minerais.length > 0 && (
                        <CardHeader>
                            <CardTitle>Selecione um Atributo</CardTitle>
                            <CardDescription>
                                Escolha um atributo para visualizar no mapa
                            </CardDescription>
                            <div className="space-y-4">
                                <div className="flex gap-2">
                                    <div className="flex-1">
                                        <Select onValueChange={setSelectedMineral} value={selectedMineral}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione um mineral..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {minerais.map((mineral, index) => (
                                                    <SelectItem key={index} value={mineral}>
                                                        {mineral}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    {selectedMineral && (
                                        <>
                                            <Button
                                                variant="outline"
                                                onClick={() => setIsColorConfigOpen(true)}
                                                className="flex items-center gap-1"
                                            >
                                                <Settings className="h-4 w-4" />
                                                Configuração de Cores
                                            </Button>
                                            <Button
                                                variant="outline"
                                                onClick={() => setIsHistogramOpen(true)}
                                                className="flex items-center gap-1"
                                            >
                                                <BarChart3 className="h-4 w-4" />
                                                Histograma
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </div>
                        </CardHeader>
                    )}
                    {loading ? (
                        <div className="flex items-center justify-center h-[400px]">
                            <Spinner className="text-black" message="Carregando mapa..." />
                        </div>
                    ) : (
                        <CardContent className="p-6">
                            {geoJsonData?.geojson?.pontos?.features ? (
                                <MapaContainer
                                    initialValue={{
                                        type: "FeatureCollection",
                                        features: Array.isArray(geoJsonData.geojson.pontos.features)
                                            ? geoJsonData.geojson.pontos.features.filter(f => f.properties?.type === "hexagon")
                                            : []
                                    }}
                                    selectedMineral={selectedMineral}
                                    data={data}
                                    mineralColorConfig={mineralColorConfig}
                                    mapMode="view"
                                />
                            ) : (
                                <div className="flex flex-col items-center justify-center h-[400px] bg-muted rounded-md p-6">
                                    <p className="text-lg font-medium text-muted-foreground mb-4">Não foi possível carregar o mapa</p>
                                    <p className="text-sm text-muted-foreground text-center max-w-md">
                                        Os dados do mapa estão corrompidos ou incompletos. Tente recarregar a página ou entre em contato com o suporte.
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    )}
                </Card>
            </div>



            {/* Modal de Visualização dos Dados */}
            <Dialog open={isDataModalOpen} onOpenChange={setIsDataModalOpen}>
                <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-hidden">
                    <DialogHeader>
                        <DialogTitle>Visualização dos Dados</DialogTitle>
                        <DialogDescription>
                            Dados do relatório
                        </DialogDescription>
                    </DialogHeader>
                    <div className="overflow-hidden">
                        <Tabs defaultValue="preview">
                            <TabsList>
                                <TabsTrigger value="preview">Preview</TabsTrigger>
                                <TabsTrigger value="complete">Dados Completos</TabsTrigger>
                            </TabsList>
                            <TabsContent value="preview" className="overflow-hidden">
                                <ScrollArea className="h-[400px] rounded-md border">
                                    <div className="overflow-x-auto">
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    {columns.map((column) => (
                                                        <TableHead key={column.accessorKey}>
                                                            {column.header}
                                                        </TableHead>
                                                    ))}
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {data.slice(0, 10).map((row, rowIndex) => (
                                                    <TableRow key={rowIndex}>
                                                        {columns.map((column) => (
                                                            <TableCell key={column.accessorKey}>
                                                                {row[column.accessorKey]}
                                                            </TableCell>
                                                        ))}
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </div>
                                </ScrollArea>
                            </TabsContent>
                            <TabsContent value="complete">
                                <ScrollArea className="h-[600px] w-full overflow-y-auto rounded-md border">
                                    <div className="w-full overflow-x-auto">
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    {columns.map((column) => (
                                                        <TableHead key={column.accessorKey}>
                                                            {column.header}
                                                        </TableHead>
                                                    ))}
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {data.map((row, rowIndex) => (
                                                    <TableRow key={rowIndex}>
                                                        {columns.map((column) => (
                                                            <TableCell key={column.accessorKey}>
                                                                {row[column.accessorKey]}
                                                            </TableCell>
                                                        ))}
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </div>
                                </ScrollArea>
                            </TabsContent>

                        </Tabs>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Modal de Visualização de Configuração de Cores */}
            <Dialog open={isColorConfigOpen} onOpenChange={setIsColorConfigOpen}>
                <DialogContent className="max-w-[500px]">
                    <DialogHeader>
                        <DialogTitle>Configuração de Cores Global</DialogTitle>
                        <DialogDescription>
                            Visualização das configurações de cores aplicadas a todos os minerais.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        {mineralColorConfig.length === 0 ? (
                            <div className="text-center p-6 bg-muted rounded-md">
                                <p className="text-muted-foreground">Nenhuma configuração de cores definida.</p>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {mineralColorConfig.map((config, index) => (
                                    <div key={index} className="border p-4 rounded-md shadow-sm bg-muted">
                                        <div className="grid grid-cols-3 gap-4">
                                            <div className="flex flex-col">
                                                <label className="mb-2 font-medium">De</label>
                                                <div className="p-2 bg-background rounded border">
                                                    {config.minimo}
                                                </div>
                                            </div>
                                            <div className="flex flex-col">
                                                <label className="mb-2 font-medium">Até</label>
                                                <div className="p-2 bg-background rounded border">
                                                    {config.maximo}
                                                </div>
                                            </div>
                                            <div className="flex flex-col">
                                                <label className="mb-2 font-medium">Cor</label>
                                                <div
                                                    className="h-10 rounded-md border"
                                                    style={{ backgroundColor: config.cor }}
                                                    title={config.cor}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                        <DialogFooter>
                            <Button onClick={() => setIsColorConfigOpen(false)}>
                                Fechar
                            </Button>
                        </DialogFooter>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Modal do Histograma */}
            <Dialog open={isHistogramOpen} onOpenChange={setIsHistogramOpen}>
                <DialogContent className="max-w-[1000px] max-h-[90vh]">
                    <DialogHeader>
                        <DialogTitle>Histograma - {selectedMineral}</DialogTitle>
                        <DialogDescription>
                            Distribuição de frequência dos valores do atributo selecionado
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                        {/* Controles do Histograma */}
                        <div className="lg:col-span-1">
                            <HistogramControls
                                bins={histogramBins}
                                setBins={setHistogramBins}
                                selectedAttribute={selectedMineral}
                                setSelectedAttribute={setSelectedMineral}
                                availableAttributes={minerais}
                                color={histogramColor}
                                setColor={setHistogramColor}
                            />
                        </div>

                        {/* Histograma */}
                        <div className="lg:col-span-2">
                            <Histogram
                                data={data}
                                selectedAttribute={selectedMineral}
                                title={`Distribuição de ${selectedMineral}`}
                                bins={histogramBins}
                                color={histogramColor}
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button onClick={() => setIsHistogramOpen(false)}>
                            Fechar
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}
import Back from "@/components/back";
import { Spinner } from "@/components/ui/spinner";
import TalhaoService from "@/lib/services/talhaoService";
import { ArrowLeft } from "lucide-react";
import { use, useEffect, useState } from "react";
import VisualizarMapaFormContainer from "@/components/forms/visualizarMapaFormContainer";
import { useToast } from "@/hooks/use-toast";

export default function VisualizarMapa({ params }) {
    const { id } = use(params);
    const [loading, setLoading] = useState(false);
    const [talhao, setTalhao] = useState({});

    const fetchTalhao = async (id) => {
        setLoading(true);
        const talhaoService = new TalhaoService();
        const talhao = await talhaoService.ObterTalhao(id);
        if (!talhao) {
            setLoading(false);
            return toast({
                title: "Erro",
                description: "Erro ao buscar talhão",
                variant: "destructive"
            });
        }
        setLoading(false);
        setTalhao(talhao);
    }

    useEffect(() => {
        fetchTalhao(id);
    }, [id]);

    return (
        <div className="container max-w-6xl justify-center items-center mx-auto p-6">
        {loading && (
            <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
                <Spinner message="carregando..." className="text-white w-10 h-10" />
            </div>
        )}
        <div className="mb-8">
            <div className="flex items-center gap-2">
                <Back icon={<ArrowLeft className="h-4 w-4" />} text="Voltar" href="/admin/visualizarmapa" />
            </div>
        </div>
        <div className="mb-8 flex justify-between items-center">
            <div>
                <h1 className="mt-4 text-3xl font-bold">
                    Visualizar Mapa
                </h1>
                <p className="text-muted-foreground">
                    Preencha os campos abaixo para cadastrar um mapa.
                </p>
            </div>
        </div>
        <VisualizarMapaFormContainer initialValue={talhao} />
    </div>
    );
}
