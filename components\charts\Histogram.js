"use client";
import { useMemo } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

const DEFAULT_CONFIG = {
  bins: 10,
  color: "#00E0FF",
  height: 300,
};

const HistogramChart = ({ data, selectedAttribute, config = {} }) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  const processedData = useMemo(() => {
    return processHistogramData(data, selectedAttribute, finalConfig.bins);
  }, [data, selectedAttribute, finalConfig.bins]);

  if (!hasValidData(data, selectedAttribute)) {
    return <EmptyHistogramState selectedAttribute={selectedAttribute} />;
  }

  return (
    <ResponsiveContainer width="100%" height={finalConfig.height}>
      <BarChart
        data={processedData}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
        <XAxis
          dataKey="range"
          tick={{ fontSize: 12 }}
          angle={-45}
          textAnchor="end"
          height={60}
        />
        <YAxis tick={{ fontSize: 12 }} />
        <Tooltip content={<CustomTooltip />} />
        <Bar dataKey="count" fill={finalConfig.color} radius={[2, 2, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  );
};

// Helper functions
const hasValidData = (data, selectedAttribute) => {
  return data && selectedAttribute && data.length > 0;
};

const extractNumericValues = (data, attribute) => {
  return data
    .map((row) => parseFloat(row[attribute]))
    .filter((value) => !isNaN(value));
};

const calculateBinRanges = (min, max, binCount) => {
  const binWidth = (max - min) / binCount;
  const ranges = [];

  for (let i = 0; i < binCount; i++) {
    const start = min + i * binWidth;
    const end = min + (i + 1) * binWidth;
    ranges.push({
      start,
      end,
      label: `${start.toFixed(2)} - ${end.toFixed(2)}`,
    });
  }

  return { ranges, binWidth };
};

const distributeToBins = (values, min, binWidth, binCount) => {
  const counts = new Array(binCount).fill(0);

  values.forEach((value) => {
    let binIndex = Math.floor((value - min) / binWidth);
    binIndex = Math.max(0, Math.min(binIndex, binCount - 1));
    counts[binIndex]++;
  });

  return counts;
};

const processHistogramData = (data, selectedAttribute, bins) => {
  if (!hasValidData(data, selectedAttribute)) return [];

  const values = extractNumericValues(data, selectedAttribute);
  if (values.length === 0) return [];

  const min = Math.min(...values);
  const max = Math.max(...values);

  if (min === max) {
    return [
      {
        range: min.toFixed(2),
        count: values.length,
      },
    ];
  }

  const { ranges, binWidth } = calculateBinRanges(min, max, bins);
  const counts = distributeToBins(values, min, binWidth, bins);

  return ranges.map((range, index) => ({
    range: range.label,
    count: counts[index],
  }));
};

const CustomTooltip = ({ active, payload, label }) => {
  if (!active || !payload || !payload.length) return null;

  return (
    <div className="bg-background border rounded-md p-2 shadow-md">
      <p className="text-sm font-medium">{`Intervalo: ${label}`}</p>
      <p className="text-sm text-primary">
        {`Frequência: ${payload[0].value}`}
      </p>
    </div>
  );
};

const EmptyHistogramState = ({ selectedAttribute }) => (
  <div className="flex items-center justify-center h-48 bg-muted rounded-md">
    <p className="text-muted-foreground">
      {!selectedAttribute ? "Selecione um atributo" : "Nenhum dado disponível"}
    </p>
  </div>
);

const Histogram = ({
  data,
  selectedAttribute,
  title,
  bins = 10,
  color = "#3b82f6",
}) => {
  const config = { bins, color, height: 300 };
  const statistics = useHistogramStatistics(data, selectedAttribute);

  if (!hasValidData(data, selectedAttribute)) {
    return <EmptyHistogramState selectedAttribute={selectedAttribute} />;
  }

  return (
    <div className="space-y-4">
      <HistogramHeader title={title} selectedAttribute={selectedAttribute} />
      <HistogramStats statistics={statistics} />
      <HistogramChart
        data={data}
        selectedAttribute={selectedAttribute}
        config={config}
      />
    </div>
  );
};

const HistogramHeader = ({ title, selectedAttribute }) => (
  <div className="mb-2">
    <h4 className="text-sm font-medium">
      {title || `Distribuição de ${selectedAttribute}`}
    </h4>
  </div>
);

const HistogramStats = ({ statistics }) => {
  if (!statistics) return null;

  const statItems = [
    { label: "Contagem", value: statistics.count },
    { label: "Média", value: statistics.mean },
    { label: "Mediana", value: statistics.median },
    { label: "Mínimo", value: statistics.min },
    { label: "Máximo", value: statistics.max },
    { label: "Desvio Padrão", value: statistics.std },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 text-sm">
      {statItems.map((item, index) => (
        <StatCard key={index} label={item.label} value={item.value} />
      ))}
    </div>
  );
};

const StatCard = ({ label, value }) => (
  <div className="bg-muted p-2 rounded text-center">
    <div className="font-medium">{label}</div>
    <div className="text-muted-foreground">{value}</div>
  </div>
);

const useHistogramStatistics = (data, selectedAttribute) => {
  return useMemo(() => {
    if (!hasValidData(data, selectedAttribute)) return null;

    const values = extractNumericValues(data, selectedAttribute);
    if (values.length === 0) return null;

    return calculateStatistics(values);
  }, [data, selectedAttribute]);
};

const calculateStatistics = (values) => {
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const sortedValues = [...values].sort((a, b) => a - b);
  const median = calculateMedian(sortedValues);
  const min = Math.min(...values);
  const max = Math.max(...values);
  const std = calculateStandardDeviation(values, mean);

  return {
    count: values.length,
    mean: mean.toFixed(2),
    median: median.toFixed(2),
    min: min.toFixed(2),
    max: max.toFixed(2),
    std: std.toFixed(2),
  };
};

const calculateMedian = (sortedValues) => {
  const length = sortedValues.length;
  return length % 2 === 0
    ? (sortedValues[length / 2 - 1] + sortedValues[length / 2]) / 2
    : sortedValues[Math.floor(length / 2)];
};

const calculateStandardDeviation = (values, mean) => {
  const variance =
    values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) /
    values.length;
  return Math.sqrt(variance);
};

export default Histogram;
