import React, { useMemo } from 'react';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend
);

const Histogram = ({ data, selectedAttribute, title, bins = 10, color = '#3b82f6' }) => {
    const histogramData = useMemo(() => {
        if (!data || !selectedAttribute || data.length === 0) {
            return {
                labels: [],
                datasets: [{
                    label: 'Frequência',
                    data: [],
                    backgroundColor: color,
                    borderColor: color,
                    borderWidth: 1,
                }]
            };
        }

        // Extrair valores do atributo selecionado
        const values = data
            .map(row => parseFloat(row[selectedAttribute]))
            .filter(value => !isNaN(value));

        if (values.length === 0) {
            return {
                labels: [],
                datasets: [{
                    label: 'Frequência',
                    data: [],
                    backgroundColor: color,
                    borderColor: color,
                    borderWidth: 1,
                }]
            };
        }

        // Calcular min e max
        const min = Math.min(...values);
        const max = Math.max(...values);

        // Se todos os valores são iguais, criar um único bin
        if (min === max) {
            return {
                labels: [`${min.toFixed(2)}`],
                datasets: [{
                    label: 'Frequência',
                    data: [values.length],
                    backgroundColor: color + '80',
                    borderColor: color,
                    borderWidth: 1,
                }]
            };
        }

        // Calcular largura do bin
        const binWidth = (max - min) / bins;

        // Criar bins
        const binRanges = [];
        const binCounts = new Array(bins).fill(0);

        for (let i = 0; i < bins; i++) {
            const binStart = min + (i * binWidth);
            const binEnd = min + ((i + 1) * binWidth);
            binRanges.push(`${binStart.toFixed(2)} - ${binEnd.toFixed(2)}`);
        }

        // Contar valores em cada bin
        values.forEach(value => {
            let binIndex = Math.floor((value - min) / binWidth);
            // Garantir que o valor máximo caia no último bin
            if (binIndex >= bins) binIndex = bins - 1;
            if (binIndex < 0) binIndex = 0;
            binCounts[binIndex]++;
        });

        return {
            labels: binRanges,
            datasets: [{
                label: 'Frequência',
                data: binCounts,
                backgroundColor: color + '80', // Adiciona transparência
                borderColor: color,
                borderWidth: 1,
            }]
        };
    }, [data, selectedAttribute, bins, color]);

    const options = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: title || `Histograma - ${selectedAttribute}`,
            },
            tooltip: {
                callbacks: {
                    title: function (context) {
                        return `Intervalo: ${context[0].label}`;
                    },
                    label: function (context) {
                        return `Frequência: ${context.parsed.y}`;
                    }
                }
            }
        },
        scales: {
            x: {
                title: {
                    display: true,
                    text: selectedAttribute || 'Valores'
                },
                ticks: {
                    maxRotation: 45,
                    minRotation: 45
                }
            },
            y: {
                title: {
                    display: true,
                    text: 'Frequência'
                },
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    };

    // Calcular estatísticas básicas
    const statistics = useMemo(() => {
        if (!data || !selectedAttribute || data.length === 0) {
            return null;
        }

        const values = data
            .map(row => parseFloat(row[selectedAttribute]))
            .filter(value => !isNaN(value));

        if (values.length === 0) return null;

        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const sortedValues = [...values].sort((a, b) => a - b);
        const median = sortedValues.length % 2 === 0
            ? (sortedValues[sortedValues.length / 2 - 1] + sortedValues[sortedValues.length / 2]) / 2
            : sortedValues[Math.floor(sortedValues.length / 2)];
        const min = Math.min(...values);
        const max = Math.max(...values);
        const std = Math.sqrt(values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length);

        return {
            count: values.length,
            mean: mean.toFixed(2),
            median: median.toFixed(2),
            min: min.toFixed(2),
            max: max.toFixed(2),
            std: std.toFixed(2)
        };
    }, [data, selectedAttribute]);

    if (!data || !selectedAttribute || data.length === 0) {
        return (
            <div className="flex items-center justify-center h-64 bg-muted rounded-md">
                <p className="text-muted-foreground">
                    Selecione um atributo para visualizar o histograma
                </p>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* Estatísticas */}
            {statistics && (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 text-sm">
                    <div className="bg-muted p-2 rounded text-center">
                        <div className="font-medium">Contagem</div>
                        <div className="text-muted-foreground">{statistics.count}</div>
                    </div>
                    <div className="bg-muted p-2 rounded text-center">
                        <div className="font-medium">Média</div>
                        <div className="text-muted-foreground">{statistics.mean}</div>
                    </div>
                    <div className="bg-muted p-2 rounded text-center">
                        <div className="font-medium">Mediana</div>
                        <div className="text-muted-foreground">{statistics.median}</div>
                    </div>
                    <div className="bg-muted p-2 rounded text-center">
                        <div className="font-medium">Mínimo</div>
                        <div className="text-muted-foreground">{statistics.min}</div>
                    </div>
                    <div className="bg-muted p-2 rounded text-center">
                        <div className="font-medium">Máximo</div>
                        <div className="text-muted-foreground">{statistics.max}</div>
                    </div>
                    <div className="bg-muted p-2 rounded text-center">
                        <div className="font-medium">Desvio Padrão</div>
                        <div className="text-muted-foreground">{statistics.std}</div>
                    </div>
                </div>
            )}

            {/* Gráfico */}
            <div className="h-64 w-full">
                <Bar data={histogramData} options={options} />
            </div>
        </div>
    );
};

export default Histogram;
