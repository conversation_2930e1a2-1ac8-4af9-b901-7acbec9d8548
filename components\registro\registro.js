import { useToast } from "@/hooks/use-toast";
import { registroSchema } from "@/lib/schema/zod";
import AuthService from "@/lib/services/authService";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import RegistroForm from "../forms/registroForm";
import { Button } from "../ui/button";
import { Spinner } from "../ui/spinner";

export default function Registro({ setRegistro }) {
  const [loading, setLoading] = useState(false);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(registroSchema),
  });

  const { toast } = useToast();

  const data = async (data) => {
    setLoading(true);
    const authService = new AuthService();
    const registro = await authService.Registro(data);
    if (!registro) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao cadastrar usuário",
        variant: "destructive",
      });
    }
    toast({
      title: "Sucesso",
      description: "Usuário cadastrado com sucesso",
    });
    reset();
    setLoading(false);
    setRegistro(false);
  };

  return (
    <form onSubmit={handleSubmit(data)} className="space-y-6">
      <RegistroForm register={register} errors={errors} />

      <div className="space-y-4">
        <Button
          type="submit"
          disabled={loading}
          className="w-full py-3 px-4 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white font-medium rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          {loading ? (
            <div className="flex items-center justify-center space-x-2">
              <Spinner size="small" />
              <span>Cadastrando...</span>
            </div>
          ) : (
            "Criar Conta"
          )}
        </Button>

        <div className="text-center">
          <button
            type="button"
            onClick={() => setRegistro(false)}
            className="inline-flex items-center space-x-2 text-emerald-600 hover:text-emerald-700 font-medium transition-colors"
          >
            <ArrowLeft size={16} />
            <span>Voltar ao Login</span>
          </button>
        </div>
      </div>
    </form>
  );
}
