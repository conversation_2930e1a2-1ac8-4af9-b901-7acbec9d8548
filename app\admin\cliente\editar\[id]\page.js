"use client";
import Back from "@/components/back";
import ClienteForm from "@/components/forms/clienteForm";
import { Button } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import { clienteSchema } from "@/lib/schema/zod";
import ClienteService from "@/lib/services/clienteService";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft, Users } from "lucide-react";
import { useRouter } from "next/navigation";
import { use, useEffect, useState } from "react";
import { useForm } from "react-hook-form";

export default function Cliente({ params }) {
  const { id } = use(params);

  const [loading, setLoading] = useState(false);
  const [cliente, setCliente] = useState({});
  const { toast } = useToast();
  const router = useRouter();
  const {
    register,
    handleSubmit,
    setValue,
    control,
    formState: { errors },
  } = useForm({ resolver: zodResolver(clienteSchema) });

  const onSubmit = async (data) => {
    setLoading(true);
    data.cep = data.cep.replace(/\D/g, "");
    data.cpf = data.cpf.replace(/\D/g, "");
    data.telefone = data.telefone.replace(/\D/g, "");
    const obj = {
      id: id,
      ...data,
    };
    const clienteService = new ClienteService();
    const cliente = await clienteService.AtualizarCliente(obj);
    if (!cliente) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao atualizar cliente",
        variant: "destructive",
      });
    }
    toast({
      title: "Sucesso",
      description: "Cliente atualizado com sucesso",
    });
    router.push("/admin/cliente");
    setLoading(false);
    setLoading(false);
  };

  const fetchCliente = async () => {
    setLoading(true);
    const clienteService = new ClienteService();
    const cliente = await clienteService.ObterCliente(id);
    if (!cliente) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao buscar cliente",
        variant: "destructive",
      });
    }
    setCliente(cliente);
    setLoading(false);
  };

  useEffect(() => {
    fetchCliente();
  }, []);

  return (
    <div className="min-h-screen p-4">
      {/* Header moderno - compacto */}
      <div className="mb-4 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <Back
                  icon={<ArrowLeft className="h-4 w-4" />}
                  text="Voltar"
                  href="/admin/cliente"
                />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Editar Cliente
              </h1>
              <p className="text-gray-600 text-sm">
                Atualize as informações do cliente
              </p>
            </div>
            <div className="mt-2 lg:mt-0 flex items-center">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-lg flex items-center space-x-2 text-sm">
                <Users className="h-4 w-4" />
                <span>Sistema Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo principal - compacto */}
      <div className="animate-slideInUp">
        {loading ? (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
            <div className="flex flex-col justify-center items-center space-y-3">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                <Spinner className="text-white" size="medium" />
              </div>
              <div className="text-center">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  Carregando dados do cliente...
                </h3>
                <p className="text-gray-600 text-sm">
                  Aguarde enquanto buscamos as informações
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                  <Users className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900">
                    Editar Dados do Cliente
                  </h3>
                  <p className="text-gray-600 text-xs">
                    Atualize as informações necessárias
                  </p>
                </div>
              </div>
            </div>

            <div className="p-4">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <ClienteForm
                  control={control}
                  register={register}
                  errors={errors}
                  setValue={setValue}
                  cliente={cliente}
                />

                <div className="flex justify-end pt-4 border-t border-gray-200">
                  <Button
                    type="submit"
                    disabled={loading}
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center space-x-2 min-w-[140px]"
                  >
                    {loading ? (
                      <>
                        <Spinner size="small" className="text-white" />
                        <span>Salvando...</span>
                      </>
                    ) : (
                      <>
                        <Users className="h-4 w-4" />
                        <span>Salvar Alterações</span>
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
