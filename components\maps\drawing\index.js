"use client";
import { useEffect, useRef, useState } from "react";

export default function MapDrawing({
  initialValue,
  coordenadas,
  noEdit,
  onPolygonComplete,
  onPolygonEdit,
  selectedMineral,
  mineralColorConfig,
  onHexagonSelect,
  selectedHexagons = [],
  showDrawingControls = false,
  allowPolygonSelection = false,
}) {
  const mapContainerRef = useRef(null);
  const [map, setMap] = useState(null);
  const [polygons, setPolygons] = useState([]);
  const [markers, setMarkers] = useState([]);
  const [pointMarkers, setPointMarkers] = useState([]);

  // Carrega o mapa inicialmente
  useEffect(() => {
    const loadGoogleMaps = () => {
      if (!window.google) return;

      const newMap = new window.google.maps.Map(mapContainerRef.current, {
        center: coordenadas,
        zoom: 15,
        mapTypeId: "satellite",
        tilt: 0,
        rotateControl: false,
        streetViewControl: false,
        mapTypeControl: false, // Desabilita o controle de tipo de mapa
      });

      setMap(newMap);
    };

    if (!window.google) {
      const script = document.createElement("script");
      script.src =
        "https://maps.googleapis.com/maps/api/js?key=AIzaSyBFaRRTTBCasdnXK1RjARVvisx1ihV9rcI&libraries=geometry,drawing";
      script.async = true;
      script.onload = loadGoogleMaps;
      document.body.appendChild(script);
    } else {
      loadGoogleMaps();
    }
  }, []);

  // Função para renderizar polígonos GeoJSON recebidos
  useEffect(() => {
    if (map && initialValue) {
      // Log para depuração
      console.log("Configurações de cores:", mineralColorConfig);

      // Limpa polígonos antigos
      polygons.forEach((polygon) => polygon.setMap(null));

      // Limpa marcadores antigos
      markers.forEach((marker) => marker.setMap(null));

      // Limpa marcadores de pontos
      pointMarkers.forEach((marker) => marker.setMap(null));

      let loadedPolygons = [];
      let loadedMarkers = [];
      let loadedPointMarkers = [];

      // Verifica se é um GeoJSON (hexágonos da API)
      if (initialValue.type === "FeatureCollection" && initialValue.features) {
        loadedPolygons = initialValue.features.map((feature, index) => {
          const coordinates = feature.geometry.coordinates[0].map((coord) => ({
            lat: coord[1],
            lng: coord[0],
          }));

          // Determine polygon color based on mineral value if available
          let fillColor = "hsl(100, 100%, 60%)";
          let strokeColor = "hsl(100, 100%, 50%)";
          let fillOpacity = 0.5;
          let labelText = feature.properties?.id.toString(); // Default label is the index

          // If we have mineral data, use it to determine the color
          if (
            feature.properties &&
            feature.properties.mineralValue !== undefined
          ) {
            const mineralValue = feature.properties.mineralValue;

            // Primeiro, verificar se temos configurações globais de cores
            if (
              mineralColorConfig &&
              Array.isArray(mineralColorConfig) &&
              mineralColorConfig.length > 0
            ) {
              // Find the appropriate color range for this mineral value
              const colorRange = mineralColorConfig.find((range) => {
                const min = parseFloat(range.minimo);
                const max = parseFloat(range.maximo);
                return mineralValue >= min && mineralValue <= max;
              });

              if (colorRange) {
                // Use the configured color for this range
                fillColor = colorRange.cor;
                strokeColor = colorRange.cor; // Use same color for stroke with slight adjustment
                fillOpacity = 0.7;
              } else {
                // Fallback to default color scale if no matching range
                fillColor = `hsl(${Math.max(
                  0,
                  120 - mineralValue * 1.2
                )}, 100%, 60%)`;
                strokeColor = `hsl(${Math.max(
                  0,
                  120 - mineralValue * 1.2
                )}, 100%, 50%)`;
              }
            }
            // Se não temos configurações globais, verificar se temos configurações específicas para este feature
            else if (
              feature.properties.mineralColorConfig &&
              Array.isArray(feature.properties.mineralColorConfig)
            ) {
              const colorConfig = feature.properties.mineralColorConfig;
              // Find the appropriate color range for this mineral value
              const colorRange = colorConfig.find((range) => {
                const min = parseFloat(range.minimo);
                const max = parseFloat(range.maximo);
                return mineralValue >= min && mineralValue <= max;
              });

              if (colorRange) {
                // Use the configured color for this range
                fillColor = colorRange.cor;
                strokeColor = colorRange.cor; // Use same color for stroke with slight adjustment
                fillOpacity = 0.7;
              } else {
                // Fallback to default color scale if no matching range
                fillColor = `hsl(${Math.max(
                  0,
                  120 - mineralValue * 1.2
                )}, 100%, 60%)`;
                strokeColor = `hsl(${Math.max(
                  0,
                  120 - mineralValue * 1.2
                )}, 100%, 50%)`;
              }
            } else {
              // Fallback to default color scale if no configuration
              fillColor = `hsl(${Math.max(
                0,
                120 - mineralValue * 1.2
              )}, 100%, 60%)`;
              strokeColor = `hsl(${Math.max(
                0,
                120 - mineralValue * 1.2
              )}, 100%, 50%)`;
            }

            fillOpacity = 0.7; // Make it more visible

            // Para hexágonos mesclados, mostramos o ID em vez do valor mineral
            if (feature.properties.merged) {
              labelText = feature.properties.id.toString();
            } else {
              // Para hexágonos normais, mostramos o valor mineral
              labelText = mineralValue.toString();
            }
          }

          // Check if this hexagon is in the selected list
          const isSelected = selectedHexagons.includes(feature.properties?.id);

          const polygon = new window.google.maps.Polygon({
            paths: coordinates,
            editable: false,
            draggable: false,
            fillColor,
            strokeColor: isSelected ? "#FFFF00" : strokeColor, // Amarelo brilhante para hexágonos selecionados
            strokeWeight: isSelected ? 4 : 2, // Borda mais grossa para hexágonos selecionados
            strokeOpacity: isSelected ? 1.0 : 0.8, // Maior opacidade para destacar
            fillOpacity: isSelected ? fillOpacity * 1.2 : fillOpacity, // Destaca o preenchimento também
          });

          // Adiciona um segundo polígono interno para criar um efeito de borda dupla para os selecionados
          if (isSelected) {
            // Cria um polígono ligeiramente menor para criar um efeito de borda dupla
            const innerPolygon = new window.google.maps.Polygon({
              paths: coordinates,
              editable: false,
              draggable: false,
              fillColor: "transparent", // Transparente para não interferir na cor
              strokeColor: "#FF0000", // Borda interna vermelha
              strokeWeight: 2,
              strokeOpacity: 0.9,
            });
            innerPolygon.setMap(map);
          }

          polygon.setMap(map);

          // Add click event to select/deselect hexagons only if selection is allowed
          if (onHexagonSelect && allowPolygonSelection) {
            polygon.addListener("click", () => {
              onHexagonSelect(feature.properties?.id);
            });
          }
          const bounds = new google.maps.LatLngBounds();
          coordinates.forEach((coord) => bounds.extend(coord));
          const center = bounds.getCenter();

          // Cria um marcador com o índice no centro e armazena a referência
          const marker = new google.maps.Marker({
            position: center,
            map,
            label: {
              text: labelText, // Use the label text we determined above
              color: "black",
              fontSize: "12px",
              fontWeight: "bold",
            },
            icon: {
              path: google.maps.SymbolPath.CIRCLE,
              scale: 0, // marcador invisível, só label visível
            },
          });

          // Adiciona o marcador à lista de marcadores
          loadedMarkers.push(marker);

          return polygon;
        });

        // Centraliza o mapa no primeiro hexágono carregado
        if (initialValue.features[0]?.geometry.coordinates[0][0]) {
          const [lng, lat] =
            initialValue.features[0].geometry.coordinates[0][0];
          map.setCenter({ lat, lng });
        }
      } else if (initialValue.talhoes) {
        // Formato original (talhões)
        loadedPolygons = initialValue.talhoes.map((t) => {
          const polygon = new window.google.maps.Polygon({
            paths: t.coordenadas,
            editable: !noEdit,
            draggable: !noEdit,
            fillColor: "transparent",
            strokeColor: "hsl(100, 100%, 50%)",
            fillOpacity: 0.5,
          });

          polygon.setMap(map);
          return polygon;
        });

        // Centraliza o mapa no primeiro talhão carregado
        if (initialValue.talhoes[0]?.coordenadas[0]) {
          map.setCenter(initialValue.talhoes[0].coordenadas[0]);
        }
      }

      // Renderiza pontos se existirem
      if (initialValue.points && Array.isArray(initialValue.points)) {
        loadedPointMarkers = initialValue.points
          .map((point, index) => {
            // Verifica se o ponto tem a estrutura esperada
            if (
              point.geometry &&
              point.geometry.type === "Point" &&
              Array.isArray(point.geometry.coordinates)
            ) {
              const [lng, lat] = point.geometry.coordinates;

              // Cria um marcador para o ponto com label
              const pointMarker = new window.google.maps.Marker({
                position: { lat, lng },
                map,
                title: point.properties?.name || `Ponto ${index + 1}`, // Tooltip
                label: {
                  text:
                    point.properties?.id?.toString() || (index + 1).toString(),
                  color: "white",
                  fontSize: "12px",
                  fontWeight: "bold",
                },
                icon: {
                  path: window.google.maps.SymbolPath.CIRCLE,
                  fillColor: "#0066CC", // Azul para diferenciar dos hexágonos
                  fillOpacity: 1,
                  strokeColor: "#FFFFFF",
                  strokeWeight: 2,
                  scale: 8, // tamanho um pouco maior
                },
              });

              return pointMarker;
            }
            return null;
          })
          .filter(Boolean); // Remove valores nulos
      }

      // Atualiza estado uma única vez
      setPolygons(loadedPolygons);
      setMarkers(loadedMarkers);
      setPointMarkers(loadedPointMarkers);

      // Centraliza o mapa se há coordenadas de centro definidas
      if (initialValue.center) {
        map.setCenter(initialValue.center);
      }
    }
  }, [
    map,
    initialValue,
    noEdit,
    selectedMineral,
    mineralColorConfig,
    selectedHexagons,
    allowPolygonSelection,
    onHexagonSelect,
  ]);

  // Atualiza o centro do mapa quando as coordenadas mudam
  useEffect(() => {
    if (map && coordenadas) {
      map.setCenter(coordenadas);
    }
  }, [coordenadas, map]);

  // Função para desenhar polígonos
  useEffect(() => {
    if (map && window.google?.maps?.drawing) {
      // Só mostra o gerenciador de desenho se showDrawingControls for true
      if (showDrawingControls) {
        const drawingManager = new window.google.maps.drawing.DrawingManager({
          drawingMode: window.google.maps.drawing.OverlayType.POLYGON,
          drawingControl: true,
          drawingControlOptions: {
            position: window.google.maps.ControlPosition.TOP_CENTER,
            drawingModes: [window.google.maps.drawing.OverlayType.POLYGON],
          },
          polygonOptions: {
            editable: !noEdit,
            draggable: !noEdit,
            fillColor: "transparent",
            strokeColor: "hsl(100, 100%, 50%)",
            fillOpacity: 0.5,
          },
        });

        drawingManager.setMap(map);

        drawingManager.addListener("overlaycomplete", (event) => {
          if (event.type === "polygon") {
            const polygon = event.overlay;
            polygon.setMap(map);
            const path = polygon.getPath().getArray();
            const coordinates = path.map((latLng) => ({
              lat: latLng.lat(),
              lng: latLng.lng(),
            }));
            onPolygonComplete?.(polygon, coordinates);
          }
        });

        return () => {
          drawingManager.setMap(null);
        };
      }
    }
  }, [map, noEdit, onPolygonComplete, showDrawingControls]);

  return <div ref={mapContainerRef} style={{ height: "500px" }} />;
    initialValue,
    coordenadas,
    noEdit,
    onPolygonComplete,
    onPolygonEdit,
}) {
    const mapContainerRef = useRef(null);
    const [map, setMap] = useState(null);
    const [polygons, setPolygons] = useState([]);

    // Carrega o mapa inicialmente
    useEffect(() => {
        const loadGoogleMaps = () => {
            if (!window.google) return;

            const newMap = new window.google.maps.Map(mapContainerRef.current, {
                center: coordenadas,
                zoom: 15,
                mapTypeId: "satellite",
                tilt: 0,
                rotateControl: false,
                streetViewControl: false,
            });

            setMap(newMap);
        };

        if (!window.google) {
            const script = document.createElement("script");
            script.src = "https://maps.googleapis.com/maps/api/js?key=AIzaSyCTi6KWVtwv58_HaYxb_OrTAa6m3K-A7Ao&libraries=geometry,drawing";
            script.async = true;
            script.onload = loadGoogleMaps;
            document.body.appendChild(script);
        } else {
            loadGoogleMaps();
        }
    }, []);

    // Função para renderizar polígonos GeoJSON recebidos
    useEffect(() => {
        if (map && initialValue) {

            // Limpa polígonos antigos
            polygons.forEach(polygon => polygon.setMap(null));

            let loadedPolygons = [];

            // Verifica se é um GeoJSON (hexágonos da API)
            if (initialValue.type === "FeatureCollection" && initialValue.features) {
                loadedPolygons = initialValue.features.map((feature, index) => {
                    const coordinates = feature.geometry.coordinates[0].map(coord => ({
                        lat: coord[1],
                        lng: coord[0]
                    }));

                    const polygon = new window.google.maps.Polygon({
                        paths: coordinates,
                        editable: false,
                        draggable: false,
                        fillColor: "hsl(100, 100%, 60%)",
                        strokeColor: "hsl(100, 100%, 50%)",
                        fillOpacity: 0.5,
                    });

                    polygon.setMap(map);
                    const bounds = new google.maps.LatLngBounds();
                    coordinates.forEach(coord => bounds.extend(coord));
                    const center = bounds.getCenter();

                    // Cria um marcador com o índice no centro
                    new google.maps.Marker({
                        position: center,
                        map,
                        label: {
                            text: (index + 1).toString(), // gera índices começando em 1
                            color: "black",
                            fontSize: "12px",
                            fontWeight: "bold"
                        },
                        icon: {
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 0, // marcador invisível, só label visível
                        },
                    });

                    return polygon;
                });

                // Centraliza o mapa no primeiro hexágono carregado
                if (initialValue.features[0]?.geometry.coordinates[0][0]) {
                    const [lng, lat] = initialValue.features[0].geometry.coordinates[0][0];
                    map.setCenter({ lat, lng });
                }

            } else if (initialValue.talhoes) {
                // Formato original (talhões)
                loadedPolygons = initialValue.talhoes.map(t => {
                    const polygon = new window.google.maps.Polygon({
                        paths: t.coordenadas,
                        editable: !noEdit,
                        draggable: !noEdit,
                        fillColor: "transparent",
                        strokeColor: "hsl(100, 100%, 50%)",
                        fillOpacity: 0.5,
                    });

                    polygon.setMap(map);
                    return polygon;
                });

                // Centraliza o mapa no primeiro talhão carregado
                if (initialValue.talhoes[0]?.coordenadas[0]) {
                    map.setCenter(initialValue.talhoes[0].coordenadas[0]);
                }
            }

            // Atualiza estado uma única vez
            setPolygons(loadedPolygons);
        }
    }, [map, initialValue, noEdit]);

    // Atualiza o centro do mapa quando as coordenadas mudam
    useEffect(() => {
        if (map && coordenadas) {
            map.setCenter(coordenadas);
        }
    }, [coordenadas, map]);


    // Função para desenhar polígonos
    useEffect(() => {
        if (map && window.google?.maps?.drawing) {
            const drawingManager = new window.google.maps.drawing.DrawingManager({
                drawingMode: window.google.maps.drawing.OverlayType.POLYGON,
                drawingControl: true,
                drawingControlOptions: {
                    position: window.google.maps.ControlPosition.TOP_CENTER,
                    drawingModes: [window.google.maps.drawing.OverlayType.POLYGON],
                },
                polygonOptions: {
                    editable: !noEdit,
                    draggable: !noEdit,
                    fillColor: "transparent",
                    strokeColor: "hsl(100, 100%, 50%)",
                    fillOpacity: 0.5,
                },
            });

            drawingManager.setMap(map);

            drawingManager.addListener("overlaycomplete", (event) => {
                if (event.type === "polygon") {
                    const polygon = event.overlay;
                    polygon.setMap(map);
                    const path = polygon.getPath().getArray();
                    const coordinates = path.map(latLng => ({
                        lat: latLng.lat(),
                        lng: latLng.lng(),
                    }));
                    onPolygonComplete?.(polygon, coordinates);
                }
            });

            return () => {
                drawingManager.setMap(null);
            };
        }
    }, [map, noEdit, onPolygonComplete]);

    return <div ref={mapContainerRef} style={{ height: "500px" }} />;
}
