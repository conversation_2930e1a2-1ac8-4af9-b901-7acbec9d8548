"use client";
import { <PERSON>ertDialogUI } from "@/components/alertDialog";
import { PaginationUI } from "@/components/pagination";
import Tables from "@/components/tables/Tables";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MultiSelect } from "@/components/ui/multi-select";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ModernLoader } from "@/components/base/LoadingSpinner";
import { useToast } from "@/hooks/use-toast";
import FuncionarioService from "@/lib/services/funcionarioService";
import VisualizarMapaService from "@/lib/services/visualizarMapa";
import {
  FileText,
  Filter,
  Grid3X3,
  List,
  MapIcon,
  Search,
  X,
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

function VisualizarColetaContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();
  const [showDialog, setShowDialog] = useState(false);
  const [coletas, setColetas] = useState([]);
  const [confirmCallback, setConfirmCallback] = useState(null);
  const [totalPage, setTotalPage] = useState(0);
  const [loading, setLoading] = useState(false);
  const currentPage = Number(searchParams.get("page")) || 1;

  // Estados para filtros e visualização
  const [filtroTipoColeta, setFiltroTipoColeta] = useState("");
  const [filtroTipoAnalise, setFiltroTipoAnalise] = useState([]);
  const [filtroFuncionarioID, setFiltroFuncionarioID] = useState("");
  const [funcionarioSearch, setFuncionarioSearch] = useState("");
  const [listaFuncionarios, setListaFuncionarios] = useState([]);
  const [viewMode, setViewMode] = useState("cards"); // "table" ou "cards"

  // Dados para os selects
  const TIPO_COLETA = [
    { id: "Hexagonal", nome: "Hexagonal (zonas)" },
    { id: "Retangular", nome: "Retangular (zonas)" },
    { id: "PontosAmostrais", nome: "Pontos Amostral" },
  ];

  const TIPO_ANALISE = [
    { id: "Macronutrientes", nome: "Macronutrientes" },
    { id: "Micronutrientes", nome: "Micronutrientes" },
    { id: "Textura", nome: "Textura" },
    { id: "Microbiologica", nome: "Microbiologica (Metagenomica)" },
    { id: "BioAs", nome: "BioAs" },
    { id: "Compactacao", nome: "Compactação" },
    { id: "Outros", nome: "Outros" },
  ];

  const columns = [
    {
      headerName: "Talhão",
      field: "nome",
      renderCell: (params) => params.row.talhao.nome,
    },
    {
      headerName: "Fazenda",
      field: "fazenda",
      renderCell: (params) =>
        params.row.talhao?.fazenda?.nome || params.row.fazenda?.nome || "N/A",
    },
    {
      headerName: "Safra",
      field: "safra",
      renderCell: (params) =>
        params.row.talhao?.safra?.nome || params.row.safra?.nome || "N/A",
    },
    {
      headerName: "Tipo Coleta",
      field: "tipoColeta",
      renderCell: (params) => params.row.tipoColeta,
    },
    {
      headerName: "Tipo Analise",
      field: "tipoAnalise",
      renderCell: (params) => {
        // Verifica se tipoAnalise existe e é um array
        const tiposAnalise = Array.isArray(params.row.tipoAnalise)
          ? params.row.tipoAnalise
          : params.row.tipoAnalise
          ? [params.row.tipoAnalise]
          : [];

        // Se não houver tipos de análise, exibe um texto informativo
        if (tiposAnalise.length === 0) {
          return <span className="text-gray-400 text-sm">Nenhum</span>;
        }

        // Renderiza todos os badges dentro de um único container flex
        return (
          <div className="flex flex-wrap gap-1">
            {tiposAnalise.map((analise) => (
              <Badge
                key={analise}
                variant="secondary"
                className="text-xs py-0.5 px-2"
              >
                {analise}
              </Badge>
            ))}
          </div>
        );
      },
    },
    {
      headerName: "Observação",
      field: "observacao",
      renderCell: (params) => params.row.observacao || "N/A",
    },
    {
      headerName: "Funcionário",
      field: "funcionario",
      renderCell: (params) => params.row.usuarioResp.nomeCompleto,
    },
    {
      headerName: "Ações",
      field: "acoes",
      renderCell: (params) => (
        <div className="flex justify-center gap-2">
          <Button
            size="sm"
            onClick={() => visualizarColeta(params.row.id)}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white transition-all duration-200 transform hover:scale-105 border-0"
          >
            <MapIcon className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ];

  const visualizarColeta = (id) => {
    router.push(`/admin/visualizarmapa/visualizar/${id}`);
  };

  // Buscar lista de funcionários para o filtro
  const fetchFuncionarios = async () => {
    try {
      const funcionarioService = new FuncionarioService();
      const response = await funcionarioService.listarFuncionarios("");
      console.log("Funcionários para filtro:", response);
      if (response) {
        setListaFuncionarios(response.items);
      }
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao carregar lista de funcionários para filtro",
        variant: "destructive",
      });
    }
  };

  // Função para aplicar filtros
  const aplicarFiltros = () => {
    const params = new URLSearchParams(searchParams);

    // Adicionar filtros aos parâmetros
    if (filtroTipoColeta) params.set("tipoColeta", filtroTipoColeta);
    else params.delete("tipoColeta");

    if (filtroTipoAnalise && filtroTipoAnalise.length > 0) {
      // Adiciona cada tipo de análise como um parâmetro separado com o mesmo nome
      filtroTipoAnalise.forEach((tipo) => {
        params.append("tipoAnalise", tipo);
      });
    } else {
      params.delete("tipoAnalise");
    }

    if (filtroFuncionarioID) params.set("funcionarioID", filtroFuncionarioID);
    else params.delete("funcionarioID");

    // Resetar para página 1 ao filtrar
    params.set("page", 1);

    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  // Função para limpar filtros
  const limparFiltros = () => {
    setFiltroTipoColeta("");
    setFiltroTipoAnalise("");
    setFiltroFuncionarioID("");
    setFuncionarioSearch("");

    const params = new URLSearchParams();
    params.set("page", 1);
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  // Filtrar funcionários para o select
  const filterFuncionarios = funcionarioSearch
    ? listaFuncionarios.filter((funcionario) =>
        funcionario.nomeCompleto
          .toLowerCase()
          .includes(funcionarioSearch.toLowerCase())
      )
    : listaFuncionarios;

  const fetchColetas = async (page) => {
    setLoading(true);
    const response = new VisualizarMapaService();
    const coletas = await response.ListarColetas(page);
    if (!coletas) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao buscar coletas",
        variant: "destructive",
      });
    }
    setColetas(coletas.items);
    setTotalPage(coletas.totalPages);
    setLoading(false);
  };

  useEffect(() => {
    // Carregar lista de funcionários para o filtro
    fetchFuncionarios();
  }, []);

  useEffect(() => {
    const params = new URLSearchParams();
    params.set("page", currentPage);
    router.push(`${window.location.pathname}?${params.toString()}`);
  }, []);

  useEffect(() => {
    fetchColetas(searchParams);
  }, [currentPage, searchParams]);

  const currentDate = new Date().toLocaleDateString("pt-BR", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <div className="min-h-screen p-4">
      <AlertDialogUI
        title="Confirmação de exclusão"
        description="Deseja realmente deletar esta coleta?"
        showDialog={showDialog}
        setShowDialog={setShowDialog}
        onConfirm={confirmCallback}
      />

      {/* Header moderno */}
      <div className="mb-4 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Relatório de Coletas
              </h1>
              <p className="text-gray-600 text-sm">
                {currentDate} • Visualize e analise as coletas realizadas
              </p>
            </div>
            <div className="mt-2 lg:mt-0 flex items-center space-x-4">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-lg flex items-center space-x-2 text-sm">
                <FileText className="h-4 w-4" />
                <span>{coletas.length} Relatórios</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Seção de filtros moderna */}
      <div className="mb-4 animate-slideInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <div className="p-1 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg">
                <Filter className="h-5 w-5 text-white" />
              </div>
              <div>
                <h2 className="text-base font-bold text-gray-900">
                  Filtros Avançados
                </h2>
                <p className="text-gray-600 text-xs">
                  Refine sua pesquisa de relatórios
                </p>
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="group">
              <Label
                htmlFor="filtroTipoColeta"
                className="text-sm font-medium text-gray-700 mb-1 block"
              >
                Tipo Coleta
              </Label>
              <Select
                value={filtroTipoColeta}
                onValueChange={setFiltroTipoColeta}
              >
                <SelectTrigger className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9">
                  <SelectValue placeholder="Selecione o tipo de coleta..." />
                </SelectTrigger>
                <SelectContent>
                  {TIPO_COLETA.map((tipo) => (
                    <SelectItem key={tipo.id} value={tipo.id}>
                      {tipo.nome}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="group">
              <Label
                htmlFor="filtroTipoAnalise"
                className="text-sm font-medium text-gray-700 mb-1 block"
              >
                Tipo Análise
              </Label>
              <MultiSelect
                options={TIPO_ANALISE}
                selected={
                  Array.isArray(filtroTipoAnalise)
                    ? filtroTipoAnalise
                    : filtroTipoAnalise
                    ? [filtroTipoAnalise]
                    : []
                }
                onChange={(values) => setFiltroTipoAnalise(values)}
                placeholder="Selecione o tipo de análise..."
              />
            </div>
            <div className="group">
              <Label
                htmlFor="filtroFuncionario"
                className="text-sm font-medium text-gray-700 mb-1 block"
              >
                Funcionário
              </Label>
              <Select
                value={filtroFuncionarioID}
                onValueChange={(value) => {
                  setFiltroFuncionarioID(value);
                  const funcionarioSelecionado = listaFuncionarios.find(
                    (f) => f.id === value
                  );
                  if (funcionarioSelecionado) {
                    setFuncionarioSearch(funcionarioSelecionado.nomeCompleto);
                  }
                }}
              >
                <SelectTrigger className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9">
                  <SelectValue placeholder="Selecione um funcionário..." />
                </SelectTrigger>
                <SelectContent>
                  <Input
                    type="text"
                    placeholder="Buscar funcionário..."
                    value={funcionarioSearch}
                    onChange={(e) => setFuncionarioSearch(e.target.value)}
                    className="p-2"
                  />
                  {filterFuncionarios.length > 0 ? (
                    filterFuncionarios.map((funcionario) => (
                      <SelectItem key={funcionario.id} value={funcionario.id}>
                        {funcionario.nomeCompleto}
                      </SelectItem>
                    ))
                  ) : (
                    <p className="p-2 text-gray-500">
                      Nenhum funcionário encontrado.
                    </p>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-2">
            <Button
              variant="outline"
              onClick={limparFiltros}
              className="border-gray-300 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2 h-9"
            >
              <X className="w-4 h-4" />
              <span>Limpar Filtros</span>
            </Button>
            <Button
              onClick={aplicarFiltros}
              className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white transition-all duration-200 transform hover:scale-105 flex items-center space-x-2 h-9"
            >
              <Search className="w-4 h-4" />
              <span>Aplicar Filtros</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="animate-fadeInUp">
        {loading ? (
          <ModernLoader
            message="Carregando relatórios..."
            subtitle="Aguarde enquanto buscamos os dados"
            variant="glass"
            size="lg"
          />
        ) : (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                    <FileText className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">
                      Relatório de Coletas
                    </h3>
                    <p className="text-gray-600 text-xs">
                      {coletas.length} relatório
                      {coletas.length !== 1 ? "s" : ""} encontrado
                      {coletas.length !== 1 ? "s" : ""}
                    </p>
                  </div>
                </div>

                {/* Botões de alternância de visualização */}
                <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
                  <Button
                    size="sm"
                    variant={viewMode === "cards" ? "default" : "ghost"}
                    onClick={() => setViewMode("cards")}
                    className={`transition-all duration-200 h-8 ${
                      viewMode === "cards"
                        ? "bg-gradient-to-r from-emerald-500 to-teal-500 text-white shadow-lg"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <Grid3X3 className="h-3 w-3 mr-1" />
                    Cards
                  </Button>
                  <Button
                    size="sm"
                    variant={viewMode === "table" ? "default" : "ghost"}
                    onClick={() => setViewMode("table")}
                    className={`transition-all duration-200 h-8 ${
                      viewMode === "table"
                        ? "bg-white shadow-sm text-gray-900"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <List className="h-3 w-3 mr-1" />
                    Tabela
                  </Button>
                </div>
              </div>
            </div>

            {coletas.length === 0 ? (
              <div className="p-8 text-center">
                <div className="flex flex-col items-center space-y-3">
                  <div className="p-3 bg-gray-100 rounded-full">
                    <FileText className="h-8 w-8 text-gray-400" />
                  </div>
                  <div>
                    <h3 className="text-base font-medium text-gray-900 mb-1">
                      Nenhum relatório encontrado
                    </h3>
                    <p className="text-gray-600 text-sm">
                      Não há relatórios de coletas ou que correspondam aos
                      filtros aplicados.
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-4">
                {viewMode === "cards" ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {coletas.map((coleta, index) => (
                      <div
                        key={coleta.id}
                        className="bg-white/60 backdrop-blur-sm rounded-lg border border-gray-200 p-4 hover:shadow-lg transition-all duration-200 animate-slideInUp"
                        style={{ animationDelay: `${index * 0.1}s` }}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <div className="p-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg">
                              <FileText className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900 text-sm">
                                {coleta.talhao.nome}
                              </h4>
                              <p className="text-gray-600 text-xs">
                                {coleta.tipoColeta}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2 mb-4">
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-gray-500">Fazenda:</span>
                            <span className="font-medium text-gray-900">
                              {coleta.talhao?.fazenda?.nome ||
                                coleta.fazenda?.nome ||
                                "N/A"}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-gray-500">Safra:</span>
                            <span className="font-medium text-gray-900">
                              {coleta.talhao?.safra?.nome ||
                                coleta.safra?.nome ||
                                "N/A"}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-gray-500">Funcionário:</span>
                            <span className="font-medium text-gray-900">
                              {coleta.usuarioResp.nomeCompleto}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-gray-500">Observação:</span>
                            <span className="font-medium text-gray-900">
                              {coleta.observacao || "N/A"}
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {Array.isArray(coleta.tipoAnalise) ? (
                              coleta.tipoAnalise.map((analise) => (
                                <Badge
                                  key={analise}
                                  variant="secondary"
                                  className="text-xs py-0.5 px-2"
                                >
                                  {analise}
                                </Badge>
                              ))
                            ) : coleta.tipoAnalise ? (
                              <Badge
                                variant="secondary"
                                className="text-xs py-0.5 px-2"
                              >
                                {coleta.tipoAnalise}
                              </Badge>
                            ) : (
                              <span className="text-gray-400 text-xs">
                                Nenhuma análise
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex justify-end">
                          <Button
                            size="sm"
                            onClick={() => visualizarColeta(coleta.id)}
                            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white transition-all duration-200 transform hover:scale-105 border-0 h-8"
                          >
                            <MapIcon className="w-3 h-3 mr-1" />
                            <span className="text-xs">Visualizar</span>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Tables data={coletas} columns={columns} />
                )}
              </div>
            )}

            {totalPage > 1 && (
              <div className="p-4 border-t border-gray-200 bg-gray-50/50">
                <div className="flex justify-end items-center">
                  <PaginationUI totalPage={totalPage} />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default function VisualizarColeta() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen p-4 flex items-center justify-center">
          <ModernLoader
            message="Carregando relatórios..."
            subtitle="Aguarde enquanto buscamos os dados"
            variant="glass"
            size="lg"
          />
        </div>
      }
    >
      <VisualizarColetaContent />
    </Suspense>
  );
}
