"use client";
import Back from "@/components/back";
import Histogram from "@/components/charts/Histogram";
import HistogramControls from "@/components/charts/HistogramControls";
import MapaContainer from "@/components/forms/MapaContainer";
import PickerColor from "@/components/pickerColor/picker";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Spinner } from "@/components/ui/spinner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import ConfiguracaoPersonalizadaService from "@/lib/services/configuracaoPersonalizadaService";
import RelatorioService from "@/lib/services/relatorioService";
import VisualizarMapaService from "@/lib/services/visualizarMapa";
import {
  Activity,
  ArrowLeft,
  BarChart3,
  Calculator,
  Database,
  Download,
  FileText,
  Map,
  Settings,
  Upload,
} from "lucide-react";
import { use, useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import * as XLSX from "xlsx";

export default function VisualizarMapa({ params, visualizar = false }) {
  const { id } = use(params);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDataModalOpen, setIsDataModalOpen] = useState(false);
  const [fileName, setFileName] = useState("");
  const [arquivo, setArquivo] = useState(null);
  const [data, setData] = useState([]);
  const [columns, setColumns] = useState([]);
  const [minerais, setMinerais] = useState([]);
  const [selectedMineral, setSelectedMineral] = useState("");
  const [isColorConfigOpen, setIsColorConfigOpen] = useState(false);
  const [isHistogramOpen, setIsHistogramOpen] = useState(false);
  const [histogramBins, setHistogramBins] = useState(10);
  const [histogramColor, setHistogramColor] = useState("#3b82f6");
  const [mineralColorConfig, setMineralColorConfig] = useState([]);
  const [geoJsonData, setGeoJsonData] = useState({});
  const [downloadLink, setDownloadLink] = useState(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const [calculatedColumns, setCalculatedColumns] = useState([]);
  const [isCalculatorModalOpen, setIsCalculatorModalOpen] = useState(false);
  const [formula, setFormula] = useState("");
  const [formulaResult, setFormulaResult] = useState(null);

  // Setup form for color configuration
  const { control, register, handleSubmit, watch, setValue } = useForm({
    defaultValues: {
      limites: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "limites",
  });

  const createCalculatedColumn = (column1, column2, operation) => {
    const newColumnName = `${column1}_${operation}_${column2}`;

    // Cria novos dados com a coluna calculada
    const newData = data.map((row) => {
      const value1 = parseFloat(row[column1]) || 0;
      const value2 = parseFloat(row[column2]) || 0;
      let result = 0;

      switch (operation) {
        case "sum":
          result = value1 + value2;
          break;
        case "subtract":
          result = value1 - value2;
          break;
        case "multiply":
          result = value1 * value2;
          break;
        case "divide":
          result = value2 !== 0 ? value1 / value2 : 0;
          break;
      }

      return {
        ...row,
        [newColumnName]: Number(result.toFixed(2)),
      };
    });

    // Atualiza os dados
    setData(newData);

    // Adiciona a nova coluna à lista de minerais possíveis
    setMinerais((prev) => [...prev, newColumnName]);

    // Atualiza as colunas da tabela
    setColumns((prev) => [
      ...prev,
      {
        accessorKey: newColumnName,
        header: newColumnName,
      },
    ]);

    return newColumnName;
  };

  // Função para avaliar fórmulas personalizadas
  const evaluateFormula = (formula, rowData) => {
    try {
      // Substitui os nomes dos minerais pelos valores correspondentes
      let processedFormula = formula;
      minerais.forEach((mineral) => {
        const regex = new RegExp(mineral, "g");
        const value = parseFloat(rowData[mineral]) || 0;
        processedFormula = processedFormula.replace(regex, value);
      });

      // Avalia a expressão
      // eslint-disable-next-line no-eval
      const result = eval(processedFormula);
      return Number(result.toFixed(2));
    } catch (error) {
      console.error("Erro ao avaliar fórmula:", error);
      return 0;
    }
  };

  // Função para criar coluna com base em uma fórmula personalizada
  const createFormulaColumn = (formula, columnName) => {
    if (!formula || !columnName) return;

    // Cria novos dados com a coluna calculada
    const newData = data.map((row) => {
      const result = evaluateFormula(formula, row);
      return {
        ...row,
        [columnName]: result,
      };
    });

    // Atualiza os dados
    setData(newData);

    // Adiciona a nova coluna à lista de minerais possíveis
    setMinerais((prev) => [...prev, columnName]);

    // Atualiza as colunas da tabela
    setColumns((prev) => [
      ...prev,
      {
        accessorKey: columnName,
        header: columnName,
      },
    ]);

    return columnName;
  };

  const ColumnCalculator = () => {
    const [column1, setColumn1] = useState("");
    const [column2, setColumn2] = useState("");
    const [operation, setOperation] = useState("sum");

    const handleCalculate = () => {
      if (column1 && column2) {
        createCalculatedColumn(column1, column2, operation);
      }
    };

    return (
      <div className="flex gap-2 items-end">
        <div className="flex-1">
          <Select onValueChange={setColumn1} value={column1}>
            <SelectTrigger>
              <SelectValue placeholder="Coluna 1" />
            </SelectTrigger>
            <SelectContent>
              {minerais.map((mineral) => (
                <SelectItem key={mineral} value={mineral}>
                  {mineral}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex-1">
          <Select onValueChange={setOperation} value={operation}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sum">+</SelectItem>
              <SelectItem value="subtract">-</SelectItem>
              <SelectItem value="multiply">×</SelectItem>
              <SelectItem value="divide">÷</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex-1">
          <Select onValueChange={setColumn2} value={column2}>
            <SelectTrigger>
              <SelectValue placeholder="Coluna 2" />
            </SelectTrigger>
            <SelectContent>
              {minerais.map((mineral) => (
                <SelectItem key={mineral} value={mineral}>
                  {mineral}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Button onClick={handleCalculate}>Calcular</Button>
      </div>
    );
  };

  // Componente para a calculadora avançada
  const FormulaCalculator = () => {
    const [formulaInput, setFormulaInput] = useState("");
    const [columnName, setColumnName] = useState("");
    const [previewResult, setPreviewResult] = useState(null);
    const [error, setError] = useState("");

    const handlePreview = () => {
      if (!formulaInput) {
        setError("Por favor, insira uma fórmula");
        return;
      }

      try {
        // Testa a fórmula com a primeira linha de dados
        if (data.length > 0) {
          const result = evaluateFormula(formulaInput, data[0]);
          setPreviewResult(result);
          setError("");
        }
      } catch (error) {
        setError("Fórmula inválida. Verifique a sintaxe.");
        setPreviewResult(null);
      }
    };

    const handleCreateColumn = () => {
      if (!formulaInput) {
        setError("Por favor, insira uma fórmula");
        return;
      }

      if (!columnName) {
        setError("Por favor, insira um nome para a coluna");
        return;
      }

      try {
        createFormulaColumn(formulaInput, columnName);
        setIsCalculatorModalOpen(false);
        setFormulaInput("");
        setColumnName("");
        setPreviewResult(null);
        setError("");

        toast({
          title: "Coluna criada",
          description: `A coluna "${columnName}" foi criada com sucesso.`,
          variant: "success",
        });
      } catch (error) {
        setError("Erro ao criar coluna. Verifique a fórmula.");
      }
    };

    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Nome da Coluna</label>
          <Input
            value={columnName}
            onChange={(e) => setColumnName(e.target.value)}
            placeholder="Ex: Resultado_PH"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Fórmula</label>
          <div className="flex gap-2">
            <Input
              value={formulaInput}
              onChange={(e) => setFormulaInput(e.target.value)}
              placeholder="Ex: (PH + PHP) / 100"
              className="flex-1"
            />
            <Button onClick={handlePreview} variant="outline">
              Testar
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Use os nomes dos minerais exatamente como aparecem na tabela.
            Operações suportadas: +, -, *, /, (, )
          </p>
        </div>

        <div className="bg-muted p-4 rounded-md">
          <h4 className="font-medium mb-2">Minerais disponíveis:</h4>
          <div className="flex flex-wrap gap-2">
            {minerais.map((mineral) => (
              <div
                key={mineral}
                className="bg-secondary px-2 py-1 rounded-md text-sm cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                onClick={() => setFormulaInput((prev) => prev + mineral)}
              >
                {mineral}
              </div>
            ))}
          </div>
        </div>

        {error && (
          <div className="bg-destructive/10 text-destructive p-3 rounded-md text-sm">
            {error}
          </div>
        )}

        {previewResult !== null && (
          <div className="bg-primary/10 text-primary p-3 rounded-md">
            <p className="font-medium">Resultado do teste:</p>
            <p>Valor calculado: {previewResult}</p>
          </div>
        )}

        <div className="flex justify-end">
          <Button onClick={handleCreateColumn}>Criar Coluna</Button>
        </div>
      </div>
    );
  };

  const fecthGeoJson = async (id) => {
    try {
      const visualizarmapa = new VisualizarMapaService();
      const relatorio = await visualizarmapa.ObterColeta(id);
      relatorio.geojson.pontos = JSON.parse(relatorio.geojson.pontos);
      setGeoJsonData(relatorio);
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao carregar dados do mapa",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fecthRelatorio = async (id) => {
    try {
      const relatorioService = new RelatorioService();
      const relatorio = await relatorioService.ObterRelatorio(id);

      if (relatorio?.jsonRelatorio) {
        const parsedData = JSON.parse(relatorio.jsonRelatorio);

        setGeoJsonData(relatorio);
        setDownloadLink(relatorio.linkBackup);
        setData(parsedData);

        const headers = Object.keys(parsedData[0]);
        const possibleMinerals = headers.filter(
          (header) => typeof parsedData[0][header] === "number"
        );

        const columnHeaders = headers.map((key) => ({
          accessorKey: key,
          header: key,
        }));

        setColumns(columnHeaders);
        setMinerais(possibleMinerals);
        if (!selectedMineral && possibleMinerals.length > 0) {
          setSelectedMineral(possibleMinerals[0]);
        }
      }
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao carregar dados do mapa",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Função para carregar configurações de cores da API
  const fetchColorConfigurations = async () => {
    try {
      const configService = new ConfiguracaoPersonalizadaService();
      const configs = await configService.listarConfiguracoes();

      // Filtrar configurações globais
      const globalConfigs = configs.filter(
        (config) => config.nome === "global"
      );

      if (globalConfigs.length > 0) {
        // Converter para o formato usado pelo componente
        const formattedConfigs = globalConfigs.map((config) => ({
          minimo: config.limiteInferior.toString(),
          maximo: config.limiteSuperior.toString(),
          cor: config.corHex,
        }));

        // Atualizar o estado e o formulário
        setMineralColorConfig(formattedConfigs);

        // Atualizar o formulário com as configurações carregadas
        setValue("limites", formattedConfigs);

        toast({
          title: "Configurações carregadas",
          description: "Configurações de cores globais carregadas com sucesso.",
          variant: "success",
        });
      } else {
        // Se não houver configurações globais, limpar o formulário
        setValue("limites", []);
        setMineralColorConfig([]);

        // Adicionar um intervalo vazio para o usuário começar a configurar
        append({ minimo: "", maximo: "", cor: "#ffffff" });
      }
    } catch (error) {
      console.error("Erro ao carregar configurações de cores:", error);
      // Em caso de erro, limpar o formulário e adicionar um intervalo vazio
      setValue("limites", []);
      setMineralColorConfig([]);
      append({ minimo: "", maximo: "", cor: "#ffffff" });
    }
  };

  // Efeito para carregar dados iniciais
  useEffect(() => {
    fecthGeoJson(id);
    fecthRelatorio(id);

    // Carregar configurações de cores globais
    fetchColorConfigurations();
  }, [id]);

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setArquivo(file);
    setFileName(file.name);
    const reader = new FileReader();

    reader.onload = (evt) => {
      const data = evt.target.result;
      const workbook = XLSX.read(data, { type: "array" });
      const worksheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[worksheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      if (jsonData.length > 0) {
        const headers = Object.keys(jsonData[0]);

        //
        // entify mineral columns (numeric values) from the file header
        const possibleMinerals = headers.filter((header) => {
          // Check if the column contains numeric values
          return typeof jsonData[0][header] === "number";
        });

        const columnHeaders = headers.map((key) => ({
          accessorKey: key,
          header: key,
        }));

        setColumns(columnHeaders);
        setData(jsonData);
        setMinerais(possibleMinerals);
        setIsModalOpen(true);
      }
    };

    reader.readAsArrayBuffer(file);
  };

  const handleSave = async () => {
    const updateRelatorio = new RelatorioService();
    const formData = new FormData();
    if (arquivo) formData.append("Arquivo", arquivo);
    formData.append("ArquivoJson", JSON.stringify(data));
    formData.append("ColetaId", id);

    const update = await updateRelatorio.salvarRelatorio(formData);
    if (update) {
      toast({
        title: "Sucesso",
        description: "Dados salvos com sucesso!",
        variant: "success",
      });
      setIsModalOpen(false);

      // If we have minerals, select the first one by default
      if (minerais.length > 0 && !selectedMineral) {
        setSelectedMineral(minerais[0]);
      }

      // Don't automatically open the data modal - let the user see the map first
      // They can click the "Ver Dados" button to see the data
    } else {
      toast({
        title: "Erro",
        description: "Erro ao salvar os dados",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen p-4">
      {/* Header moderno - compacto */}
      <div className="mb-4 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <Back
                  icon={<ArrowLeft className="h-4 w-4" />}
                  text="Voltar"
                  href="/admin/visualizarmapa"
                />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Resultado da Análise
              </h1>
              <p className="text-gray-600 text-sm">
                Visualize e analise os dados da coleta de solo
              </p>
            </div>
            <div className="mt-2 lg:mt-0 flex items-center space-x-4">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-lg flex items-center space-x-2 text-sm">
                <Activity className="h-4 w-4" />
                <span>Análise Ativa</span>
              </div>
              <div className="flex gap-2">
                {data.length > 0 && (
                  <Button
                    variant="outline"
                    onClick={() => setIsDataModalOpen(true)}
                    className="border-gray-300 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2"
                  >
                    <Database className="h-4 w-4" />
                    <span>Ver Dados</span>
                  </Button>
                )}
                {data.length > 0 && (
                  <Button
                    variant="outline"
                    onClick={() => setIsCalculatorModalOpen(true)}
                    className="border-gray-300 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2"
                  >
                    <Calculator className="h-4 w-4" />
                    <span>Calculadora</span>
                  </Button>
                )}
                {downloadLink && (
                  <Button
                    variant="outline"
                    onClick={() => window.open(data.linkBackup, "_blank")}
                    className="border-gray-300 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2"
                  >
                    <Download className="h-4 w-4" />
                    <span>Exportar Original</span>
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={() => window.open(data.linkBackup, "_blank")}
                  className="border-gray-300 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2"
                >
                  <Download className="h-4 w-4" />
                  <span>Exportar Relatório</span>
                </Button>
                <Button
                  onClick={() => document.getElementById("file-upload").click()}
                  className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white transition-all duration-200 transform hover:scale-105 flex items-center space-x-2"
                >
                  <Upload className="h-4 w-4" />
                  <span>Upload de Arquivo</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <input
        id="file-upload"
        type="file"
        accept=".xlsx, .xls"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* Conteúdo Principal */}
      <div className="space-y-4 animate-fadeInUp">
        {/* Card de Informações */}
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                <FileText className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900">
                  Informações da Coleta
                </h3>
                <p className="text-gray-600 text-xs">
                  Dados detalhados da coleta realizada
                </p>
              </div>
            </div>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Responsável
                </p>
                <p className="font-medium">
                  {geoJsonData?.usuarioResp?.nomeCompleto}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Tipo de Coleta
                </p>
                <p className="font-medium">{geoJsonData?.tipoColeta}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Tipo de Análise
                </p>
                <div className="flex flex-wrap gap-1">
                  {Array.isArray(geoJsonData?.tipoAnalise) ? (
                    geoJsonData.tipoAnalise.map((tipo, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="font-medium"
                      >
                        {tipo}
                      </Badge>
                    ))
                  ) : geoJsonData?.tipoAnalise ? (
                    <Badge variant="secondary" className="font-medium">
                      {geoJsonData.tipoAnalise}
                    </Badge>
                  ) : (
                    <span className="text-gray-400">Nenhum</span>
                  )}
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Área do Talhão
                </p>
                <p className="font-medium">{geoJsonData?.talhao?.area} ha</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Nome do Talhão
                </p>
                <p className="font-medium">{geoJsonData?.talhao?.nome}</p>
              </div>
              {geoJsonData?.observacao && (
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">
                    Observação
                  </p>
                  <p className="font-medium">{geoJsonData.observacao}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Card do Mapa */}
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
          {minerais.length > 0 && (
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-1.5 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                  <Map className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900">
                    Selecione um Atributo
                  </h3>
                  <p className="text-gray-600 text-xs">
                    Escolha um atributo para visualizar no mapa
                  </p>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex gap-2">
                  <div className="flex-1">
                    <Select
                      onValueChange={setSelectedMineral}
                      value={selectedMineral}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione um mineral..." />
                      </SelectTrigger>
                      <SelectContent>
                        {minerais.map((mineral, index) => (
                          <SelectItem key={index} value={mineral}>
                            {mineral}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  {selectedMineral && (
                    <>
                      <Button
                        variant="outline"
                        onClick={() => setIsColorConfigOpen(true)}
                        className="border-gray-300 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2"
                      >
                        <Settings className="h-4 w-4" />
                        <span>Configurar Cores</span>
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setIsHistogramOpen(true)}
                        className="border-gray-300 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2"
                      >
                        <BarChart3 className="h-4 w-4" />
                        <span>Histograma</span>
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}
          {loading ? (
            <div className="p-8">
              <div className="flex flex-col justify-center items-center space-y-3">
                <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                  <Spinner className="text-white" size="medium" />
                </div>
                <div className="text-center">
                  <h3 className="text-base font-medium text-gray-900 mb-1">
                    Carregando mapa...
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Aguarde enquanto processamos os dados
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-4">
              <MapaContainer
                initialValue={
                  geoJsonData?.geojson?.pontos?.features
                    ? {
                        type: "FeatureCollection",
                        features: geoJsonData.geojson.pontos.features.filter(
                          (f) => f.properties?.type === "hexagon"
                        ),
                      }
                    : []
                }
                selectedMineral={selectedMineral}
                data={data}
                mineralColorConfig={mineralColorConfig}
              />
            </div>
          )}
        </div>
      </div>

      {/* Modal de Preview e Upload */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>Preview dos Dados</DialogTitle>
            <DialogDescription>
              Visualização do arquivo: {fileName}
            </DialogDescription>
          </DialogHeader>
          <div>
            <Tabs defaultValue="complete">
              <TabsList>
                <TabsTrigger value="complete">Dados Completos</TabsTrigger>
              </TabsList>

              <TabsContent value="complete" className="overflow-hidden">
                <ScrollArea className="h-[500px] rounded-md border">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {columns.map((column) => (
                            <TableHead key={column.accessorKey}>
                              {column.header}
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {data.map((row, rowIndex) => (
                          <TableRow key={rowIndex}>
                            {columns.map((column) => (
                              <TableCell key={column.accessorKey}>
                                {row[column.accessorKey]}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </div>
          <DialogFooter className="mt-4 flex justify-end">
            <Button onClick={handleSave} className="w-full sm:w-auto">
              Confirmar e Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Visualização dos Dados */}
      <Dialog open={isDataModalOpen} onOpenChange={setIsDataModalOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Visualização dos Dados</DialogTitle>
            <DialogDescription>Dados do arquivo: {fileName}</DialogDescription>
          </DialogHeader>
          <div className="overflow-hidden">
            <Tabs defaultValue="preview">
              <TabsList>
                <TabsTrigger value="preview">Preview</TabsTrigger>
                <TabsTrigger value="complete">Dados Completos</TabsTrigger>
              </TabsList>
              <TabsContent value="preview" className="overflow-hidden">
                <ScrollArea className="h-[400px] rounded-md border">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {columns.map((column) => (
                            <TableHead key={column.accessorKey}>
                              {column.header}
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {data.slice(0, 10).map((row, rowIndex) => (
                          <TableRow key={rowIndex}>
                            {columns.map((column) => (
                              <TableCell key={column.accessorKey}>
                                {row[column.accessorKey]}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </ScrollArea>
              </TabsContent>
              <TabsContent value="complete">
                <ScrollArea className="h-[600px] w-full overflow-y-auto rounded-md border">
                  <div className="w-full overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {columns.map((column) => (
                            <TableHead key={column.accessorKey}>
                              {column.header}
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {data.map((row, rowIndex) => (
                          <TableRow key={rowIndex}>
                            {columns.map((column) => (
                              <TableCell key={column.accessorKey}>
                                {row[column.accessorKey]}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>

      {/* Modal de Configuração de Cores */}
      <Dialog open={isColorConfigOpen} onOpenChange={setIsColorConfigOpen}>
        <DialogContent className="max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Configuração de Cores Global</DialogTitle>
            <DialogDescription>
              Configure os intervalos de valores e cores que serão aplicados a
              todos os minerais.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {fields.length === 0 ? (
              <div className="text-center p-6 bg-muted rounded-md">
                <p className="text-muted-foreground mb-4">
                  Não há configurações de cores globais definidas.
                </p>
                <Button
                  type="button"
                  onClick={() =>
                    append({ minimo: "", maximo: "", cor: "#ffffff" })
                  }
                >
                  Adicionar Primeiro Intervalo
                </Button>
              </div>
            ) : (
              <>
                {fields.map((field, index) => (
                  <div
                    key={field.id}
                    className="relative border p-4 rounded-md shadow-sm bg-muted"
                  >
                    <div className="grid grid-cols-3 gap-4">
                      <div className="flex flex-col">
                        <label
                          htmlFor={`limites.${index}.minimo`}
                          className="mb-2"
                        >
                          De
                        </label>
                        <Input
                          type="number"
                          min={0}
                          placeholder="0.0"
                          {...register(`limites.${index}.minimo`)}
                        />
                      </div>

                      <div className="flex flex-col">
                        <label
                          htmlFor={`limites.${index}.maximo`}
                          className="mb-2"
                        >
                          Até
                        </label>
                        <Input
                          type="number"
                          min={0}
                          placeholder="0.0"
                          {...register(`limites.${index}.maximo`)}
                        />
                      </div>

                      <div className="flex flex-col">
                        <label className="mb-2">Cor</label>
                        <PickerColor
                          selectedColor={
                            watch(`limites.${index}.cor`) || "#ffffff"
                          }
                          onColorChange={(color) =>
                            setValue(`limites.${index}.cor`, color)
                          }
                        />
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => remove(index)}
                    >
                      ×
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  onClick={() =>
                    append({ minimo: "", maximo: "", cor: "#ffffff" })
                  }
                >
                  Adicionar Intervalo
                </Button>
              </>
            )}
            <DialogFooter>
              <Button
                onClick={async () => {
                  const formValues = control._formValues.limites;

                  // Validar se todos os campos estão preenchidos
                  const isValid = formValues.every(
                    (limite) => limite.minimo && limite.maximo && limite.cor
                  );

                  if (!isValid) {
                    toast({
                      title: "Campos incompletos",
                      description: "Preencha todos os campos de limite e cor.",
                      variant: "destructive",
                    });
                    return;
                  }

                  setMineralColorConfig(formValues);

                  try {
                    // Salvar na API
                    const configService =
                      new ConfiguracaoPersonalizadaService();

                    // Obter todas as configurações existentes
                    const configs = await configService.listarConfiguracoes();

                    // Excluir todas as configurações existentes
                    for (const config of configs) {
                      if (config.id) {
                        await configService.deletarConfiguracao(config.id);
                      }
                    }

                    // Para cada limite, salvar uma nova configuração
                    for (const limite of formValues) {
                      const configData = {
                        nome: "global", // Usamos "global" para indicar que é uma configuração para todos os minerais
                        limiteInferior: parseFloat(limite.minimo),
                        limiteSuperior: parseFloat(limite.maximo),
                        corHex: limite.cor,
                      };

                      await configService.salvarConfiguracao(configData);
                    }

                    // Atualizar o estado para forçar a renderização do mapa com as novas cores
                    setMineralColorConfig([...formValues]);

                    setIsColorConfigOpen(false);
                    toast({
                      title: "Configuração global salva",
                      description:
                        "As configurações de cores globais foram salvas com sucesso no servidor.",
                      variant: "success",
                    });
                  } catch (error) {
                    console.error("Erro ao salvar configuração:", error);
                    toast({
                      title: "Erro",
                      description:
                        "Ocorreu um erro ao salvar a configuração no servidor.",
                      variant: "destructive",
                    });
                  }
                }}
              >
                Salvar Configuração
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>

      {/* Modal da Calculadora Avançada */}
      <Dialog
        open={isCalculatorModalOpen}
        onOpenChange={setIsCalculatorModalOpen}
      >
        <DialogContent className="max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Calculadora Avançada</DialogTitle>
            <DialogDescription>
              Crie fórmulas personalizadas utilizando os minerais disponíveis.
            </DialogDescription>
          </DialogHeader>
          <FormulaCalculator />
        </DialogContent>
      </Dialog>

      {/* Modal do Histograma */}
      <Dialog open={isHistogramOpen} onOpenChange={setIsHistogramOpen}>
        <DialogContent className="max-w-[1000px] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Histograma - {selectedMineral}</DialogTitle>
            <DialogDescription>
              Distribuição de frequência dos valores do atributo selecionado
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {/* Controles do Histograma */}
            <div className="lg:col-span-1">
              <HistogramControls
                bins={histogramBins}
                setBins={setHistogramBins}
                selectedAttribute={selectedMineral}
                setSelectedAttribute={setSelectedMineral}
                availableAttributes={minerais}
                color={histogramColor}
                setColor={setHistogramColor}
              />
            </div>

            {/* Histograma */}
            <div className="lg:col-span-2">
              <Histogram
                data={data}
                selectedAttribute={selectedMineral}
                title={`Distribuição de ${selectedMineral}`}
                bins={histogramBins}
                color={histogramColor}
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setIsHistogramOpen(false)}>Fechar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
