"use client";
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

export const LoadingSpinner = ({ 
  size = "default",
  message,
  className,
  ...props 
}) => {
  const sizeClasses = {
    sm: "h-4 w-4",
    default: "h-8 w-8",
    lg: "h-12 w-12",
    xl: "h-16 w-16"
  };

  return (
    <div 
      className={cn(
        "flex flex-col items-center justify-center gap-2",
        className
      )}
      {...props}
    >
      <Loader2 
        className={cn(
          "animate-spin text-primary",
          sizeClasses[size]
        )}
      />
      {message && (
        <p className="text-sm text-muted-foreground text-center">
          {message}
        </p>
      )}
    </div>
  );
};

export const FullPageLoader = ({ message = "Carregando..." }) => (
  <div className="fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50">
    <LoadingSpinner size="lg" message={message} />
  </div>
);

export const InlineLoader = ({ message, className }) => (
  <div className={cn("flex items-center gap-2 py-4", className)}>
    <LoadingSpinner size="sm" />
    {message && (
      <span className="text-sm text-muted-foreground">{message}</span>
    )}
  </div>
);

export const CardLoader = ({ message = "Carregando dados..." }) => (
  <div className="flex items-center justify-center h-32 bg-muted rounded-md">
    <LoadingSpinner message={message} />
  </div>
);

export const TableLoader = ({ rows = 5, columns = 4 }) => (
  <div className="space-y-2">
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div key={rowIndex} className="flex gap-4">
        {Array.from({ length: columns }).map((_, colIndex) => (
          <div 
            key={colIndex}
            className="h-4 bg-muted rounded animate-pulse flex-1"
          />
        ))}
      </div>
    ))}
  </div>
);

export const SkeletonLoader = ({ 
  lines = 3, 
  className,
  ...props 
}) => (
  <div className={cn("space-y-2", className)} {...props}>
    {Array.from({ length: lines }).map((_, index) => (
      <div 
        key={index}
        className="h-4 bg-muted rounded animate-pulse"
        style={{ 
          width: `${Math.random() * 40 + 60}%` 
        }}
      />
    ))}
  </div>
);

export default LoadingSpinner;
