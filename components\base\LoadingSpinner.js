"use client";
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

export const LoadingSpinner = ({
  size = "default",
  color = "primary",
  message,
  className,
  ...props
}) => {
  const sizeClasses = {
    xs: "h-4 w-4",
    sm: "h-6 w-6",
    default: "h-8 w-8",
    lg: "h-12 w-12",
    xl: "h-16 w-16"
  };

  const colorClasses = {
    primary: "text-primary",
    white: "text-white",
    current: "text-current",
    muted: "text-muted-foreground"
  };

  const messageSizeClasses = {
    xs: "text-xs",
    sm: "text-sm",
    default: "text-sm",
    lg: "text-base",
    xl: "text-lg"
  };

  const messageColorClasses = {
    primary: "text-foreground",
    white: "text-white/90",
    current: "text-current",
    muted: "text-muted-foreground"
  };

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center gap-3",
        className
      )}
      {...props}
    >
      <Loader2
        className={cn(
          "animate-spin transition-all duration-200",
          sizeClasses[size],
          colorClasses[color]
        )}
      />
      {message && (
        <p className={cn(
          "text-center font-medium max-w-xs",
          messageSizeClasses[size],
          messageColorClasses[color === "white" ? "white" : "muted"]
        )}>
          {message}
        </p>
      )}
    </div>
  );
};

export const FullPageLoader = ({
  message = "Carregando...",
  size = "lg",
  className
}) => (
  <div className={cn(
    "fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50",
    className
  )}>
    <div className="bg-background/95 backdrop-blur-sm rounded-xl shadow-lg border p-8 max-w-sm mx-4">
      <LoadingSpinner size={size} message={message} />
    </div>
  </div>
);

export const InlineLoader = ({
  message,
  size = "sm",
  orientation = "horizontal",
  className
}) => {
  if (orientation === "vertical") {
    return (
      <div className={cn("flex flex-col items-center gap-3 py-6", className)}>
        <LoadingSpinner size={size} />
        {message && (
          <span className="text-sm text-muted-foreground text-center">{message}</span>
        )}
      </div>
    );
  }

  return (
    <div className={cn("flex items-center gap-3 py-4", className)}>
      <LoadingSpinner size={size} />
      {message && (
        <span className="text-sm text-muted-foreground">{message}</span>
      )}
    </div>
  );
};

export const CardLoader = ({
  message = "Carregando dados...",
  size = "default",
  height = "h-32",
  className
}) => (
  <div className={cn(
    "flex items-center justify-center bg-muted/50 rounded-lg border-2 border-dashed border-muted-foreground/20",
    height,
    className
  )}>
    <LoadingSpinner size={size} message={message} />
  </div>
);

export const TableLoader = ({
  rows = 5,
  columns = 4,
  showHeader = true,
  className
}) => (
  <div className={cn("space-y-3", className)}>
    {showHeader && (
      <div className="flex gap-4 pb-2 border-b">
        {Array.from({ length: columns }).map((_, colIndex) => (
          <div
            key={`header-${colIndex}`}
            className="h-5 bg-muted rounded animate-pulse flex-1"
          />
        ))}
      </div>
    )}
    <div className="space-y-3">
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex gap-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div
              key={colIndex}
              className="h-4 bg-muted/70 rounded animate-pulse flex-1"
              style={{
                animationDelay: `${(rowIndex * columns + colIndex) * 0.1}s`
              }}
            />
          ))}
        </div>
      ))}
    </div>
  </div>
);

export const SkeletonLoader = ({
  lines = 3,
  variant = "text",
  className,
  ...props
}) => {
  const getRandomWidth = (index) => {
    // Última linha sempre menor para parecer mais natural
    if (index === lines - 1) {
      return `${Math.random() * 30 + 40}%`;
    }
    return `${Math.random() * 20 + 80}%`;
  };

  const variants = {
    text: "h-4",
    title: "h-6",
    paragraph: "h-3",
    card: "h-24"
  };

  return (
    <div className={cn("space-y-3", className)} {...props}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={cn(
            "bg-muted rounded animate-pulse",
            variants[variant]
          )}
          style={{
            width: variant === "card" ? "100%" : getRandomWidth(index),
            animationDelay: `${index * 0.2}s`
          }}
        />
      ))}
    </div>
  );
};

// Componente especializado para loading com design moderno
export const ModernLoader = ({
  message = "Carregando...",
  subtitle,
  size = "default",
  variant = "gradient",
  className
}) => {
  const variants = {
    gradient: "bg-gradient-to-r from-emerald-500 to-teal-500",
    solid: "bg-primary",
    outline: "border-2 border-primary bg-background",
    glass: "bg-background/80 backdrop-blur-sm border border-white/20"
  };

  return (
    <div className={cn(
      "flex flex-col items-center justify-center space-y-4 p-8 rounded-xl shadow-lg",
      variants[variant],
      className
    )}>
      <div className={cn(
        "p-3 rounded-full",
        variant === "gradient" || variant === "solid" ? "bg-white/20" : "bg-primary/10"
      )}>
        <LoadingSpinner
          size={size}
          color={variant === "gradient" || variant === "solid" ? "white" : "primary"}
        />
      </div>
      <div className="text-center space-y-1">
        <h3 className={cn(
          "font-semibold",
          variant === "gradient" || variant === "solid" ? "text-white" : "text-foreground",
          size === "lg" ? "text-lg" : size === "sm" ? "text-sm" : "text-base"
        )}>
          {message}
        </h3>
        {subtitle && (
          <p className={cn(
            "text-sm opacity-80",
            variant === "gradient" || variant === "solid" ? "text-white" : "text-muted-foreground"
          )}>
            {subtitle}
          </p>
        )}
      </div>
    </div>
  );
};

// Componente para loading em overlay com blur
export const OverlayLoader = ({
  message = "Carregando...",
  subtitle,
  size = "lg",
  blur = true,
  className
}) => (
  <div className={cn(
    "fixed inset-0 flex items-center justify-center z-50",
    blur ? "bg-black/50 backdrop-blur-sm" : "bg-black/30",
    className
  )}>
    <ModernLoader
      message={message}
      subtitle={subtitle}
      size={size}
      variant="glass"
      className="max-w-sm mx-4"
    />
  </div>
);

export default LoadingSpinner;
