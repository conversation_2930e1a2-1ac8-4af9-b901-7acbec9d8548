import { getLoggingConfig, IS_PRODUCTION } from '@/config/environment';

const config = getLoggingConfig();

export class Logger {
  static levels = {
    ERROR: 0,
    WARN: 1,
    INFO: 2,
    DEBUG: 3
  };

  static getLevelValue(level) {
    return this.levels[level.toUpperCase()] ?? this.levels.INFO;
  }

  static shouldLog(level) {
    const configLevel = this.getLevelValue(config.level);
    const messageLevel = this.getLevelValue(level);
    return messageLevel <= configLevel;
  }

  static formatMessage(level, message, context = {}) {
    const timestamp = new Date().toISOString();
    const contextStr = Object.keys(context).length > 0 
      ? ` | Context: ${JSON.stringify(context)}` 
      : '';
    
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${contextStr}`;
  }

  static log(level, message, context = {}) {
    if (!this.shouldLog(level)) return;

    const formattedMessage = this.formatMessage(level, message, context);

    if (config.enableConsole) {
      this.logToConsole(level, formattedMessage, context);
    }

    if (config.enableRemote) {
      this.logToRemote(level, message, context);
    }
  }

  static logToConsole(level, message, context) {
    const method = level === 'error' ? 'error' : 
                   level === 'warn' ? 'warn' : 
                   level === 'debug' ? 'debug' : 'log';
    
    console[method](message);
    
    if (Object.keys(context).length > 0) {
      console[method]('Context:', context);
    }
  }

  static logToRemote(level, message, context) {
    // TODO: Implement remote logging service
    // This could send logs to services like LogRocket, Sentry, etc.
    if (IS_PRODUCTION) {
      // Example: Send to monitoring service
      // monitoringService.log({ level, message, context, timestamp: new Date() });
    }
  }

  static error(message, context = {}) {
    this.log('error', message, context);
  }

  static warn(message, context = {}) {
    this.log('warn', message, context);
  }

  static info(message, context = {}) {
    this.log('info', message, context);
  }

  static debug(message, context = {}) {
    this.log('debug', message, context);
  }

  static performance(label, startTime) {
    if (!config.enablePerformance) return;

    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.debug(`Performance: ${label}`, { 
      duration: `${duration.toFixed(2)}ms`,
      startTime,
      endTime
    });
  }

  static apiRequest(method, url, data = null) {
    this.debug(`API Request: ${method.toUpperCase()} ${url}`, { 
      method, 
      url, 
      data: data ? JSON.stringify(data) : null 
    });
  }

  static apiResponse(method, url, status, data = null) {
    const level = status >= 400 ? 'error' : 'debug';
    this.log(level, `API Response: ${method.toUpperCase()} ${url} - ${status}`, { 
      method, 
      url, 
      status,
      data: data ? JSON.stringify(data) : null 
    });
  }

  static userAction(action, details = {}) {
    this.info(`User Action: ${action}`, details);
  }

  static componentError(componentName, error, props = {}) {
    this.error(`Component Error: ${componentName}`, {
      error: error.message,
      stack: error.stack,
      props
    });
  }

  static hookError(hookName, error, dependencies = []) {
    this.error(`Hook Error: ${hookName}`, {
      error: error.message,
      stack: error.stack,
      dependencies
    });
  }

  static serviceError(serviceName, method, error, params = {}) {
    this.error(`Service Error: ${serviceName}.${method}`, {
      error: error.message,
      stack: error.stack,
      params
    });
  }
}

// Convenience functions
export const logError = (message, context) => Logger.error(message, context);
export const logWarn = (message, context) => Logger.warn(message, context);
export const logInfo = (message, context) => Logger.info(message, context);
export const logDebug = (message, context) => Logger.debug(message, context);

// Performance logging
export const logPerformance = (label, startTime) => Logger.performance(label, startTime);

// API logging
export const logApiRequest = (method, url, data) => Logger.apiRequest(method, url, data);
export const logApiResponse = (method, url, status, data) => Logger.apiResponse(method, url, status, data);

// User action logging
export const logUserAction = (action, details) => Logger.userAction(action, details);

// Error logging helpers
export const logComponentError = (componentName, error, props) => Logger.componentError(componentName, error, props);
export const logHookError = (hookName, error, dependencies) => Logger.hookError(hookName, error, dependencies);
export const logServiceError = (serviceName, method, error, params) => Logger.serviceError(serviceName, method, error, params);

export default Logger;
