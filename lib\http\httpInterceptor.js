/**
 * HttpInterceptor class for intercepting HTTP requests and responses
 * This class allows adding pre-request and post-response processing
 */
class HttpInterceptor {
    constructor() {
        this.requestInterceptors = [];
        this.responseInterceptors = [];
    }

    /**
     * Add a request interceptor
     * @param {Function} interceptor - Function that receives and can modify request config
     * @returns {number} - Index of the interceptor for later removal
     */
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
        return this.requestInterceptors.length - 1;
    }

    /**
     * Add a response interceptor
     * @param {Function} interceptor - Function that receives and can modify response
     * @returns {number} - Index of the interceptor for later removal
     */
    addResponseInterceptor(interceptor) {
        this.responseInterceptors.push(interceptor);
        return this.responseInterceptors.length - 1;
    }

    /**
     * Remove a request interceptor
     * @param {number} index - Index of the interceptor to remove
     */
    removeRequestInterceptor(index) {
        if (index >= 0 && index < this.requestInterceptors.length) {
            this.requestInterceptors.splice(index, 1);
        }
    }

    /**
     * Remove a response interceptor
     * @param {number} index - Index of the interceptor to remove
     */
    removeResponseInterceptor(index) {
        if (index >= 0 && index < this.responseInterceptors.length) {
            this.responseInterceptors.splice(index, 1);
        }
    }

    /**
     * Process request config through all request interceptors
     * @param {Object} config - Request configuration
     * @returns {Object} - Modified request configuration
     */
    processRequest(config) {
        let processedConfig = { ...config };
        
        for (const interceptor of this.requestInterceptors) {
            processedConfig = interceptor(processedConfig);
        }
        
        return processedConfig;
    }

    /**
     * Process response through all response interceptors
     * @param {Response} response - Fetch API Response object
     * @returns {Promise<Response>} - Modified response
     */
    async processResponse(response) {
        let processedResponse = response;
        
        for (const interceptor of this.responseInterceptors) {
            processedResponse = await interceptor(processedResponse);
        }
        
        return processedResponse;
    }
}

// Create a singleton instance
const interceptor = new HttpInterceptor();

export default interceptor;
