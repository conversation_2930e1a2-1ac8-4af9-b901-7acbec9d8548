module.exports = {
  extends: [
    'next/core-web-vitals',
    'eslint:recommended',
    '@typescript-eslint/recommended'
  ],
  rules: {
    // Clean Code Rules
    'max-len': ['error', { code: 100, ignoreUrls: true }],
    'max-lines': ['error', { max: 300, skipBlankLines: true }],
    'max-lines-per-function': ['error', { max: 20, skipBlankLines: true }],
    'max-params': ['error', 3],
    'max-depth': ['error', 3],
    'complexity': ['error', 5],
    
    // SOLID Principles
    'prefer-const': 'error',
    'no-var': 'error',
    'no-duplicate-imports': 'error',
    'no-unused-vars': 'error',
    
    // Object Calisthenics
    'no-else-return': 'error',
    'prefer-early-return': 'off', // Custom rule would be needed
    'one-var': ['error', 'never'],
    'brace-style': ['error', '1tbs', { allowSingleLine: false }],
    
    // React Best Practices
    'react/jsx-no-duplicate-props': 'error',
    'react/jsx-uses-react': 'off',
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off',
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    
    // Import Organization
    'import/order': [
      'error',
      {
        groups: [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index'
        ],
        'newlines-between': 'never',
        alphabetize: {
          order: 'asc',
          caseInsensitive: true
        }
      }
    ],
    
    // Naming Conventions
    'camelcase': ['error', { properties: 'never' }],
    
    // Code Quality
    'no-console': ['warn', { allow: ['warn', 'error'] }],
    'no-debugger': 'error',
    'no-alert': 'error',
    'prefer-template': 'error',
    'template-curly-spacing': ['error', 'never'],
    
    // Function Rules
    'func-style': ['error', 'expression'],
    'prefer-arrow-callback': 'error',
    'arrow-spacing': 'error',
    
    // Object and Array Rules
    'object-shorthand': 'error',
    'prefer-destructuring': ['error', {
      array: true,
      object: true
    }],
    
    // Spacing and Formatting
    'indent': ['error', 2],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    'comma-dangle': ['error', 'never'],
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
    
    // JSX Rules
    'react/jsx-indent': ['error', 2],
    'react/jsx-indent-props': ['error', 2],
    'react/jsx-closing-bracket-location': 'error',
    'react/jsx-closing-tag-location': 'error',
    'react/jsx-curly-spacing': ['error', 'never'],
    'react/jsx-equals-spacing': ['error', 'never'],
    'react/jsx-first-prop-new-line': ['error', 'multiline'],
    'react/jsx-max-props-per-line': ['error', { maximum: 1, when: 'multiline' }],
    'react/jsx-no-useless-fragment': 'error',
    'react/jsx-pascal-case': 'error',
    'react/jsx-props-no-multi-spaces': 'error',
    'react/jsx-tag-spacing': 'error',
    'react/jsx-wrap-multilines': 'error'
  },
  
  overrides: [
    {
      files: ['**/*.test.js', '**/*.test.jsx', '**/*.spec.js', '**/*.spec.jsx'],
      rules: {
        'max-lines-per-function': 'off',
        'max-lines': 'off'
      }
    },
    {
      files: ['**/types/**/*.js', '**/types/**/*.ts'],
      rules: {
        'max-lines': ['error', { max: 500 }]
      }
    }
  ]
};
