{"name": "coleta_new_front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p ${PORT:-8080}", "lint": "next lint"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-tooltip": "^1.1.7", "@react-google-maps/api": "^2.20.6", "@tanstack/react-table": "^8.13.2", "@turf/turf": "^7.2.0", "canvas-datagrid": "^0.4.7", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.1", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "lucide-react": "^0.474.0", "next": "^15.3.2", "or": "^0.2.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.2.3", "react-google-maps": "^9.4.5", "react-hook-form": "^7.54.2", "recharts": "^2.15.3", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"postcss": "^8", "tailwindcss": "^3.4.1"}}