import httpClient from "../http/httpClient";

export default class ConfiguracaoPersonalizadaService {
    async salvarConfiguracao(data) {
        const response = await httpClient.post("configuracao-personalizada", data, true);
        if (response.status !== 200) {
            return false;
        }
        return true;
    }

    async listarConfiguracoes() {
        const response = await httpClient.get("configuracao-personalizada", true);
        if (response.status !== 200) {
            return [];
        }
        const data = await response.json();
        return data;
    }

    async obterConfiguracao(id) {
        const response = await httpClient.get(`configuracao-personalizada/${id}`, true);
        if (response.status !== 200) {
            return null;
        }
        const data = await response.json();
        return data;
    }

    async deletarConfiguracao(id) {
        const response = await httpClient.delete(`configuracao-personalizada/${id}`, null, true);
        if (response.status !== 200) {
            return false;
        }
        return true;
    }
}
