import { PaginatedService } from './base/BaseService';
import { UserRole } from '@/types';

export class UserService extends PaginatedService {
  constructor() {
    super(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001', 'usuario');
  }

  async getFuncionarios(params = {}) {
    return this.get('funcionario', params);
  }

  async createFuncionario(funcionarioData) {
    return this.post('cadastrar/funcionario', funcionarioData);
  }

  async updateFuncionario(id, funcionarioData) {
    return this.put(`funcionario/${id}`, funcionarioData);
  }

  async deleteFuncionario(id) {
    return this.delete(`funcionario/${id}`);
  }

  async getFuncionarioById(id) {
    return this.get(`funcionario/${id}`);
  }

  async getClientes(params = {}) {
    return this.get('cliente', params);
  }

  async createCliente(clienteData) {
    return this.post('cadastrar/cliente', clienteData);
  }

  async updateCliente(id, clienteData) {
    return this.put(`cliente/${id}`, clienteData);
  }

  async deleteCliente(id) {
    return this.delete(`cliente/${id}`);
  }

  async getClienteById(id) {
    return this.get(`cliente/${id}`);
  }

  async getUsersByRole(role) {
    return this.get('', { role });
  }

  async activateUser(id) {
    return this.post(`${id}/activate`);
  }

  async deactivateUser(id) {
    return this.post(`${id}/deactivate`);
  }

  async resetUserPassword(id) {
    return this.post(`${id}/reset-password`);
  }

  async getUserPermissions(id) {
    return this.get(`${id}/permissions`);
  }

  async updateUserPermissions(id, permissions) {
    return this.put(`${id}/permissions`, { permissions });
  }

  async searchUsers(query, role = null) {
    const params = { q: query };
    if (role) params.role = role;
    return this.get('search', params);
  }

  async getUserStats() {
    return this.get('stats');
  }

  async bulkCreateUsers(usersData) {
    return this.post('bulk-create', { users: usersData });
  }

  async exportUsers(format = 'csv', filters = {}) {
    return this.get('export', { format, ...filters });
  }

  async importUsers(file) {
    const formData = new FormData();
    formData.append('file', file);
    return this.postFormData('import', formData);
  }

  validateUserData(userData, role) {
    const errors = {};

    if (!userData.nomeCompleto?.trim()) {
      errors.nomeCompleto = 'Nome completo é obrigatório';
    }

    if (!userData.email?.trim()) {
      errors.email = 'Email é obrigatório';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email)) {
      errors.email = 'Email inválido';
    }

    if (role === UserRole.FUNCIONARIO) {
      if (!userData.cpf?.trim()) {
        errors.cpf = 'CPF é obrigatório para funcionários';
      }
      
      if (!userData.telefone?.trim()) {
        errors.telefone = 'Telefone é obrigatório para funcionários';
      }
    }

    if (role === UserRole.CLIENTE) {
      if (!userData.cnpj?.trim()) {
        errors.cnpj = 'CNPJ é obrigatório para clientes';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  formatUserForDisplay(user) {
    return {
      ...user,
      displayName: user.nomeCompleto || user.email,
      roleLabel: this.getRoleLabel(user.role),
      statusLabel: user.ativo ? 'Ativo' : 'Inativo',
      createdAtFormatted: new Date(user.createdAt).toLocaleDateString('pt-BR')
    };
  }

  getRoleLabel(role) {
    const roleLabels = {
      [UserRole.ADMIN]: 'Administrador',
      [UserRole.FUNCIONARIO]: 'Funcionário',
      [UserRole.CLIENTE]: 'Cliente'
    };

    return roleLabels[role] || 'Desconhecido';
  }

  getUserInitials(user) {
    if (!user.nomeCompleto) return '??';
    
    return user.nomeCompleto
      .split(' ')
      .map(name => name.charAt(0))
      .slice(0, 2)
      .join('')
      .toUpperCase();
  }

  canUserPerformAction(user, action) {
    if (!user || !user.role) return false;

    const permissions = {
      [UserRole.ADMIN]: ['create', 'read', 'update', 'delete', 'manage'],
      [UserRole.FUNCIONARIO]: ['create', 'read', 'update'],
      [UserRole.CLIENTE]: ['read']
    };

    return permissions[user.role]?.includes(action) || false;
  }
}

export default UserService;
