# Stage 1: Build
FROM node:20-alpine AS builder

WORKDIR /app

# Copie apenas os arquivos de dependências
COPY package.json ./

# Copie package-lock.json se existir, sen<PERSON> ignore
COPY package-lock.json* ./

# Instale as dependências com cache
# Use npm install se npm ci falhar devido a inconsistências
RUN npm ci --legacy-peer-deps || npm install --legacy-peer-deps

# Copie o restante do código
COPY . .

# Adicione configuração para ignorar erros de build
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# Build da aplicação com flags para ignorar erros
RUN npm run build

# Stage 2: Runtime
FROM node:20-alpine AS runner

WORKDIR /app

# Defina para ambiente de produção
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
# A URL do backend com valor padrão
ENV NEXT_PUBLIC_API_URL=https://apis-api-coleta.w4dxlp.easypanel.host/api/
# Configuração de porta para o Next.js
ENV PORT=8080

# Copie apenas os arquivos necessários
COPY --from=builder /app/next.config.mjs ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# Exponha a porta correta
EXPOSE 8080

# Comando para iniciar a aplicação em produção
CMD ["node_modules/.bin/next", "start", "-p", "8080"]
