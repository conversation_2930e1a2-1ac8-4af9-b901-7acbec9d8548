//const baseUrl = 'https://apis-api-coleta.uwqcav.easypanel.host/api/';
const baseUrl = "https://apis-api-coleta.uwqcav.easypanel.host/api/";
import Cookies from "js-cookie";
import interceptor from "./httpInterceptor";

const getToken = () => Cookies.get("token");

/**
 * Execute HTTP request with interceptors
 * @param {string} url - Full URL for the request
 * @param {Object} config - Fetch API configuration
 * @returns {Promise<Response>} - Fetch API Response
 */
const executeRequest = async (url, config) => {
  // Process request through interceptors
  const processedConfig = interceptor.processRequest(config);

  // Execute the request
  const response = await fetch(url, processedConfig);

  // Process response through interceptors
  return interceptor.processResponse(response);
};

const httpClient = {
  get: async (path, token = false) => {
    return executeRequest(baseUrl + path, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: token ? `Bearer ${getToken()}` : "",
      },
    });
  },
  post: async (path, data, token = false) => {
    return executeRequest(baseUrl + path, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: token ? `Bearer ${getToken()}` : "",
      },
      body: JSON.stringify(data),
    });
  },
  postFormData: async (path, data, token = false) => {
    return executeRequest(baseUrl + path, {
      method: "POST",
      body: data,
      headers: {
        Authorization: token ? `Bearer ${getToken()}` : "",
      },
    });
  },
  put: async (path, data, token = false) => {
    return executeRequest(baseUrl + path, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: token ? `Bearer ${getToken()}` : "",
      },
      body: JSON.stringify(data),
    });
  },
  delete: async (path, data, token = false) => {
    return executeRequest(baseUrl + path, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: token ? `Bearer ${getToken()}` : "",
      },
      body: JSON.stringify(data),
    });
  },
};

export default httpClient;
