"use client";
import { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

const buttonVariants = {
  variant: {
    default: "bg-primary text-primary-foreground hover:bg-primary/90",
    destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
    outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    ghost: "hover:bg-accent hover:text-accent-foreground",
    link: "text-primary underline-offset-4 hover:underline",
    success: "bg-green-600 text-white hover:bg-green-700",
    warning: "bg-yellow-600 text-white hover:bg-yellow-700"
  },
  size: {
    default: "h-10 px-4 py-2",
    sm: "h-9 rounded-md px-3",
    lg: "h-11 rounded-md px-8",
    icon: "h-10 w-10",
    xs: "h-8 rounded px-2 text-xs"
  }
};

export const Button = forwardRef(({ 
  className,
  variant = "default",
  size = "default",
  loading = false,
  disabled = false,
  children,
  icon,
  iconPosition = "left",
  ...props 
}, ref) => {
  const isDisabled = disabled || loading;

  return (
    <button
      className={cn(
        "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
        buttonVariants.variant[variant],
        buttonVariants.size[size],
        className
      )}
      ref={ref}
      disabled={isDisabled}
      {...props}
    >
      {loading && (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      )}
      
      {!loading && icon && iconPosition === "left" && (
        <span className="mr-2">{icon}</span>
      )}
      
      {children}
      
      {!loading && icon && iconPosition === "right" && (
        <span className="ml-2">{icon}</span>
      )}
    </button>
  );
});

Button.displayName = "Button";

export const IconButton = forwardRef(({ 
  icon,
  "aria-label": ariaLabel,
  size = "icon",
  ...props 
}, ref) => {
  return (
    <Button
      ref={ref}
      size={size}
      aria-label={ariaLabel}
      {...props}
    >
      {icon}
    </Button>
  );
});

IconButton.displayName = "IconButton";

export const LoadingButton = forwardRef(({ 
  loading,
  loadingText = "Carregando...",
  children,
  ...props 
}, ref) => {
  return (
    <Button
      ref={ref}
      loading={loading}
      {...props}
    >
      {loading ? loadingText : children}
    </Button>
  );
});

LoadingButton.displayName = "LoadingButton";

export const ConfirmButton = forwardRef(({ 
  onConfirm,
  confirmText = "Tem certeza?",
  children,
  ...props 
}, ref) => {
  const handleClick = (e) => {
    if (window.confirm(confirmText)) {
      onConfirm?.(e);
    }
  };

  return (
    <Button
      ref={ref}
      onClick={handleClick}
      {...props}
    >
      {children}
    </Button>
  );
});

ConfirmButton.displayName = "ConfirmButton";

export const ButtonGroup = ({ 
  children, 
  className,
  orientation = "horizontal",
  ...props 
}) => {
  const orientationClasses = {
    horizontal: "flex-row",
    vertical: "flex-col"
  };

  return (
    <div 
      className={cn(
        "inline-flex",
        orientationClasses[orientation],
        "[&>button]:rounded-none [&>button:first-child]:rounded-l-md [&>button:last-child]:rounded-r-md",
        orientation === "vertical" && "[&>button:first-child]:rounded-t-md [&>button:first-child]:rounded-l-none [&>button:last-child]:rounded-b-md [&>button:last-child]:rounded-r-none",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

export const DropdownButton = forwardRef(({ 
  children,
  dropdownContent,
  ...props 
}, ref) => {
  // This would need to be implemented with a dropdown component
  // For now, just return a regular button
  return (
    <Button ref={ref} {...props}>
      {children}
    </Button>
  );
});

DropdownButton.displayName = "DropdownButton";

export const FloatingActionButton = forwardRef(({ 
  className,
  ...props 
}, ref) => {
  return (
    <Button
      ref={ref}
      size="icon"
      className={cn(
        "fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-lg",
        className
      )}
      {...props}
    />
  );
});

FloatingActionButton.displayName = "FloatingActionButton";
