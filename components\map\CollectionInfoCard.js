"use client";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/base/Button';
import { FileText } from 'lucide-react';

const InfoItem = ({ label, value }) => {
  if (!value) return null;
  
  return (
    <div>
      <strong>{label}:</strong> {value}
    </div>
  );
};

const CollectionDetails = ({ geoJsonData }) => {
  const details = [
    {
      label: 'Responsável',
      value: geoJsonData?.usuarioResp?.nomeCompleto
    },
    {
      label: 'Tipo de Coleta',
      value: geoJsonData?.tipoColeta
    },
    {
      label: 'Tipo de Análise',
      value: geoJsonData?.tipoAnalise
    },
    {
      label: 'Área do Talhão',
      value: geoJsonData?.talhao?.area ? `${geoJsonData.talhao.area} ha` : null
    },
    {
      label: 'Nome do Talhão',
      value: geoJsonData?.talhao?.nome
    },
    {
      label: 'Observa<PERSON>',
      value: geoJsonData?.observacao
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {details.map((detail, index) => (
        <InfoItem 
          key={index}
          label={detail.label}
          value={detail.value}
        />
      ))}
    </div>
  );
};

const ActionButtons = ({ onGeneratePDF }) => (
  <div className="flex gap-2 mt-4">
    <Button 
      size="sm" 
      variant="secondary"
      icon={<FileText className="h-4 w-4" />}
      onClick={onGeneratePDF}
    >
      Gerar PDF
    </Button>
  </div>
);

export const CollectionInfoCard = ({ 
  geoJsonData, 
  onGeneratePDF 
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Informações da Coleta</CardTitle>
      </CardHeader>
      <CardContent>
        <CollectionDetails geoJsonData={geoJsonData} />
        <ActionButtons onGeneratePDF={onGeneratePDF} />
      </CardContent>
    </Card>
  );
};

export default CollectionInfoCard;
