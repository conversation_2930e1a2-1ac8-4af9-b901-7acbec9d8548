"use client";
import { AlertDialogUI } from "@/components/alertDialog";
import { PaginationUI } from "@/components/pagination";
import Tables from "@/components/tables/Tables";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select";
import { MultiSelect } from "@/components/ui/multi-select";
import { Badge } from "@/components/ui/badge";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import FuncionarioService from "@/lib/services/funcionarioService";
import VisualizarMapaService from "@/lib/services/visualizarMapa";
import { MapIcon, Pencil, Search, Trash2 } from "lucide-react";
import AccordionWithTables from "@/components/accordion/accordion";
import { AlertDialogUI } from "@/components/alertDialog";
import { PaginationUI } from "@/components/pagination";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import TalhaoService from "@/lib/services/talhaoService";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function VisualizarMapa() {
    const searchParams = useSearchParams();
    const router = useRouter();
    const { toast } = useToast();
    const [showDialog, setShowDialog] = useState(false);
    const [coletas, setColetas] = useState([]);
    const [confirmCallback, setConfirmCallback] = useState(null);
    const [totalPage, setTotalPage] = useState(0);
    const [loading, setLoading] = useState(false);
    const currentPage = Number(searchParams.get("page")) || 1;

    // Estados para os filtros
    const [filtroTipoColeta, setFiltroTipoColeta] = useState("");
    const [filtroTipoAnalise, setFiltroTipoAnalise] = useState([]);
    const [filtroFuncionarioID, setFiltroFuncionarioID] = useState("");
    const [funcionarioSearch, setFuncionarioSearch] = useState("");
    const [listaFuncionarios, setListaFuncionarios] = useState([]);

    // Dados para os selects
    const TIPO_COLETA = [
        { id: "Hexagonal", nome: 'Hexagonal (zonas)' },
        { id: "Retangular", nome: 'Retangular (zonas)' },
        { id: "PontosAmostrais", nome: 'Pontos Amostral' },
    ];

    const TIPO_ANALISE = [
        { id: "Macronutrientes", nome: 'Macronutrientes' },
        { id: "Micronutrientes", nome: 'Micronutrientes' },
        { id: "Textura", nome: 'Textura' },
        { id: "Microbiologica", nome: 'Microbiologica (Metagenomica)' },
        { id: "BioAs", nome: 'BioAs' },
        { id: "Compactacao", nome: 'Compactação' },
        { id: "Outros", nome: 'Outros' },
    ];

    const columns = [
        { headerName: "Talhão", field: "nome", renderCell: (params) => params.row.talhao.nome },
        { headerName: "Tipo Coleta", field: "tipoColeta", renderCell: (params) => params.row.tipoColeta },
        {
            headerName: "Tipo Analise", field: "tipoAnalise", renderCell: (params) => {
                // Verifica se tipoAnalise existe e é um array
                const tiposAnalise = Array.isArray(params.row.tipoAnalise) ? params.row.tipoAnalise : [];

                // Se não houver tipos de análise, exibe um texto informativo
                if (tiposAnalise.length === 0) {
                    return <span className="text-gray-400 text-sm">Nenhum</span>;
                }

                // Renderiza todos os badges dentro de um único container flex
                return (
                    <div className="flex flex-wrap gap-3">
                        {tiposAnalise.map((analise) => (
                            <Badge
                                key={analise}
                                variant="secondary"
                                className="text-xs py-0.5 px-2"
                            >
                                {analise}
                            </Badge>
                        ))}
                    </div>
                );
            }
        },
        { headerName: "Observação", field: "observacao", renderCell: (params) => params.row.observacao || "N/A" },
        { headerName: "Funcionário", field: "funcionario", renderCell: (params) => params.row.usuarioResp.nomeCompleto },
        {
            headerName: "Ações",
            field: "acoes",
            renderCell: (params) => (
                <div className="flex justify-center gap-3">
                    <Button size="sm" onClick={() => visualizarColeta(params.row.id)}>
                        <MapIcon className="w-4 h-4" />
                    </Button>
                    <Button size="sm" onClick={() => editarColeta(params.row.id)}>
                        <Pencil className="w-4 h-4" />
                    </Button>
                    <Button size="sm" onClick={() => deletarColeta(params.row.talhaoID)}>
                        <Trash2 className="w-4 h-4" />
                    </Button>
                </div>
            ),
        },
    ];

    const editarColeta = (id) => {
        router.push(`/admin/talhao/editar/${id}`);
    };

    const visualizarColeta = (id) => {
        router.push(`/admin/visualizarmapa/upload/${id}`);
    };

    const deletarColeta = async (id) => {
        setShowDialog(true);
        setConfirmCallback(() => async () => {
            setLoading(true);
            const visualizar = new VisualizarMapaService();
            const deletar = await visualizar.DeletarMapa(id);
            if (!deletar) {
                setLoading(false);
                setShowDialog(false);
                return toast({
                    title: "Erro",
                    description: "Erro ao deletar coleta",
                    variant: "destructive",
                });
            }

            toast({
                title: "Sucesso",
                description: "Coleta deletado com sucesso",
            });
            setShowDialog(false);
            fetchColetas();
        });
        setLoading(false);
    };

    // Buscar lista de funcionários para o filtro
    const fetchFuncionarios = async () => {
        try {
            const funcionarioService = new FuncionarioService();
            const response = await funcionarioService.listarFuncionarios("");
            if (response) {
                setListaFuncionarios(response.items);
            }
        } catch (error) {
            toast({
                title: "Erro",
                description: "Erro ao carregar lista de funcionários para filtro",
                variant: "destructive"
            });
        }
    };

    // Função para aplicar filtros
    const aplicarFiltros = () => {
        const params = new URLSearchParams(searchParams);

        // Adicionar filtros aos parâmetros
        if (filtroTipoColeta) params.set("tipoColeta", filtroTipoColeta);
        else params.delete("tipoColeta");

        if (filtroTipoAnalise && filtroTipoAnalise.length > 0) {
            // Adiciona cada tipo de análise como um parâmetro separado com o mesmo nome
            filtroTipoAnalise.forEach(tipo => {
                params.append("tipoAnalise", tipo);
            });
        } else {
            params.delete("tipoAnalise");
        }

        if (filtroFuncionarioID) params.set("funcionarioID", filtroFuncionarioID);
        else params.delete("funcionarioID");

        // Resetar para página 1 ao filtrar
        params.set("page", 1);

        router.push(`${window.location.pathname}?${params.toString()}`);
    };

    // Função para limpar filtros
    const limparFiltros = () => {
        setFiltroTipoColeta("");
        setFiltroTipoAnalise("");
        setFiltroFuncionarioID("");
        setFuncionarioSearch("");

        const params = new URLSearchParams();
        params.set("page", 1);
        router.push(`${window.location.pathname}?${params.toString()}`);
    };

    // Filtrar funcionários para o select
    const filterFuncionarios = funcionarioSearch
        ? listaFuncionarios.filter(funcionario =>
            funcionario.nomeCompleto.toLowerCase().includes(funcionarioSearch.toLowerCase()))
        : listaFuncionarios;

    const fetchColetas = async (page) => {
        setLoading(true);
        const response = new VisualizarMapaService();
        const coletas = await response.ListarColetas(page);
        if (!coletas) {
            setLoading(false);
            return toast({
                title: "Erro",
                description: "Erro ao buscar coletas",
                variant: "destructive"
            });
        }
        setColetas(coletas.items);
        setTotalPage(coletas.totalPages)
        setLoading(false);
    }


    useEffect(() => {
        // Carregar lista de funcionários para o filtro
        fetchFuncionarios();
    }, []);
    const [fazenda, setFazenda] = useState([]);
    const { toast } = useToast();
    const [showDialog, setShowDialog] = useState(false);
    const [confirmCallback, setConfirmCallback] = useState(null);
    const [totalPage, setTotalPage] = useState(0);
    const [loading, setLoading] = useState(false);
    const currentPage = Number(searchParams.get("page")) || 1

    const fetchTalhao = async (page) => {
        setLoading(true);
        const talhaoService = new TalhaoService();
        const talhao = await talhaoService.ListarTalhao(page);
        if (!talhao) {
            setLoading(false);
            return toast({
                title: "Erro",
                description: "Erro ao buscar talhão",
                variant: "destructive"
            });
        }
        setFazenda(talhao.items);
        setTotalPage(talhao.totalPages)
        setLoading(false);
    };

        const visualizar = (id) => {
            router.push(`/admin/visualizarmapa/visualizar/${id}`);
        };
    
        const deletar = async (id) => {
            setShowDialog(true);
            setConfirmCallback(() => async () => {
                setLoading(true);
                const talhaoService = new TalhaoService();
                const deletar = await talhaoService.DeletarTalhao(id);
                if (!deletar) {
                    setLoading(false);
                    setShowDialog(false);
                    return toast({
                        title: "Erro",
                        description: "Erro ao deletar talhão",
                        variant: "destructive",
                    });
                }
    
                toast({
                    title: "Sucesso",
                    description: "Talhão deletado com sucesso",
                });
                setShowDialog(false);
                setLoading(false);
                fetchTalhao();
            });
        };

    useEffect(() => {
        const params = new URLSearchParams();
        params.set("page", currentPage);
        router.push(`${window.location.pathname}?${params.toString()}`)
    }, []);

    useEffect(() => {
        fetchColetas(searchParams)
        fetchTalhao(searchParams)
    }, [currentPage, searchParams]);

    return (
        <div className="container  max-w-full justify-center items-center mx-auto p-6">
            <AlertDialogUI
                title="Confirmação de exclusão"
                description="Deseja realmente deletar esta coleta?"
                description="Deseja realmente deletar esta talhão?"
                showDialog={showDialog}
                setShowDialog={setShowDialog}
                onConfirm={confirmCallback}
            />
            <div className="mb-8 flex justify-between items-center">
                <div>
                    <h1 className="mt-4 text-3xl font-bold">Gerenciar Coletas</h1>
                    <p className="text-muted-foreground">
                        Lista de coletas cadastradas
                    </p>
                </div>
                <div className="flex flex-row justify-center items-center gap-2">
                    <Link className="flex items-center justify-center" href="/admin/visualizarmapa/novo">
                        <Button className="px-4">Nova Coleta</Button>
                    </Link>
                </div>
            </div>

            {/* Seção de filtros */}
            <div className="mb-6 p-4 border rounded-lg bg-gray-50">
                <h2 className="text-lg font-medium mb-3">Filtros</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <Label htmlFor="filtroTipoColeta">Tipo Coleta</Label>
                        <Select
                            value={filtroTipoColeta}
                            onValueChange={setFiltroTipoColeta}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Selecione o tipo de coleta..." />
                            </SelectTrigger>
                            <SelectContent>
                                {TIPO_COLETA.map((tipo) => (
                                    <SelectItem key={tipo.id} value={tipo.id}>
                                        {tipo.nome}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Label htmlFor="filtroTipoAnalise">Tipo Análise</Label>
                        <MultiSelect
                            options={TIPO_ANALISE}
                            selected={Array.isArray(filtroTipoAnalise) ? filtroTipoAnalise : filtroTipoAnalise ? [filtroTipoAnalise] : []}
                            onChange={(values) => setFiltroTipoAnalise(values)}
                            placeholder="Selecione o tipo de análise..."
                        />
                    </div>
                    <div>
                        <Label htmlFor="filtroFuncionario">Funcionário</Label>
                        <Select
                            value={filtroFuncionarioID}
                            onValueChange={(value) => {
                                setFiltroFuncionarioID(value);
                                const funcionarioSelecionado = listaFuncionarios.find(f => f.id === value);
                                if (funcionarioSelecionado) {
                                    setFuncionarioSearch(funcionarioSelecionado.nomeCompleto);
                                }
                            }}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Selecione um funcionário..." />
                            </SelectTrigger>
                            <SelectContent>
                                <Input
                                    type="text"
                                    placeholder="Buscar funcionário..."
                                    value={funcionarioSearch}
                                    onChange={(e) => setFuncionarioSearch(e.target.value)}
                                    className="p-2"
                                />
                                {filterFuncionarios.length > 0 ? (
                                    filterFuncionarios.map((funcionario) => (
                                        <SelectItem key={funcionario.id} value={funcionario.id}>
                                            {funcionario.nomeCompleto}
                                        </SelectItem>
                                    ))
                                ) : (
                                    <p className="p-2 text-gray-500">Nenhum funcionário encontrado.</p>
                                )}
                            </SelectContent>
                        </Select>
                    </div>
                </div>
                <div className="flex justify-end mt-3 gap-2">
                    <Button variant="outline" onClick={limparFiltros}>
                        Limpar
                    </Button>
                    <Button onClick={aplicarFiltros}>
                        <Search className="w-4 h-4 mr-2" />
                        Filtrar
                    </Button>
                </div>
            </div>

                    <h1 className="mt-4 text-3xl font-bold">Visualizar Mapa</h1>
                    <p className="text-muted-foreground">Lista de mapas cadastradas</p>
                </div>
                <div className="flex flex-row justify-center items-center gap-2">
                    <Link className="flex items-center justify-center" href="/admin/visualizarmapa/novo" />
                </div>
            </div>
            {loading ? (
                <div className="flex justify-center items-center">
                    <Spinner className="text-black" message="Carregando..." />
                </div>
            ) : (
                <>
                    <Tables data={coletas} columns={columns} />
                    <AccordionWithTables data={fazenda} isVisualizarMapa={true} visualizar={visualizar} />
                    <div className="mt-4 flex justify-end items-center">
                        <PaginationUI
                            totalPage={totalPage}
                        />
                    </div>
                </>
            )}
        </div>
    );
}