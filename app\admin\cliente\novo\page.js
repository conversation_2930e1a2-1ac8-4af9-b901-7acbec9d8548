"use client";
import Back from "@/components/back";
import ClienteForm from "@/components/forms/clienteForm";
import { Button } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import { clienteSchema } from "@/lib/schema/zod";
import ClienteService from "@/lib/services/clienteService";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft, Users } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";

export default function Cliente() {
  const {
    register,
    handleSubmit,
    setValue,
    control,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(clienteSchema),
  });

  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const onSubmit = async (data) => {
    setLoading(true);
    const clienteService = new ClienteService();
    data.cep = data.cep.replace(/\D/g, "");
    data.cpf = data.cpf.replace(/\D/g, "");
    data.telefone = data.telefone.replace(/\D/g, "");
    const cliente = await clienteService.CadastrarCliente(data);
    if (!cliente) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao cadastrar cliente",
        variant: "destructive",
      });
    }
    toast({
      title: "Sucesso",
      description: "Cliente cadastrado com sucesso",
    });
    router.push("/admin/cliente");
    setLoading(false);
  };

  return (
    <div className="min-h-screen p-4">
      {/* Header moderno - compacto */}
      <div className="mb-4 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <Back
                  icon={<ArrowLeft className="h-4 w-4" />}
                  text="Voltar"
                  href="/admin/cliente"
                />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Novo Cliente
              </h1>
              <p className="text-gray-600 text-sm">
                Cadastre um novo cliente no sistema
              </p>
            </div>
            <div className="mt-2 lg:mt-0 flex items-center">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-lg flex items-center space-x-2 text-sm">
                <Users className="h-4 w-4" />
                <span>Sistema Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Formulário moderno - compacto */}
      <div className="animate-slideInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="p-1.5 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg">
                <Users className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900">
                  Dados do Cliente
                </h3>
                <p className="text-gray-600 text-xs">
                  Preencha todas as informações obrigatórias
                </p>
              </div>
            </div>
          </div>

          <div className="p-4">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <ClienteForm
                control={control}
                register={register}
                errors={errors}
                setValue={setValue}
              />

              <div className="flex justify-end pt-4 border-t border-gray-200">
                <Button
                  type="submit"
                  disabled={loading}
                  className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-6 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center space-x-2 min-w-[120px]"
                >
                  {loading ? (
                    <>
                      <Spinner size="small" className="text-white" />
                      <span>Cadastrando...</span>
                    </>
                  ) : (
                    <>
                      <Users className="h-4 w-4" />
                      <span>Cadastrar</span>
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
