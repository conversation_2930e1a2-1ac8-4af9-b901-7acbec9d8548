"use client";
import Back from "@/components/back";
import SafraForm from "@/components/forms/safraForm";
import { Button } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import { safraSchema } from "@/lib/schema/zod";
import ClienteService from "@/lib/services/clienteService";
import FazendaService from "@/lib/services/fazendaService";
import SafraService from "@/lib/services/safraService";
import { zodResolver } from "@hookform/resolvers/zod";
import { Activity, ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { use, useEffect, useState } from "react";
import { useForm } from "react-hook-form";

export default function Safra({ params }) {
  const {
    register,
    handleSubmit,
    setValue,
    control,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(safraSchema),
  });
  const [clientes, setClientes] = useState([]);
  const [fazendas, setFazendas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [safra, setSafra] = useState({});
  const { toast } = useToast();
  const router = useRouter();
  const { id } = use(params);

  const onSubmit = async (data) => {
    setLoading(true);
    data.dataInicio = new Date(data.dataInicio).toISOString();
    data.dataFim ? (data.dataFim = new Date(data.dataFim).toISOString()) : null;
    let obj = {
      ...data,
      id: id,
    };
    const safraService = new SafraService();
    const cadastrar = await safraService.AtualizarSafra(obj);
    if (!cadastrar) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao atualizar safra",
        variant: "destructive",
      });
    }
    setLoading(false);
    toast({
      title: "Sucesso",
      description: "Safra atualizada com sucesso",
      variant: "success",
    });
    return router.push("/admin/safra");
  };

  useEffect(() => {
    fetchSafra(id);
    fetchClientes();
    fetchFazendas();
  }, [id]);

  const fetchClientes = async () => {
    setLoading(true);
    const clienteService = new ClienteService();
    const clientes = await clienteService.ListarTodosClientes();
    if (!clientes) {
      return toast({
        title: "Erro",
        description: "Erro ao listar clientes",
        variant: "destructive",
      });
    }
    setClientes(clientes);
    setLoading(false);
  };

  const fetchFazendas = async () => {
    setLoading(true);
    const fazendaService = new FazendaService();
    const fazendas = await fazendaService.ListarTodasFazendas();
    if (!fazendas) {
      return toast({
        title: "Erro",
        description: "Erro ao listar fazendas",
        variant: "destructive",
      });
    }
    setFazendas(fazendas);
    setLoading(false);
  };

  const fetchSafra = async (id) => {
    setLoading(true);
    const safraService = new SafraService();
    const safra = await safraService.BuscarSafra(id);
    if (!safra) {
      return toast({
        title: "Erro",
        description: "Erro ao buscar safra",
        variant: "destructive",
      });
    }
    safra.dataInicio = new Date(safra.dataInicio);
    safra.dataFim = safra.dataFim ? new Date(safra.dataFim) : undefined;
    setSafra(safra);
    setLoading(false);
  };

  return (
    <div className="min-h-screen p-4">
      {/* Header moderno - compacto */}
      <div className="mb-4 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <Back
                  icon={<ArrowLeft className="h-4 w-4" />}
                  text="Voltar"
                  href="/admin/safra"
                />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Editar Safra
              </h1>
              <p className="text-gray-600 text-sm">
                Atualize as informações da safra
              </p>
            </div>
            <div className="mt-2 lg:mt-0 flex items-center">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-lg flex items-center space-x-2 text-sm">
                <Activity className="h-4 w-4" />
                <span>Sistema Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo principal - compacto */}
      <div className="animate-slideInUp">
        {loading ? (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
            <div className="flex flex-col justify-center items-center space-y-3">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                <Spinner className="text-white" size="medium" />
              </div>
              <div className="text-center">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  Carregando dados da safra...
                </h3>
                <p className="text-gray-600 text-sm">
                  Aguarde enquanto buscamos as informações
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                  <Activity className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900">
                    Editar Dados da Safra
                  </h3>
                  <p className="text-gray-600 text-xs">
                    Atualize as informações necessárias
                  </p>
                </div>
              </div>
            </div>

            <div className="p-4">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <SafraForm
                  control={control}
                  initialValue={safra}
                  register={register}
                  errors={errors}
                  fazendas={fazendas}
                  clientes={clientes}
                  setValue={setValue}
                />

                <div className="flex justify-end pt-4 border-t border-gray-200">
                  <Button
                    type="submit"
                    disabled={loading}
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center space-x-2 min-w-[140px]"
                  >
                    {loading ? (
                      <>
                        <Spinner size="small" className="text-white" />
                        <span>Salvando...</span>
                      </>
                    ) : (
                      <>
                        <Activity className="h-4 w-4" />
                        <span>Salvar Alterações</span>
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
