"use client";
import { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { X, Alert<PERSON>riangle, CheckCircle, Info, XCircle } from 'lucide-react';
import { Button } from './Button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

export const Modal = forwardRef(({ 
  open,
  onOpenChange,
  children,
  className,
  ...props 
}, ref) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange} {...props}>
      <DialogContent ref={ref} className={className}>
        {children}
      </DialogContent>
    </Dialog>
  );
});

Modal.displayName = "Modal";

export const ModalHeader = ({ 
  title,
  description,
  onClose,
  className,
  ...props 
}) => {
  return (
    <DialogHeader className={className} {...props}>
      <div className="flex items-center justify-between">
        <DialogTitle>{title}</DialogTitle>
        {onClose && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-6 w-6"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
      {description && (
        <DialogDescription>{description}</DialogDescription>
      )}
    </DialogHeader>
  );
};

export const ModalBody = ({ 
  children, 
  className,
  ...props 
}) => {
  return (
    <div className={cn("py-4", className)} {...props}>
      {children}
    </div>
  );
};

export const ModalFooter = ({ 
  children, 
  className,
  align = "right",
  ...props 
}) => {
  const alignments = {
    left: "justify-start",
    center: "justify-center",
    right: "justify-end",
    between: "justify-between"
  };

  return (
    <DialogFooter className={cn(alignments[align], className)} {...props}>
      {children}
    </DialogFooter>
  );
};

export const ConfirmModal = ({ 
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  title = "Confirmar ação",
  description = "Tem certeza que deseja continuar?",
  confirmText = "Confirmar",
  cancelText = "Cancelar",
  variant = "default",
  loading = false,
  ...props 
}) => {
  const handleConfirm = () => {
    onConfirm?.();
  };

  const handleCancel = () => {
    onCancel?.();
    onOpenChange?.(false);
  };

  const icons = {
    default: null,
    warning: <AlertTriangle className="h-6 w-6 text-yellow-600" />,
    danger: <XCircle className="h-6 w-6 text-red-600" />,
    success: <CheckCircle className="h-6 w-6 text-green-600" />,
    info: <Info className="h-6 w-6 text-blue-600" />
  };

  const buttonVariants = {
    default: "default",
    warning: "warning",
    danger: "destructive",
    success: "success",
    info: "default"
  };

  return (
    <Modal open={open} onOpenChange={onOpenChange} {...props}>
      <ModalHeader title={title} />
      <ModalBody>
        <div className="flex items-start gap-3">
          {icons[variant]}
          <p className="text-sm text-muted-foreground flex-1">
            {description}
          </p>
        </div>
      </ModalBody>
      <ModalFooter>
        <Button
          variant="outline"
          onClick={handleCancel}
          disabled={loading}
        >
          {cancelText}
        </Button>
        <Button
          variant={buttonVariants[variant]}
          onClick={handleConfirm}
          loading={loading}
        >
          {confirmText}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export const FormModal = ({ 
  open,
  onOpenChange,
  onSubmit,
  title,
  description,
  children,
  submitText = "Salvar",
  cancelText = "Cancelar",
  loading = false,
  ...props 
}) => {
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit?.(e);
  };

  const handleCancel = () => {
    onOpenChange?.(false);
  };

  return (
    <Modal open={open} onOpenChange={onOpenChange} {...props}>
      <form onSubmit={handleSubmit}>
        <ModalHeader title={title} description={description} />
        <ModalBody>
          {children}
        </ModalBody>
        <ModalFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            {cancelText}
          </Button>
          <Button
            type="submit"
            loading={loading}
          >
            {submitText}
          </Button>
        </ModalFooter>
      </form>
    </Modal>
  );
};

export const InfoModal = ({ 
  open,
  onOpenChange,
  title,
  description,
  children,
  closeText = "Fechar",
  ...props 
}) => {
  const handleClose = () => {
    onOpenChange?.(false);
  };

  return (
    <Modal open={open} onOpenChange={onOpenChange} {...props}>
      <ModalHeader title={title} description={description} />
      <ModalBody>
        {children}
      </ModalBody>
      <ModalFooter>
        <Button onClick={handleClose}>
          {closeText}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export const FullScreenModal = ({ 
  open,
  onOpenChange,
  children,
  className,
  ...props 
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange} {...props}>
      <DialogContent 
        className={cn(
          "max-w-none w-screen h-screen m-0 rounded-none",
          className
        )}
      >
        {children}
      </DialogContent>
    </Dialog>
  );
};
