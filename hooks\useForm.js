import { useState, useCallback, useRef } from 'react';
import { ValidationUtils } from '@/utils/validation';

export const useFormState = (initialValues = {}, validationSchema = {}) => {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const setValue = useCallback((name, value) => {
    setValues(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when value changes
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  }, [errors]);

  const setFieldTouched = useCallback((name, isTouched = true) => {
    setTouched(prev => ({
      ...prev,
      [name]: isTouched
    }));
  }, []);

  const setFieldError = useCallback((name, error) => {
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  }, []);

  const validateField = useCallback((name, value) => {
    const fieldRules = validationSchema[name];
    if (!fieldRules) return null;

    const fieldErrors = ValidationUtils.validateField(value, fieldRules);
    return fieldErrors.length > 0 ? fieldErrors[0] : null;
  }, [validationSchema]);

  const validateForm = useCallback(() => {
    const newErrors = {};
    let isValid = true;

    Object.keys(validationSchema).forEach(fieldName => {
      const fieldValue = values[fieldName];
      const error = validateField(fieldName, fieldValue);
      
      if (error) {
        newErrors[fieldName] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return { isValid, errors: newErrors };
  }, [values, validationSchema, validateField]);

  const handleChange = useCallback((name) => (event) => {
    const value = event.target.type === 'checkbox' 
      ? event.target.checked 
      : event.target.value;
    
    setValue(name, value);
  }, [setValue]);

  const handleBlur = useCallback((name) => () => {
    setFieldTouched(name, true);
    
    const value = values[name];
    const error = validateField(name, value);
    
    if (error) {
      setFieldError(name, error);
    }
  }, [values, validateField, setFieldTouched, setFieldError]);

  const reset = useCallback((newValues = initialValues) => {
    setValues(newValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  }, [initialValues]);

  const submit = useCallback(async (onSubmit) => {
    setIsSubmitting(true);
    
    const validation = validateForm();
    if (!validation.isValid) {
      setIsSubmitting(false);
      return { success: false, errors: validation.errors };
    }

    try {
      await onSubmit(values);
      setIsSubmitting(false);
      return { success: true };
    } catch (error) {
      setIsSubmitting(false);
      return { success: false, error };
    }
  }, [values, validateForm]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    setValue,
    setFieldTouched,
    setFieldError,
    validateField,
    validateForm,
    handleChange,
    handleBlur,
    reset,
    submit,
    isValid: Object.keys(errors).length === 0,
    isDirty: Object.keys(touched).length > 0
  };
};

export const useFileUpload = (options = {}) => {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB
    allowedTypes = [],
    multiple = false
  } = options;

  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [errors, setErrors] = useState([]);
  const fileInputRef = useRef(null);

  const validateFile = useCallback((file) => {
    const errors = [];

    if (maxSize && file.size > maxSize) {
      errors.push(`Arquivo muito grande. Máximo: ${maxSize / 1024 / 1024}MB`);
    }

    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      errors.push(`Tipo de arquivo não permitido: ${file.type}`);
    }

    return errors;
  }, [maxSize, allowedTypes]);

  const addFiles = useCallback((newFiles) => {
    const fileArray = Array.from(newFiles);
    const validFiles = [];
    const fileErrors = [];

    fileArray.forEach(file => {
      const validation = validateFile(file);
      if (validation.length === 0) {
        validFiles.push(file);
      } else {
        fileErrors.push(...validation);
      }
    });

    if (multiple) {
      setFiles(prev => [...prev, ...validFiles]);
    } else {
      setFiles(validFiles.slice(0, 1));
    }

    setErrors(fileErrors);
  }, [multiple, validateFile]);

  const removeFile = useCallback((index) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  }, []);

  const clearFiles = useCallback(() => {
    setFiles([]);
    setErrors([]);
  }, []);

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleFileChange = useCallback((event) => {
    const selectedFiles = event.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      addFiles(selectedFiles);
    }
  }, [addFiles]);

  const uploadFiles = useCallback(async (uploadFunction) => {
    if (files.length === 0) return [];

    setUploading(true);
    try {
      const results = await Promise.all(
        files.map(file => uploadFunction(file))
      );
      setUploading(false);
      return results;
    } catch (error) {
      setUploading(false);
      throw error;
    }
  }, [files]);

  return {
    files,
    uploading,
    errors,
    fileInputRef,
    addFiles,
    removeFile,
    clearFiles,
    openFileDialog,
    handleFileChange,
    uploadFiles,
    hasFiles: files.length > 0,
    hasErrors: errors.length > 0
  };
};
