import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@radix-ui/react-label";
import { useState } from "react";
import { Controller } from "react-hook-form";
import { Input } from "../ui/input";
import { MultiSelect } from "../ui/multi-select";
import { Textarea } from "../ui/textarea";

// Mocked data
const TIPO_COLETA = [
  { id: "Hexagonal", nome: "Hexagonal (zonas)" },
  { id: "Retangular", nome: "Retangular (zonas)" },
  { id: "PontosAmostrais", nome: "Pontos Amostral" },
];

const TIPO_ANALISE = [
  { id: "Macronutrientes", nome: "Macronutrientes" },
  { id: "Micronutrientes", nome: "Micronutrientes" },
  { id: "Textura", nome: "Textura" },
  { id: "Microbiologica", nome: "Microbiologica (Metagenomica)" },
  { id: "BioAs", nome: "BioAs" },
  { id: "Compactacao", nome: "Compactação" },
  { id: "Outros", nome: "Outros" },
];

const PROFUNDIDADE = [
  { id: "ZeroADez", nome: "00-10 cm" },
  { id: "ZeroAVinte", nome: "00-20 cm" },
  { id: "ZeroATrinta", nome: "00-30 cm" },
  { id: "ZeroAQuarenta", nome: "00-40 cm" },
  { id: "ZeroACinquenta", nome: "00-50 cm" },
  { id: "ZeroASetenta", nome: "00-60 cm" },
  { id: "DezAVinte", nome: "10-20 cm" },
  { id: "VinteATrinta", nome: "20-30 cm" },
  { id: "TrintaAQuarenta", nome: "30-40 cm" },
  { id: "QuarentaACinquenta", nome: "40-50 cm" },
  { id: "CinquentaASetenta", nome: "50-60 cm" },
];

export default function VisualizarForm({
  control,
  register,
  errors,
  children,
  onChangeColeta,
  onChangeQuantidade,
  onChangePontos,
  funcionarios,
  clientes = [],
  fazendas = [],
  talhoes = [],
  onClienteSelect,
  onFazendaSelect,
  onTalhaoSelect,
}) {
  const [clienteSearch, setClienteSearch] = useState("");
  const [fazendaSearch, setFazendaSearch] = useState("");
  const [talhaoSearch, setTalhaoSearch] = useState("");

  const filterClientes = clienteSearch
    ? clientes.filter((cliente) =>
        cliente.nome.toLowerCase().includes(clienteSearch.toLowerCase())
      )
    : clientes;

  const filterFazendas = fazendaSearch
    ? fazendas.filter((fazenda) =>
        fazenda.nome.toLowerCase().includes(fazendaSearch.toLowerCase())
      )
    : fazendas;

  const filterTalhoes = talhaoSearch
    ? talhoes.filter((talhao) =>
        talhao.nome.toLowerCase().includes(talhaoSearch.toLowerCase())
      )
    : talhoes;

  return (
    <div className="space-y-6">
      {/* Localização Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="clienteID">Cliente</Label>
          <Controller
            name="clienteID"
            control={control}
            render={({ field }) => (
              <Select
                onValueChange={(value) => {
                  field.onChange(value);
                  onClienteSelect?.(value);
                }}
                value={field.value}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o cliente" />
                </SelectTrigger>
                <SelectContent>
                  <Input
                    type="text"
                    placeholder="Buscar cliente..."
                    value={clienteSearch}
                    onChange={(e) => setClienteSearch(e.target.value)}
                    className="p-2"
                  />
                  {filterClientes.map((cliente) => (
                    <SelectItem key={cliente.id} value={cliente.id}>
                      {cliente.nome}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.clienteID && (
            <p className="text-red-500 text-sm">*{errors.clienteID.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="fazendaID">Fazenda</Label>
          <Controller
            name="fazendaID"
            control={control}
            render={({ field }) => (
              <Select
                onValueChange={(value) => {
                  field.onChange(value);
                  onFazendaSelect?.(value);
                }}
                value={field.value}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione a fazenda" />
                </SelectTrigger>
                <SelectContent>
                  <Input
                    type="text"
                    placeholder="Buscar fazenda..."
                    value={fazendaSearch}
                    onChange={(e) => setFazendaSearch(e.target.value)}
                    className="p-2"
                  />
                  {filterFazendas.map((fazenda) => (
                    <SelectItem key={fazenda.id} value={fazenda.id}>
                      {fazenda.nome}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.fazendaID && (
            <p className="text-red-500 text-sm">*{errors.fazendaID.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="talhaoID">Talhão</Label>
          <Controller
            name="talhaoID"
            control={control}
            render={({ field }) => (
              <Select
                onValueChange={(value) => {
                  field.onChange(value);
                  onTalhaoSelect?.(value);
                }}
                value={field.value}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o talhão" />
                </SelectTrigger>
                <SelectContent>
                  <Input
                    type="text"
                    placeholder="Buscar talhão..."
                    value={talhaoSearch}
                    onChange={(e) => setTalhaoSearch(e.target.value)}
                    className="p-2"
                  />
                  {filterTalhoes.map((talhao) => (
                    <SelectItem key={talhao.id} value={talhao.id}>
                      {talhao.nome}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.talhaoID && (
            <p className="text-red-500 text-sm">*{errors.talhaoID.message}</p>
          )}
        </div>
      </div>

      {/* Informações da Coleta Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="nomeColeta">Nome da Coleta</Label>
          <Input
            id="nomeColeta"
            placeholder="Digite o nome da coleta"
            {...register("nomeColeta")}
          />
          {errors.nomeColeta && (
            <p className="text-red-500 text-sm">*{errors.nomeColeta.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="tipoAnalise">Tipo de Análise</Label>
          <Controller
            name="tipoAnalise"
            control={control}
            render={({ field }) => (
              <div>
                <MultiSelect
                  options={TIPO_ANALISE}
                  selected={field.value || []}
                  onChange={field.onChange}
                  placeholder="Selecione os tipos de análise"
                />
              </div>
            )}
          />
          {errors.tipoAnalise && (
            <p className="text-red-500 text-sm">
              *{errors.tipoAnalise.message}
            </p>
          )}
        </div>
      </div>

      {/* Configuração da Grade Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="tipoColeta">Tipo da Coleta</Label>
          <Controller
            name="tipoColeta"
            control={control}
            render={({ field }) => (
              <Select
                onValueChange={(value) => {
                  field.onChange(value);
                  onChangeColeta(value);
                }}
                value={field.value}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo de coleta" />
                </SelectTrigger>
                <SelectContent>
                  {TIPO_COLETA.map((tipo) => (
                    <SelectItem key={tipo.id} value={tipo.id}>
                      {tipo.nome}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.tipoColeta && (
            <p className="text-red-500 text-sm">*{errors.tipoColeta.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="quantidadeGrid">Área do grid (ha)</Label>
          <Input
            id="quantidadeGrid"
            placeholder="Digite a quantidade"
            {...register("quantidadeGrid")}
            onChange={(e) => onChangeQuantidade(e.target.value)}
          />
          {errors.quantidadeGrid && (
            <p className="text-red-500 text-sm">
              *{errors.quantidadeGrid.message}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor="quantidadePontos">Quantidade de Pontos</Label>
          <Input
            id="quantidadePontos"
            placeholder="Digite a quantidade"
            {...register("quantidadePontos")}
            onChange={(e) => onChangePontos?.(e.target.value)}
          />
          {errors.quantidadePontos && (
            <p className="text-red-500 text-sm">
              *{errors.quantidadePontos.message}
            </p>
          )}
        </div>
      </div>

      {/* Detalhes Adicionais Section */}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="profundidade">Profundidade</Label>
          <Controller
            name="profundidade"
            control={control}
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione a profundidade" />
                </SelectTrigger>
                <SelectContent>
                  {PROFUNDIDADE.map((prof) => (
                    <SelectItem key={prof.id} value={prof.id}>
                      {prof.nome}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.profundidade && (
            <p className="text-red-500 text-sm">
              *{errors.profundidade.message}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor="funcionarioID">Funcionário</Label>
          <Controller
            name="funcionarioID"
            control={control}
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o funcionário" />
                </SelectTrigger>
                <SelectContent>
                  {funcionarios.map((func) => (
                    <SelectItem key={func.id} value={func.id}>
                      {func.nomeCompleto}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.funcionarioID && (
            <p className="text-red-500 text-sm">
              *{errors.funcionarioID.message}
            </p>
          )}
        </div>

        <div className="md:col-span-2">
          <Label htmlFor="observacao">Observação</Label>
          <Textarea
            id="observacao"
            placeholder="Digite uma observação (opcional)"
            {...register("observacao")}
          />
          {errors.observacao && (
            <p className="text-red-500 text-sm">*{errors.observacao.message}</p>
          )}
        </div>
      </div>

      {children}
    </div>
  );
}
