"use client";
import { createContext, useContext, useReducer, useCallback } from 'react';
import { ToastVariant } from '@/types';

// Action Types
const ActionTypes = {
  ADD_NOTIFICATION: 'ADD_NOTIFICATION',
  REMOVE_NOTIFICATION: 'REMOVE_NOTIFICATION',
  C<PERSON>AR_ALL_NOTIFICATIONS: 'C<PERSON>AR_ALL_NOTIFICATIONS',
  MARK_AS_READ: 'MARK_AS_READ',
  MARK_ALL_AS_READ: 'MARK_ALL_AS_READ'
};

// Initial State
const initialState = {
  notifications: []
};

// Reducer
const notificationReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.ADD_NOTIFICATION:
      return {
        ...state,
        notifications: [action.payload, ...state.notifications]
      };

    case ActionTypes.REMOVE_NOTIFICATION:
      return {
        ...state,
        notifications: state.notifications.filter(
          notification => notification.id !== action.payload
        )
      };

    case ActionTypes.CLEAR_ALL_NOTIFICATIONS:
      return {
        ...state,
        notifications: []
      };

    case ActionTypes.MARK_AS_READ:
      return {
        ...state,
        notifications: state.notifications.map(notification =>
          notification.id === action.payload
            ? { ...notification, read: true }
            : notification
        )
      };

    case ActionTypes.MARK_ALL_AS_READ:
      return {
        ...state,
        notifications: state.notifications.map(notification => ({
          ...notification,
          read: true
        }))
      };

    default:
      return state;
  }
};

// Context
const NotificationContext = createContext();

// Provider Component
export const NotificationProvider = ({ children }) => {
  const [state, dispatch] = useReducer(notificationReducer, initialState);

  const addNotification = useCallback((notification) => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const newNotification = {
      id,
      timestamp: new Date(),
      read: false,
      variant: ToastVariant.DEFAULT,
      ...notification
    };

    dispatch({ 
      type: ActionTypes.ADD_NOTIFICATION, 
      payload: newNotification 
    });

    // Auto-remove notification after duration (if specified)
    if (notification.duration) {
      setTimeout(() => {
        removeNotification(id);
      }, notification.duration);
    }

    return id;
  }, []);

  const removeNotification = useCallback((id) => {
    dispatch({ type: ActionTypes.REMOVE_NOTIFICATION, payload: id });
  }, []);

  const clearAllNotifications = useCallback(() => {
    dispatch({ type: ActionTypes.CLEAR_ALL_NOTIFICATIONS });
  }, []);

  const markAsRead = useCallback((id) => {
    dispatch({ type: ActionTypes.MARK_AS_READ, payload: id });
  }, []);

  const markAllAsRead = useCallback(() => {
    dispatch({ type: ActionTypes.MARK_ALL_AS_READ });
  }, []);

  // Convenience methods for different notification types
  const showSuccess = useCallback((title, description, options = {}) => {
    return addNotification({
      title,
      description,
      variant: ToastVariant.SUCCESS,
      duration: 5000,
      ...options
    });
  }, [addNotification]);

  const showError = useCallback((title, description, options = {}) => {
    return addNotification({
      title,
      description,
      variant: ToastVariant.DESTRUCTIVE,
      duration: 8000,
      ...options
    });
  }, [addNotification]);

  const showWarning = useCallback((title, description, options = {}) => {
    return addNotification({
      title,
      description,
      variant: ToastVariant.WARNING,
      duration: 6000,
      ...options
    });
  }, [addNotification]);

  const showInfo = useCallback((title, description, options = {}) => {
    return addNotification({
      title,
      description,
      variant: ToastVariant.DEFAULT,
      duration: 5000,
      ...options
    });
  }, [addNotification]);

  // Computed values
  const unreadCount = state.notifications.filter(n => !n.read).length;
  const hasUnread = unreadCount > 0;
  const recentNotifications = state.notifications.slice(0, 10);

  const value = {
    // State
    notifications: state.notifications,
    unreadCount,
    hasUnread,
    recentNotifications,
    
    // Actions
    addNotification,
    removeNotification,
    clearAllNotifications,
    markAsRead,
    markAllAsRead,
    
    // Convenience methods
    showSuccess,
    showError,
    showWarning,
    showInfo
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Hook
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  
  return context;
};

// Notification Component
export const NotificationDisplay = () => {
  const { notifications, removeNotification } = useNotifications();

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.slice(0, 5).map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onRemove={() => removeNotification(notification.id)}
        />
      ))}
    </div>
  );
};

const NotificationItem = ({ notification, onRemove }) => {
  const variantStyles = {
    [ToastVariant.DEFAULT]: 'bg-background border-border',
    [ToastVariant.SUCCESS]: 'bg-green-50 border-green-200 text-green-800',
    [ToastVariant.WARNING]: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    [ToastVariant.DESTRUCTIVE]: 'bg-red-50 border-red-200 text-red-800'
  };

  return (
    <div className={`
      max-w-sm p-4 rounded-lg border shadow-lg
      ${variantStyles[notification.variant]}
      animate-in slide-in-from-right-full
    `}>
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h4 className="font-medium">{notification.title}</h4>
          {notification.description && (
            <p className="text-sm mt-1 opacity-90">
              {notification.description}
            </p>
          )}
        </div>
        <button
          onClick={onRemove}
          className="ml-2 text-gray-400 hover:text-gray-600"
        >
          ×
        </button>
      </div>
    </div>
  );
};
