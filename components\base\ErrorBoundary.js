"use client";
import { Component } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from './Button';

export class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // Log error to monitoring service
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // You can also log the error to an error reporting service here
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
    
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      return (
        <ErrorFallback 
          error={this.state.error}
          onRetry={this.handleRetry}
          showDetails={this.props.showDetails}
        />
      );
    }

    return this.props.children;
  }
}

const ErrorFallback = ({ 
  error, 
  onRetry, 
  showDetails = false 
}) => (
  <div className="flex flex-col items-center justify-center min-h-[400px] p-6 bg-muted/50 rounded-lg border border-destructive/20">
    <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
    
    <h2 className="text-lg font-semibold text-destructive mb-2">
      Algo deu errado
    </h2>
    
    <p className="text-sm text-muted-foreground text-center mb-4 max-w-md">
      Ocorreu um erro inesperado. Tente recarregar a página ou entre em contato com o suporte se o problema persistir.
    </p>

    {showDetails && error && (
      <details className="mb-4 w-full max-w-md">
        <summary className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
          Detalhes do erro
        </summary>
        <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto max-h-32">
          {error.toString()}
        </pre>
      </details>
    )}

    <Button 
      onClick={onRetry}
      variant="outline"
      className="gap-2"
    >
      <RefreshCw className="h-4 w-4" />
      Tentar novamente
    </Button>
  </div>
);

export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  const WrappedComponent = (props) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

export const AsyncErrorBoundary = ({ 
  children, 
  onError,
  fallback 
}) => {
  return (
    <ErrorBoundary 
      onError={onError}
      fallback={fallback}
    >
      {children}
    </ErrorBoundary>
  );
};

export default ErrorBoundary;
