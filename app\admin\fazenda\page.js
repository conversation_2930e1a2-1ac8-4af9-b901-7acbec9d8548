"use client";
import { AlertDialogUI } from "@/components/alertDialog";
import { PaginationUI } from "@/components/pagination";
import Tables from "@/components/tables/Tables";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import FazendaService from "@/lib/services/fazendaService";
import {
  Filter,
  Grid3X3,
  List,
  Pencil,
  Plus,
  Search,
  Trash2,
  Users,
  X,
} from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

function FazendaContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [fazenda, setFazenda] = useState([]);
  const { toast } = useToast();
  const [showDialog, setShowDialog] = useState(false);
  const [confirmCallback, setConfirmCallback] = useState(null);
  const [totalPage, setTotalPage] = useState(0);
  const [loading, setLoading] = useState(false);

  // Estados para filtros e visualização
  const [filtroNome, setFiltroNome] = useState("");
  const [viewMode, setViewMode] = useState("cards"); // "table" ou "cards"

  const columns = [
    { headerName: "Nome", field: "nome" },
    { headerName: "Enderenco", field: "endereco" },
    {
      headerName: "Ações",
      field: "acoes",
      renderCell: (params) => (
        <div className="flex justify-center gap-2">
          <Button
            size="sm"
            onClick={() => editarFazenda(params.row.id)}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white transition-all duration-200 transform hover:scale-105 border-0"
          >
            <Pencil className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            onClick={() => deletarFazenda(params.row.id)}
            className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white transition-all duration-200 transform hover:scale-105 border-0"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ];

  const editarFazenda = (id) => {
    router.push(`/admin/fazenda/editar/${id}`);
  };

  const deletarFazenda = async (id) => {
    setShowDialog(true);
    setConfirmCallback(() => async () => {
      setLoading(true);
      const fazendaCliente = new FazendaService();
      const deletar = await fazendaCliente.DeletarFazenda(id);
      if (!deletar) {
        setShowDialog(false);
        setLoading(false);
        return toast({
          title: "Erro",
          description: "Erro ao deletar fazenda",
          variant: "destructive",
        });
      }

      toast({
        title: "Sucesso",
        description: "Fazenda deletado com sucesso",
      });
      setShowDialog(false);
      setLoading(false);
      fetchFazendas();
    });
  };

  // Função para aplicar filtros
  const aplicarFiltros = () => {
    setLoading(true);
    const params = new URLSearchParams(searchParams);

    // Adicionar filtro de nome aos parâmetros
    if (filtroNome) params.set("nome", filtroNome);
    else params.delete("nome");

    // Resetar para página 1 ao filtrar
    params.set("page", 1);

    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  // Função para limpar filtros
  const limparFiltros = () => {
    setFiltroNome("");

    const params = new URLSearchParams();
    params.set("page", 1);
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  const fetchFazendas = async (params) => {
    setLoading(true);
    const fazendaService = new FazendaService();

    // Converter o objeto URLSearchParams para string de consulta
    const queryString = params.toString();

    const fazendas = await fazendaService.ListarFazendas(queryString);
    if (!fazendas) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao buscar fazendas",
        variant: "destructive",
      });
    }
    setFazenda(fazendas.items);
    setTotalPage(fazendas.totalPages);
    setLoading(false);
  };

  useEffect(() => {
    fetchFazendas(searchParams);
  }, [searchParams]);

  const currentDate = new Date().toLocaleDateString("pt-BR", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <div className="min-h-screen p-4">
      <AlertDialogUI
        title="Confirmação de exclusão"
        description="Deseja realmente deletar esta fazenda?"
        showDialog={showDialog}
        setShowDialog={setShowDialog}
        onConfirm={confirmCallback}
      />

      {/* Header moderno */}
      <div className="mb-4 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Gestão de Fazendas
              </h1>
              <p className="text-gray-600 text-sm">
                {currentDate} • Gerencie suas fazendas cadastradas
              </p>
            </div>
            <div className="mt-2 lg:mt-0 flex items-center space-x-4">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-lg flex items-center space-x-2 text-sm">
                <Users className="h-4 w-4" />
                <span>{fazenda.length} Fazendas</span>
              </div>
              <Link href="/admin/fazenda/novo">
                <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center space-x-2">
                  <Plus className="h-4 w-4" />
                  <span>Nova Fazenda</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Seção de filtros moderna */}
      <div className="mb-4 animate-slideInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <div className="p-1 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg">
                <Filter className="h-5 w-5 text-white" />
              </div>
              <div>
                <h2 className="text-base font-bold text-gray-900">
                  Filtros de Busca
                </h2>
                <p className="text-gray-600 text-xs">
                  Refine sua pesquisa de fazendas
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="group">
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Nome da Fazenda
              </label>
              <Input
                placeholder="Digite o nome..."
                value={filtroNome}
                onChange={(e) => setFiltroNome(e.target.value)}
                className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
              />
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-2">
            <Button
              variant="outline"
              onClick={limparFiltros}
              className="border-gray-300 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2 h-9"
            >
              <X className="w-4 h-4" />
              <span>Limpar Filtros</span>
            </Button>
            <Button
              onClick={aplicarFiltros}
              className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white transition-all duration-200 transform hover:scale-105 flex items-center space-x-2 h-9"
            >
              <Search className="w-4 h-4" />
              <span>Aplicar Filtros</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="animate-fadeInUp">
        {loading ? (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
            <div className="flex flex-col justify-center items-center space-y-3">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                <Spinner className="text-white" size="medium" />
              </div>
              <div className="text-center">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  Carregando fazendas...
                </h3>
                <p className="text-gray-600 text-sm">
                  Aguarde enquanto buscamos os dados
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">
                      Lista de Fazendas
                    </h3>
                    <p className="text-gray-600 text-xs">
                      {fazenda.length} fazenda
                      {fazenda.length !== 1 ? "s" : ""} encontrada
                      {fazenda.length !== 1 ? "s" : ""}
                    </p>
                  </div>
                </div>

                {/* Botões de alternância de visualização */}
                <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
                  <Button
                    size="sm"
                    variant={viewMode === "cards" ? "default" : "ghost"}
                    onClick={() => setViewMode("cards")}
                    className={`transition-all duration-200 h-8 ${
                      viewMode === "cards"
                        ? "bg-gradient-to-r from-emerald-500 to-teal-500 text-white shadow-lg"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <Grid3X3 className="h-3 w-3 mr-1" />
                    Cards
                  </Button>
                  <Button
                    size="sm"
                    variant={viewMode === "table" ? "default" : "ghost"}
                    onClick={() => setViewMode("table")}
                    className={`transition-all duration-200 h-8 ${
                      viewMode === "table"
                        ? "bg-white shadow-sm text-gray-900"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <List className="h-3 w-3 mr-1" />
                    Tabela
                  </Button>
                </div>
              </div>
            </div>

            {fazenda.length === 0 ? (
              <div className="p-8 text-center">
                <div className="flex flex-col items-center space-y-3">
                  <div className="p-3 bg-gray-100 rounded-full">
                    <Users className="h-8 w-8 text-gray-400" />
                  </div>
                  <div>
                    <h3 className="text-base font-medium text-gray-900 mb-1">
                      Nenhuma fazenda encontrada
                    </h3>
                    <p className="text-gray-600 text-sm">
                      Não há fazendas cadastradas ou que correspondam aos
                      filtros aplicados.
                    </p>
                  </div>
                  <Link href="/admin/fazenda/novo">
                    <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white">
                      <Plus className="h-4 w-4 mr-2" />
                      Cadastrar Primeira Fazenda
                    </Button>
                  </Link>
                </div>
              </div>
            ) : viewMode === "cards" ? (
              <div className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {fazenda.map((item, index) => (
                    <div
                      key={item.id}
                      className="bg-white/80 backdrop-blur-sm rounded-lg shadow-md border border-white/20 p-4 hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] animate-fadeInUp"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <div className="p-1.5 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                            <Users className="h-4 w-4 text-white" />
                          </div>
                          <div>
                            <h3 className="text-base font-bold text-gray-900">
                              {item.nome}
                            </h3>
                            <p className="text-xs text-gray-600">
                              Fazenda #{item.id.slice(0, 8)}
                            </p>
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <Button
                            size="sm"
                            onClick={() => editarFazenda(item.id)}
                            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white transition-all duration-200 transform hover:scale-105 border-0 h-7 w-7 p-0"
                          >
                            <Pencil className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => deletarFazenda(item.id)}
                            className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white transition-all duration-200 transform hover:scale-105 border-0 h-7 w-7 p-0"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 text-gray-600">
                          <Plus className="h-3 w-3" />
                          <span className="text-sm">{item.endereco}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Tables data={fazenda} columns={columns} />
              </div>
            )}

            {totalPage > 1 && (
              <div className="p-4 border-t border-gray-200 bg-gray-50/50">
                <div className="flex justify-end items-center">
                  <PaginationUI totalPage={totalPage} />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default function Fazenda() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen p-4">
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
            <div className="flex flex-col justify-center items-center space-y-3">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                <Spinner className="text-white" size="medium" />
              </div>
              <div className="text-center">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  Carregando fazendas...
                </h3>
                <p className="text-gray-600 text-sm">
                  Aguarde enquanto buscamos os dados
                </p>
              </div>
            </div>
          </div>
        </div>
      }
    >
      <FazendaContent />
    </Suspense>
  );
}
