"use client";
import MapDrawing from "../maps/drawing";
import { useEffect, useState, useMemo } from "react";

export default function MapaContainer({
    initialValue,
    selectedMineral,
    data,
    mineralColorConfig,
    onHexagonSelect,
    selectedHexagons = [],
    mapMode = "view", // Novo prop para controlar o modo do mapa: "view", "draw", "edit", "select"
    onPolygonComplete,
    onPolygonEdit
}) {
    const [coordinates, setCoordinates] = useState({ lat: -23.5505, lng: -46.6333 });

    // Process data based on selected mineral
    const processedData = useMemo(() => {
        // Log para depuração
        console.log("processedData recalculado - mineralColorConfig:", mineralColorConfig);

        if (!data || !selectedMineral || data.length === 0) {
            return initialValue;
        }

        // Create a copy of the initialValue to avoid mutating the original
        const processedGeoJson = JSON.parse(JSON.stringify(initialValue));

        // If we have features (hexagons), process them
        if (processedGeoJson.features && processedGeoJson.features.length > 0) {
            // Map the data to the hexagons based on index
            processedGeoJson.features = processedGeoJson.features.map((feature, index) => {
                // Only process if we have data for this index
                if (data[index]) {
                    const mineralValue = data[index][selectedMineral];

                    // If we have a value for the selected mineral, update the feature
                    if (mineralValue !== undefined) {
                        // Add the mineral value to the properties
                        feature.properties = {
                            ...feature.properties,
                            mineralValue,
                            mineralName: selectedMineral,
                            mineralColorConfig: mineralColorConfig // Add color configuration for this mineral
                        };
                    }
                }
                return feature;
            });
        }
        return processedGeoJson;
    }, [initialValue, selectedMineral, data, mineralColorConfig]);

import { useEffect, useState } from "react";

export default function MapaContainer({ initialValue }) {
    const [coordinates, setCoordinates] = useState({ lat: -23.5505, lng: -46.6333 });

    useEffect(() => {
        if (initialValue && initialValue.talhoes?.[0]?.coordenadas?.[0]) {
            setCoordinates(initialValue.talhoes[0].coordenadas[0]);
        }
    }, [initialValue]);

    // Determinar as configurações com base no modo do mapa
    const getMapSettings = () => {
        switch (mapMode) {
            case "draw":
                // Modo de desenho para criar novos talhões
                return {
                    noEdit: false,
                    showDrawingControls: true,
                    allowPolygonSelection: false
                };
            case "edit":
                // Modo de edição para editar talhões existentes
                return {
                    noEdit: false,
                    showDrawingControls: false,
                    allowPolygonSelection: false
                };
            case "select":
                // Modo de seleção para selecionar hexágonos
                return {
                    noEdit: true,
                    showDrawingControls: false,
                    allowPolygonSelection: true
                };
            case "view":
            default:
                // Modo de visualização apenas
                return {
                    noEdit: true,
                    showDrawingControls: false,
                    allowPolygonSelection: false
                };
        }
    };

    const mapSettings = getMapSettings();

    // Log para depuração
    console.log("MapaContainer - mineralColorConfig:", mineralColorConfig);

    return (
        <div className="mt-4">
            <MapDrawing
                initialValue={processedData}
                coordenadas={coordinates}
                noEdit={mapSettings.noEdit}
                showDrawingControls={mapSettings.showDrawingControls}
                allowPolygonSelection={mapSettings.allowPolygonSelection}
                selectedMineral={selectedMineral}
                mineralColorConfig={mineralColorConfig}
                onHexagonSelect={onHexagonSelect}
                selectedHexagons={selectedHexagons}
                onPolygonComplete={onPolygonComplete}
                onPolygonEdit={onPolygonEdit}
    return (
        <div className="mt-4">
            <div className="mb-4">
                <h2 className="text-xl font-bold">Mapa</h2>
                <p className="text-muted-foreground">
                    Visualização do mapa do talhão selecionado.
                </p>
            </div>
            <MapDrawing
                initialValue={initialValue}
                coordenadas={coordinates}
                noEdit={true}
            />
        </div>
    );
}
