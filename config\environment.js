// Environment Configuration
export const ENV = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || 'Coleta Web',
  APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0'
};

// Environment Checks
export const IS_DEVELOPMENT = ENV.NODE_ENV === 'development';
export const IS_PRODUCTION = ENV.NODE_ENV === 'production';
export const IS_TEST = ENV.NODE_ENV === 'test';

// API Configuration based on environment
export const getApiConfig = () => {
  const baseConfig = {
    timeout: 30000,
    retries: 3
  };

  if (IS_DEVELOPMENT) {
    return {
      ...baseConfig,
      baseURL: ENV.API_URL,
      debug: true,
      timeout: 60000 // Longer timeout for development
    };
  }

  if (IS_PRODUCTION) {
    return {
      ...baseConfig,
      baseURL: ENV.API_URL,
      debug: false,
      timeout: 15000 // Shorter timeout for production
    };
  }

  return baseConfig;
};

// Feature flags based on environment
export const getFeatureFlags = () => {
  return {
    enableDebugMode: IS_DEVELOPMENT,
    enableAnalytics: IS_PRODUCTION,
    enableErrorReporting: IS_PRODUCTION,
    enablePerformanceMonitoring: IS_PRODUCTION,
    enableBetaFeatures: IS_DEVELOPMENT,
    enableConsoleLogging: !IS_PRODUCTION
  };
};

// Logging configuration
export const getLoggingConfig = () => {
  return {
    level: IS_PRODUCTION ? 'error' : 'debug',
    enableConsole: !IS_PRODUCTION,
    enableRemote: IS_PRODUCTION,
    enablePerformance: IS_DEVELOPMENT
  };
};

export default {
  ENV,
  IS_DEVELOPMENT,
  IS_PRODUCTION,
  IS_TEST,
  getApiConfig,
  getFeatureFlags,
  getLoggingConfig
};
