# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next
out

# Production
build

# Misc
.DS_Store
*.tgz
*.tar.gz

# Debug
debug
.nyc_output

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo

# IDE
.vscode
.idea

# Git
.git
.gitignore

# Documentation
*.md

# Images and assets that might be large
*.png
*.jpg
*.jpeg
*.gif
*.ico
*.svg

# Logs
logs
*.log

# Docker
Dockerfile
.dockerignore
docker-compose.yml
