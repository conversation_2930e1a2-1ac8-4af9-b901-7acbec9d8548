"use client";
import TalhaoForm from "@/components/forms/talhaoForm";
import { Button } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import { talhaoSchema } from "@/lib/schema/zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import MapaContainer from "./MapaContainer";

export default function TalhaoFormContainer({
  initialValue,
  clientes,
  fazendas,
  onSubmit,
  setLoadingFazendas,
}) {
  const {
    register,
    handleSubmit,
    setValue,
    control,
    watch,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(talhaoSchema),
    defaultValues: {
      clienteID: initialValue?.clienteID || "",
      fazendaID: initialValue?.fazendaID || "",
      talhoes: initialValue?.talhoes || [],
    },
  });
  const [polygons, setPolygons] = useState([]);
  const [loading, setLoading] = useState(false);
  const [initial, setInitial] = useState({});
  const [coordinates, setCoordinates] = useState({
    lat: -23.5505,
    lng: -46.6333,
  });
  const fileInputRef = useRef(null);

  const handlePolygonComplete = (polygon, coordinates) => {
    const area = getArea(polygon);
    setPolygons((prevPolygons) => {
      const newPolygonIndex = prevPolygons.length + 1;
      return [
        ...prevPolygons,
        {
          polygon,
          area,
          coordinates,
          name: `Talhão ${newPolygonIndex}`,
        },
      ];
    });
  };

  const handlePolygonEdit = (polygon) => {
    const area = getArea(polygon);
    const path = polygon.getPath().getArray();
    const coordinates = path.map((latLng) => ({
      lat: latLng.lat(),
      lng: latLng.lng(),
    }));

    setPolygons((prevPolygons) =>
      prevPolygons.map((item) =>
        item.polygon === polygon ? { ...item, area, coordinates } : item
      )
    );
  };

  const handleDeletePolygon = (e, polygonToDelete) => {
    polygonToDelete.setMap(null);
    setPolygons((prevPolygons) =>
      prevPolygons.filter(({ polygon }) => polygon !== polygonToDelete)
    );
  };

  const getArea = (polygon) => {
    const path = polygon.getPath().getArray();
    const areaInSqMeters =
      window.google.maps.geometry.spherical.computeArea(path);
    const areaInHectares = areaInSqMeters / 10000;
    return Math.round(areaInHectares * 100) / 100;
  };

  useEffect(() => {
    if (initialValue) {
      setInitial(initialValue);
      setValue("clienteID", initialValue.clienteID);
      setValue("fazendaID", initialValue.fazendaID);
      setValue("talhoes", initialValue.talhoes);
    } else {
      setValue(
        "talhoes",
        polygons.map((polygon) => ({
          nome: polygon.name,
          area: polygon.area,
          coordenadas: polygon.coordinates,
        }))
      );
    }
  }, [initialValue, polygons, setValue]);

  const handleCoordendas = (coor) => {
    setLoadingFazendas(true);
    setCoordinates({ lat: coor.lat, lng: coor.lng });
    setTimeout(() => {
      setLoadingFazendas(false);
    }, 2000);
  };

  const handleImportGeoJSON = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const geoJSON = JSON.parse(e.target.result);
        processGeoJSON(geoJSON);
      } catch (error) {
        console.error("Erro ao processar arquivo GeoJSON:", error);
        alert(
          "Erro ao processar arquivo GeoJSON. Verifique se o arquivo está no formato correto."
        );
      }
    };
    reader.readAsText(file);

    event.target.value = "";
  };

  const processGeoJSON = (geoJSON) => {
    if (!window.google) {
      alert("Google Maps não está carregado ainda. Tente novamente.");
      return;
    }

    const newPolygons = [];

    const processFeature = (feature) => {
      const geometry = feature.geometry;
      const properties = feature.properties || {};

      if (
        geometry &&
        (geometry.type === "Polygon" || geometry.type === "MultiPolygon")
      ) {
        let coordinatesArray = [];

        if (geometry.type === "Polygon") {
          coordinatesArray = [geometry.coordinates];
        } else if (geometry.type === "MultiPolygon") {
          coordinatesArray = geometry.coordinates;
        }

        coordinatesArray.forEach((polygonCoords, index) => {
          const coordinates = polygonCoords[0];

          const path = coordinates.map((coord) => ({
            lat: coord[1],
            lng: coord[0],
          }));

          if (
            path.length > 0 &&
            path[0].lat === path[path.length - 1].lat &&
            path[0].lng === path[path.length - 1].lng
          ) {
            path.pop();
          }

          const area = getAreaFromPath(path);

          const tempPolygon = new window.google.maps.Polygon({ paths: path });

          const polygonName =
            properties.name ||
            properties.nome ||
            properties.id ||
            `Talhão ${newPolygons.length + 1}`;

          newPolygons.push({
            polygon: tempPolygon,
            area: area,
            coordinates: path,
            name: polygonName,
            properties: properties,
          });
        });
      }
    };

    try {
      if (geoJSON.type === "FeatureCollection") {
        geoJSON.features.forEach(processFeature);
      } else if (geoJSON.type === "Feature") {
        processFeature(geoJSON);
      } else if (
        geoJSON.type === "Polygon" ||
        geoJSON.type === "MultiPolygon"
      ) {
        processFeature({ geometry: geoJSON, properties: {} });
      }

      if (newPolygons.length > 0) {
        setPolygons((prevPolygons) => [...prevPolygons, ...newPolygons]);

        const bounds = new window.google.maps.LatLngBounds();
        newPolygons.forEach((polygon) => {
          polygon.coordinates.forEach((coord) => {
            bounds.extend(new window.google.maps.LatLng(coord.lat, coord.lng));
          });
        });

        if (!bounds.isEmpty()) {
          const center = bounds.getCenter();
          setCoordinates({ lat: center.lat(), lng: center.lng() });
        }

        const totalArea = newPolygons.reduce(
          (sum, polygon) => sum + polygon.area,
          0
        );
        alert(
          `✅ Importação concluída!\n${
            newPolygons.length
          } polígono(s) importado(s)\nÁrea total: ${totalArea.toFixed(
            2
          )} hectares`
        );
      } else {
        alert(
          "❌ Nenhum polígono válido encontrado no arquivo GeoJSON.\nVerifique se o arquivo contém geometrias do tipo Polygon ou MultiPolygon."
        );
      }
    } catch (error) {
      console.error("Erro ao processar GeoJSON:", error);
      alert(
        "❌ Erro ao processar o arquivo GeoJSON. Verifique a estrutura do arquivo."
      );
    }
  };

  const getAreaFromPath = (path) => {
    if (!window.google || path.length < 3) return 0;

    const polygon = new window.google.maps.Polygon({ paths: path });
    const areaInSqMeters = window.google.maps.geometry.spherical.computeArea(
      polygon.getPath()
    );
    const areaInHectares = areaInSqMeters / 10000;
    return Math.round(areaInHectares * 100) / 100;
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <TalhaoForm
          control={control}
          children={
            <>
              <div className="mt-4 mb-4">
                <h2 className="text-xl font-bold">Desenhe o talhão no mapa</h2>
                <p className="text-muted-foreground">
                  Clique no botão "Desenhar forma" e clique no mapa para
                  desenhar o talhão.
                </p>
                <div className="flex gap-2 mt-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleImportGeoJSON}
                    className="flex items-center gap-2"
                  >
                    📁 Importar GeoJSON
                  </Button>
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".geojson,.json"
                  onChange={handleFileChange}
                  style={{ display: "none" }}
                />
              </div>
              <MapaContainer
                onPolygonComplete={handlePolygonComplete}
                onPolygonEdit={handlePolygonEdit}
                initialValue={initial}
                mapMode={initialValue ? "view" : "draw"}
              />
            </>
          }
          polygons={polygons}
          register={register}
          errors={errors}
          fazendas={fazendas}
          clientes={clientes}
          setValue={setValue}
          handleDeletePolygon={handleDeletePolygon}
          handleCoordendas={handleCoordendas}
          watch={watch}
        />
        <Button type="submit" className="mt-4 w-24" disabled={loading}>
          {loading ? <Spinner /> : initialValue ? "Editar" : "Cadastrar"}
        </Button>
      </form>
    </>
  );
}
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { talhaoSchema } from "@/lib/schema/zod";
import TalhaoForm from "@/components/forms/talhaoForm";
import MapDrawing from "../maps/drawing/index.js";
import { Button } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";

export default function TalhaoFormContainer({ initialValue, clientes, fazendas, onSubmit, setLoadingFazendas }) {
    const { register, handleSubmit, setValue, control, watch, formState: { errors } } = useForm({
        resolver: zodResolver(talhaoSchema),
        defaultValues: {
            clienteID: initialValue?.clienteID || "",
            fazendaID: initialValue?.fazendaID || "",
            talhoes: initialValue?.talhoes || [],
        }
    });
    const [polygons, setPolygons] = useState([]);
    const [loading, setLoading] = useState(false);
    const [initial, setInitial] = useState({});
    const [coordinates, setCoordinates] = useState({ lat: -23.5505, lng: -46.6333 });

    const handlePolygonComplete = (polygon, coordinates) => {
        const area = getArea(polygon);
        setPolygons((prevPolygons) => [
            ...prevPolygons,
            { polygon, area, coordinates },
        ]);
    };

    const handlePolygonEdit = (polygon) => {
        const area = getArea(polygon);
        const path = polygon.getPath().getArray();
        const coordinates = path.map(latLng => ({ lat: latLng.lat(), lng: latLng.lng() }));

        setPolygons((prevPolygons) =>
            prevPolygons.map((item) =>
                item.polygon === polygon ? { ...item, area, coordinates } : item
            )
        );
    };

    const handleDeletePolygon = (e, polygonToDelete) => {
        polygonToDelete.setMap(null);
        setPolygons((prevPolygons) => prevPolygons.filter(({ polygon }) => polygon !== polygonToDelete));
    };

    const getArea = (polygon) => {
        const path = polygon.getPath().getArray();
        const areaInSqMeters = window.google.maps.geometry.spherical.computeArea(path);
        const areaInHectares = areaInSqMeters / 10000;
        return Math.round(areaInHectares * 100) / 100;
    };

    useEffect(() => {
        if (initialValue) {
            setInitial(initialValue);
            setValue("clienteID", initialValue.clienteID);
            setValue("fazendaID", initialValue.fazendaID);
            setValue("talhoes", initialValue.talhoes);
        } else {
            setValue("talhoes", polygons.map(polygon => ({
                area: polygon.area,
                coordenadas: polygon.coordinates,
            })));
        }
    }, [initialValue, polygons, setValue]);

    const handleCoordendas = (coor) => {
        setLoadingFazendas(true);
        setCoordinates({ lat: coor.lat, lng: coor.lng });
        setTimeout(() => {
            setLoadingFazendas(false);
        }, 2000);
    };

    return (
        <>
            <form onSubmit={handleSubmit(onSubmit)}>
                <TalhaoForm
                    control={control}
                    children={
                        <>
                            <div className="mt-4 mb-4">
                                <h2 className="text-xl font-bold">Desenhe o talhão no mapa</h2>
                                <p className="text-muted-foreground">
                                    Clique no botão "Desenhar forma" e clique no mapa para desenhar o talhão.
                                </p>
                            </div>
                            <MapDrawing
                                onPolygonComplete={handlePolygonComplete}
                                onPolygonEdit={handlePolygonEdit}
                                coordenadas={coordinates}
                                initialValue={initial}
                                noEdit={initialValue}
                            />
                        </>
                    }
                    polygons={polygons}
                    register={register}
                    errors={errors}
                    fazendas={fazendas}
                    clientes={clientes}
                    setValue={setValue}
                    handleDeletePolygon={handleDeletePolygon}
                    handleCoordendas={handleCoordendas}
                    watch={watch}
                />
                <Button type="submit" className="mt-4 w-24" disabled={loading}>
                    {loading ? <Spinner /> : initialValue ? "Editar" : "Cadastrar"}
                </Button>
            </form>
        </>

    );
}
