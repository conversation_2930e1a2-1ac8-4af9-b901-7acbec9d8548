import { BaseService } from './base/BaseService';

export class AuthService extends BaseService {
  constructor() {
    super(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001', 'auth');
  }

  async login(credentials) {
    return this.post('login', credentials, false);
  }

  async register(userData) {
    return this.post('register', userData, false);
  }

  async logout() {
    return this.post('logout');
  }

  async refreshToken() {
    return this.post('refresh');
  }

  async forgotPassword(email) {
    return this.post('forgot-password', { email }, false);
  }

  async resetPassword(token, newPassword) {
    return this.post('reset-password', { token, password: newPassword }, false);
  }

  async changePassword(currentPassword, newPassword) {
    return this.post('change-password', {
      currentPassword,
      newPassword
    });
  }

  async verifyEmail(token) {
    return this.post('verify-email', { token }, false);
  }

  async resendVerification(email) {
    return this.post('resend-verification', { email }, false);
  }

  async getProfile() {
    return this.get('profile');
  }

  async updateProfile(profileData) {
    return this.put('profile', profileData);
  }

  async validateToken() {
    return this.get('validate');
  }

  isAuthenticated() {
    if (typeof window === 'undefined') return false;
    
    try {
      const Cookies = require('js-cookie');
      return Boolean(Cookies.get('token'));
    } catch {
      return false;
    }
  }

  getToken() {
    if (typeof window === 'undefined') return null;
    
    try {
      const Cookies = require('js-cookie');
      return Cookies.get('token');
    } catch {
      return null;
    }
  }

  setToken(token) {
    if (typeof window === 'undefined') return;
    
    try {
      const Cookies = require('js-cookie');
      Cookies.set('token', token, { expires: 7 });
    } catch (error) {
      console.error('Error setting token:', error);
    }
  }

  removeToken() {
    if (typeof window === 'undefined') return;
    
    try {
      const Cookies = require('js-cookie');
      Cookies.remove('token');
    } catch (error) {
      console.error('Error removing token:', error);
    }
  }

  async loginWithCredentials(email, password) {
    try {
      const response = await this.login({ email, password });
      
      if (response.token) {
        this.setToken(response.token);
      }
      
      return response;
    } catch (error) {
      throw error;
    }
  }

  async logoutUser() {
    try {
      await this.logout();
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      this.removeToken();
    }
  }
}

export default AuthService;
