"use client";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import logout from "@/lib/logout";
import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight, LogOut, Menu } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { Items } from "./items";

export function Sidebar({ onSidebarToggle }) {
  const [isOpen, setIsOpen] = useState(false);
  const [desktopOpen, setDesktopOpen] = useState(true);
  const pathname = usePathname();

  const handleDesktopToggle = () => {
    const newState = !desktopOpen;
    setDesktopOpen(newState);
    if (onSidebarToggle) {
      onSidebarToggle(newState);
    }
  };

  return (
    <>
      {/* Botão de menu mobile */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-40 lg:hidden"
        onClick={() => setIsOpen(true)}
      >
        <Menu className="h-6 w-6" />
      </Button>

      {/* Sidebar mobile */}
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetContent side="left" className="w-[300px] p-0">
          <MobileSidebar pathname={pathname} onLogout={logout} />
        </SheetContent>
      </Sheet>

      {/* Sidebar desktop com botão de esconder */}
      <aside
        className={cn(
          "fixed top-0 left-0 z-30 h-screen bg-white/80 backdrop-blur-sm border-r border-white/20 shadow-xl transition-all duration-300 hidden lg:block",
          desktopOpen ? "w-64" : "w-16"
        )}
      >
        <div className="flex h-full flex-col">
          <div className="flex items-center justify-between p-4 border-b border-white/20">
            <h4
              className={cn(
                "font-bold text-xl bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent transition-opacity duration-300",
                desktopOpen ? "opacity-100" : "opacity-0"
              )}
            >
              AgroSyste
            </h4>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleDesktopToggle}
              className="hover:bg-emerald-100 hover:text-emerald-600"
            >
              {desktopOpen ? (
                <ChevronLeft className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </div>
          <DesktopSidebar
            pathname={pathname}
            onLogout={logout}
            collapsed={!desktopOpen}
          />
        </div>
      </aside>
    </>
  );
}

function SidebarContent({ pathname, onLogout, className, collapsed }) {
  const NavItems = Items;

  return (
    <div className={cn("flex h-full flex-col", className)}>
      <ScrollArea className="flex-1 p-2">
        <nav className="grid gap-1">
          {NavItems.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              className={cn(
                "flex items-center gap-3 rounded-xl px-3 py-3 text-sm font-medium transition-all duration-200 group",
                pathname === item.href
                  ? "bg-gradient-to-r from-emerald-500 to-teal-500 text-white shadow-lg"
                  : "text-gray-700 hover:bg-emerald-50 hover:text-emerald-600",
                collapsed && "justify-center px-2"
              )}
            >
              <item.icon
                className={cn(
                  "h-5 w-5 transition-colors",
                  pathname === item.href
                    ? "text-white"
                    : "text-gray-600 group-hover:text-emerald-600"
                )}
              />
              {!collapsed && <span>{item.title}</span>}
              {!collapsed && item.label && (
                <span className="ml-auto bg-emerald-100 text-emerald-700 px-2 py-0.5 rounded-md text-xs font-medium">
                  {item.label}
                </span>
              )}
            </Link>
          ))}
        </nav>
      </ScrollArea>
      <div className="mt-auto p-2 border-t border-white/20">
        <Button
          className={cn(
            "w-full justify-start bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-lg transition-all duration-200",
            collapsed && "justify-center"
          )}
          onClick={onLogout}
        >
          <LogOut className="h-4 w-4" />
          {!collapsed && <span className="ml-2">Logout</span>}
        </Button>
      </div>
    </div>
  );
}

function MobileSidebar({ pathname, onLogout }) {
  return <SidebarContent pathname={pathname} onLogout={onLogout} />;
}

function DesktopSidebar({ pathname, onLogout, collapsed }) {
  return (
    <SidebarContent
      pathname={pathname}
      onLogout={onLogout}
      collapsed={collapsed}
    />
  );
}
