"use client"
import { useState } from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
    Sheet,
    SheetContent,
} from "@/components/ui/sheet"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Items } from "./items"
import { Menu, LogOut, ChevronLeft, ChevronRight } from "lucide-react"
import logout from "@/lib/logout"

export function Sidebar({ onSidebarToggle }) {
    const [isOpen, setIsOpen] = useState(false)
    const [desktopOpen, setDesktopOpen] = useState(true)
    const pathname = usePathname()

    const handleDesktopToggle = () => {
        const newState = !desktopOpen;
        setDesktopOpen(newState);
        if (onSidebarToggle) {
            onSidebarToggle(newState);
        }
    };

    return (
        <>
            {/* Botão de menu mobile */}
import { <PERSON>u, LogOut } from "lucide-react"
import logout from "@/lib/logout"

export function Sidebar() {
    const [isOpen, setIsOpen] = useState(false)
    const pathname = usePathname()

    return (
        <>
            <Button
                variant="ghost"
                size="icon"
                className="fixed top-4 left-4 z-40 lg:hidden"
                onClick={() => setIsOpen(true)}
            >
                <Menu className="h-6 w-6" />
            </Button>

            {/* Sidebar mobile */}
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
                <SheetContent side="left" className="w-[300px] p-0">
                    <MobileSidebar pathname={pathname} onLogout={logout} />
                </SheetContent>
            </Sheet>

            {/* Sidebar desktop com botão de esconder */}
            <aside
                className={cn(
                    "fixed top-0 left-0 z-30 h-screen border-r bg-background transition-all duration-300 hidden lg:block",
                    desktopOpen ? "w-64" : "w-16"
                )}
            >
                <div className="flex h-full flex-col">
                    <div className="flex items-center justify-between p-4 border-b">
                        <h4 className={cn(
                            "font-bold transition-opacity duration-300",
                            desktopOpen ? "opacity-100" : "opacity-0"
                        )}>
                            AgroSystem
                        </h4>
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={handleDesktopToggle}
                        >
                            {desktopOpen ? 
                                <ChevronLeft className="h-4 w-4" /> : 
                                <ChevronRight className="h-4 w-4" />
                            }
                        </Button>
                    </div>
                    <DesktopSidebar 
                        pathname={pathname} 
                        onLogout={logout} 
                        collapsed={!desktopOpen} 
                    />
                </div>
            <aside className="fixed left-0 top-0 z-30 hidden h-screen w-64 border-r bg-background lg:block">
                <DesktopSidebar pathname={pathname} onLogout={logout} />
            </aside>
        </>
    )
}

function SidebarContent({ pathname, onLogout, className, collapsed }) {
    const [NavItems, setNavItems] = useState(Items)

    return (
        <div className={cn("flex h-full flex-col", className)}>
            <ScrollArea className="flex-1 p-2">
function SidebarContent({
    pathname,
    onLogout,
    className,
}) {
    const [NavItems, setNavItems] = useState(Items);

    return (
        <div className={cn("flex h-full flex-col", className)}>
            <div className="p-4 border-b flex justify-center items-center">
                <h4 className="font-bold">AgroSystem</h4>
            </div>
            <ScrollArea className="flex-1 p-4">
                <nav className="grid gap-1">
                    {NavItems.map((item, index) => (
                        <Link
                            key={index}
                            href={item.href}
                            className={cn(
                                "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-all",
                                pathname === item.href ? "bg-accent" : "transparent",
                                item.variant === "default" ? "text-black" : "text-muted-foreground",
                                collapsed && "justify-center px-2"
                            )}
                        >
                            <item.icon className="h-4 w-4" />
                            {!collapsed && <span>{item.title}</span>}
                            {!collapsed && item.label && (
                                "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                                pathname === item.href ? "bg-accent" : "transparent",
                                item.variant === "default" ? "text-black" : "text-muted-foreground"
                            )}
                        >
                            <item.icon className="h-4 w-4" />
                            <span>{item.title}</span>
                            {item.label && (
                                <span className="ml-auto bg-primary/10 text-black px-2 py-0.5 rounded-md text-xs">
                                    {item.label}
                                </span>
                            )}
                        </Link>
                    ))}
                </nav>
            </ScrollArea>
            <div className="mt-auto p-2 border-t">
                <Button
                    className={cn("w-full justify-start", collapsed && "justify-center")}
                    onClick={onLogout}
                >
                    <LogOut className="h-4 w-4" />
                    {!collapsed && <span className="ml-2">Logout</span>}
            <div className="mt-auto p-4 border-t">
                <Button
                    
                    className="w-full justify-start"
                    onClick={onLogout}
                >
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout
                </Button>
            </div>
        </div>
    )
}

function MobileSidebar({ pathname, onLogout }) {
    return <SidebarContent pathname={pathname} onLogout={onLogout} />
}

function DesktopSidebar({ pathname, onLogout, collapsed }) {
    return <SidebarContent pathname={pathname} onLogout={onLogout} collapsed={collapsed} />
}
function DesktopSidebar({ pathname, onLogout }) {
    return <SidebarContent pathname={pathname} onLogout={onLogout} />
}
