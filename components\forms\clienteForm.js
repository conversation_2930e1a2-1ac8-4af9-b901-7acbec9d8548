import CepAdapter from "@/lib/adapter/cep";
import { maskCep, maskCpf, maskPhone } from "@/lib/mask";
import { Label } from "@radix-ui/react-label";
import { useEffect, useState } from "react";
import { Controller } from "react-hook-form";
import { Input } from "../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

export default function ClienteForm({
  control,
  register,
  errors,
  cliente,
  setValue,
}) {
  const [cep, setCep] = useState({});
  useEffect(() => {
    if (cliente) {
      setValue("nome", cliente.nome);
      setValue("cpf", maskCpf(cliente.cpf));
      setValue("telefone", maskPhone(cliente.telefone));
      setValue("email", cliente.email);
      setValue("cep", maskCep(cliente.cep));
      setValue("endereco", cliente.endereco);
      setValue("cidade", cliente.cidade);
      setValue("estado", cliente.estado);
    }
  }, [cliente]);

  const ufs = [
    "AC",
    "AL",
    "AP",
    "AM",
    "BA",
    "CE",
    "DF",
    "ES",
    "GO",
    "MA",
    "MT",
    "MS",
    "MG",
    "PA",
    "PB",
    "PR",
    "PE",
    "PI",
    "RJ",
    "RN",
    "RS",
    "RO",
    "RR",
    "SC",
    "SP",
    "SE",
    "TO",
  ];

  const buscarCep = async (data) => {
    const cepAdapter = new CepAdapter();
    const buscarcep = await cepAdapter.buscarCep(data);
    if (!buscarcep) {
      return;
    }
    setCep(buscarcep);
    setValue("endereco", buscarcep.logradouro);
    setValue("cidade", buscarcep.localidade);
    setValue("estado", buscarcep.uf);
  };

  return (
    <div className="space-y-4">
      {/* Seção: Informações Pessoais */}
      <div className="bg-gray-50/50 rounded-lg p-4 border border-gray-200">
        <div className="flex items-center space-x-2 mb-3">
          <div className="p-1 bg-gradient-to-r from-blue-500 to-blue-600 rounded">
            <div className="h-4 w-4 bg-white rounded-full"></div>
          </div>
          <div>
            <h4 className="text-base font-semibold text-gray-900">
              Informações Pessoais
            </h4>
            <p className="text-xs text-gray-600">Dados básicos do cliente</p>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div className="group">
            <Label
              htmlFor="nome"
              className="text-sm font-medium text-gray-700 mb-1 block"
            >
              Nome Completo
            </Label>
            <Input
              id="nome"
              type="text"
              {...register("nome")}
              placeholder="Ex: João da Silva"
              className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
            />
            {errors.nome && (
              <p className="text-red-500 text-xs mt-1 animate-pulse">
                *{errors.nome.message}
              </p>
            )}
          </div>
          <div className="group">
            <Label
              htmlFor="cpf"
              className="text-sm font-medium text-gray-700 mb-1 block"
            >
              CPF
            </Label>
            <Input
              id="cpf"
              type="text"
              {...register("cpf")}
              placeholder="000.000.000-00"
              onChange={(e) => setValue("cpf", maskCpf(e.target.value))}
              className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
            />
            {errors.cpf && (
              <p className="text-red-500 text-xs mt-1 animate-pulse">
                *{errors.cpf.message}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Seção: Contato */}
      <div className="bg-gray-50/50 rounded-lg p-4 border border-gray-200">
        <div className="flex items-center space-x-2 mb-3">
          <div className="p-1 bg-gradient-to-r from-green-500 to-green-600 rounded">
            <div className="h-4 w-4 bg-white rounded-full"></div>
          </div>
          <div>
            <h4 className="text-base font-semibold text-gray-900">
              Informações de Contato
            </h4>
            <p className="text-xs text-gray-600">
              Telefone e e-mail para comunicação
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div className="group">
            <Label
              htmlFor="telefone"
              className="text-sm font-medium text-gray-700 mb-1 block"
            >
              Telefone
            </Label>
            <Input
              id="telefone"
              type="text"
              {...register("telefone")}
              placeholder="(00) 00000-0000"
              onChange={(e) => setValue("telefone", maskPhone(e.target.value))}
              className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
            />
            {errors.telefone && (
              <p className="text-red-500 text-xs mt-1 animate-pulse">
                *{errors.telefone.message}
              </p>
            )}
          </div>
          <div className="group">
            <Label
              htmlFor="email"
              className="text-sm font-medium text-gray-700 mb-1 block"
            >
              E-mail
            </Label>
            <Input
              id="email"
              type="email"
              {...register("email")}
              placeholder="<EMAIL>"
              className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
            />
            {errors.email && (
              <p className="text-red-500 text-xs mt-1 animate-pulse">
                *{errors.email.message}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Seção: Endereço */}
      <div className="bg-gray-50/50 rounded-lg p-4 border border-gray-200">
        <div className="flex items-center space-x-2 mb-3">
          <div className="p-1 bg-gradient-to-r from-purple-500 to-purple-600 rounded">
            <div className="h-4 w-4 bg-white rounded-full"></div>
          </div>
          <div>
            <h4 className="text-base font-semibold text-gray-900">Endereço</h4>
            <p className="text-xs text-gray-600">
              Localização e dados de entrega
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="group">
              <Label
                htmlFor="cep"
                className="text-sm font-medium text-gray-700 mb-1 block"
              >
                CEP
              </Label>
              <Input
                id="cep"
                type="text"
                {...register("cep")}
                placeholder="00000-000"
                onBlur={(e) => buscarCep(e.target.value)}
                onChange={(e) => setValue("cep", maskCep(e.target.value))}
                className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
              />
              {errors.cep && (
                <p className="text-red-500 text-xs mt-1 animate-pulse">
                  *{errors.cep.message}
                </p>
              )}
              <p className="text-xs text-gray-500 mt-1">
                Digite o CEP para preenchimento automático
              </p>
            </div>
            <div className="group">
              <Label
                htmlFor="endereco"
                className="text-sm font-medium text-gray-700 mb-1 block"
              >
                Endereço
              </Label>
              <Input
                id="endereco"
                type="text"
                {...register("endereco")}
                placeholder="Rua Exemplo, 123"
                className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
              />
              {errors.endereco && (
                <p className="text-red-500 text-xs mt-1 animate-pulse">
                  *{errors.endereco.message}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="group">
              <Label
                htmlFor="cidade"
                className="text-sm font-medium text-gray-700 mb-1 block"
              >
                Cidade
              </Label>
              <Input
                id="cidade"
                type="text"
                {...register("cidade")}
                placeholder="Nome da Cidade"
                className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
              />
              {errors.cidade && (
                <p className="text-red-500 text-xs mt-1 animate-pulse">
                  *{errors.cidade.message}
                </p>
              )}
            </div>

            <div className="group">
              <Label
                htmlFor="estado"
                className="text-sm font-medium text-gray-700 mb-1 block"
              >
                Estado
              </Label>
              <Controller
                name="estado"
                control={control}
                rules={{ required: "Campo obrigatório" }}
                render={({ field }) => (
                  <Select onValueChange={field.onChange} value={field.value}>
                    <SelectTrigger className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9">
                      <SelectValue placeholder="Selecione o Estado" />
                    </SelectTrigger>
                    <SelectContent>
                      {ufs.map((uf, index) => (
                        <SelectItem key={index} value={uf}>
                          {uf}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.estado && (
                <p className="text-red-500 text-xs mt-1 animate-pulse">
                  *{errors.estado.message}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
