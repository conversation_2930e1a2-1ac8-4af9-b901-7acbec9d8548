import { ApiStatus, ErrorType } from '@/types';

export class Api<PERSON>rror extends Error {
  constructor(message, status, type = ErrorType.UNKNOWN) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.type = type;
  }
}

export class ApiUtils {
  static isSuccessStatus(status) {
    return status >= 200 && status < 300;
  }

  static getErrorType(status) {
    switch (status) {
      case ApiStatus.BAD_REQUEST:
        return ErrorType.VALIDATION;
      case ApiStatus.UNAUTHORIZED:
        return ErrorType.AUTHENTICATION;
      case ApiStatus.FORBIDDEN:
        return ErrorType.AUTHORIZATION;
      case ApiStatus.NOT_FOUND:
        return ErrorType.NOT_FOUND;
      case ApiStatus.CONFLICT:
        return ErrorType.VALIDATION;
      case ApiStatus.INTERNAL_ERROR:
        return ErrorType.SERVER;
      default:
        return ErrorType.UNKNOWN;
    }
  }

  static createError(message, status) {
    const type = this.getErrorType(status);
    return new ApiError(message, status, type);
  }

  static async handleResponse(response) {
    if (this.isSuccessStatus(response.status)) {
      try {
        return await response.json();
      } catch {
        return null;
      }
    }

    let errorMessage = 'Erro desconhecido';
    
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;
    } catch {
      errorMessage = response.statusText || errorMessage;
    }

    throw this.createError(errorMessage, response.status);
  }

  static buildQueryString(params) {
    if (!params || Object.keys(params).length === 0) {
      return '';
    }

    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
  }

  static buildUrl(baseUrl, endpoint, params = {}) {
    const url = `${baseUrl}/${endpoint}`;
    const queryString = this.buildQueryString(params);
    return `${url}${queryString}`;
  }

  static createHeaders(includeAuth = false, customHeaders = {}) {
    const headers = {
      'Content-Type': 'application/json',
      ...customHeaders
    };

    if (includeAuth) {
      const token = this.getAuthToken();
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
    }

    return headers;
  }

  static createFormDataHeaders(includeAuth = false, customHeaders = {}) {
    const headers = { ...customHeaders };

    if (includeAuth) {
      const token = this.getAuthToken();
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
    }

    return headers;
  }

  static getAuthToken() {
    if (typeof window === 'undefined') return null;
    
    try {
      const Cookies = require('js-cookie');
      return Cookies.get('token');
    } catch {
      return null;
    }
  }

  static async makeRequest(url, options = {}) {
    try {
      const response = await fetch(url, options);
      return await this.handleResponse(response);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw this.createError(
        error.message || 'Erro de conexão',
        0
      );
    }
  }

  static async get(url, includeAuth = false, params = {}) {
    const queryString = this.buildQueryString(params);
    const fullUrl = `${url}${queryString}`;
    
    const options = {
      method: 'GET',
      headers: this.createHeaders(includeAuth)
    };

    return this.makeRequest(fullUrl, options);
  }

  static async post(url, data, includeAuth = false) {
    const options = {
      method: 'POST',
      headers: this.createHeaders(includeAuth),
      body: JSON.stringify(data)
    };

    return this.makeRequest(url, options);
  }

  static async put(url, data, includeAuth = false) {
    const options = {
      method: 'PUT',
      headers: this.createHeaders(includeAuth),
      body: JSON.stringify(data)
    };

    return this.makeRequest(url, options);
  }

  static async delete(url, includeAuth = false) {
    const options = {
      method: 'DELETE',
      headers: this.createHeaders(includeAuth)
    };

    return this.makeRequest(url, options);
  }

  static async postFormData(url, formData, includeAuth = false) {
    const options = {
      method: 'POST',
      headers: this.createFormDataHeaders(includeAuth),
      body: formData
    };

    return this.makeRequest(url, options);
  }

  static async putFormData(url, formData, includeAuth = false) {
    const options = {
      method: 'PUT',
      headers: this.createFormDataHeaders(includeAuth),
      body: formData
    };

    return this.makeRequest(url, options);
  }
}

export const createApiService = (baseUrl) => {
  return {
    get: (endpoint, params, includeAuth = false) => 
      ApiUtils.get(`${baseUrl}/${endpoint}`, includeAuth, params),
    
    post: (endpoint, data, includeAuth = false) => 
      ApiUtils.post(`${baseUrl}/${endpoint}`, data, includeAuth),
    
    put: (endpoint, data, includeAuth = false) => 
      ApiUtils.put(`${baseUrl}/${endpoint}`, data, includeAuth),
    
    delete: (endpoint, includeAuth = false) => 
      ApiUtils.delete(`${baseUrl}/${endpoint}`, includeAuth),
    
    postFormData: (endpoint, formData, includeAuth = false) => 
      ApiUtils.postFormData(`${baseUrl}/${endpoint}`, formData, includeAuth),
    
    putFormData: (endpoint, formData, includeAuth = false) => 
      ApiUtils.putFormData(`${baseUrl}/${endpoint}`, formData, includeAuth)
  };
};
