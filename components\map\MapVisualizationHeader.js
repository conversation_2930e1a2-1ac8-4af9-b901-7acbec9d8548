"use client";
import { ArrowLeft } from 'lucide-react';
import Back from '@/components/back';

export const MapVisualizationHeader = () => {
  return (
    <div className="space-y-6">
      <Back 
        icon={<ArrowLeft className="h-4 w-4" />} 
        text="Voltar" 
        href="/admin/visualizarmapa" 
      />
      
      <div>
        <h1 className="text-3xl font-bold">
          Resultado da Análise
        </h1>
        <p className="text-muted-foreground mt-1">
          Visualize os dados coletados e análises realizadas no mapa interativo.
        </p>
      </div>
    </div>
  );
};

export default MapVisualizationHeader;
