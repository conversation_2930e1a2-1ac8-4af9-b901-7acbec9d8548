import httpClient from "../http/httpClient";

export default class RelatorioService {
    async salvarRelatorio(data) {
        const response = await httpClient.postFormData("relatorio/upload", data, true);
        if (response.status !== 200) {
            return false;
        }
        return true;
    }

    async ObterRelatorio(id) {
        const response = await httpClient.get(`relatorio/${id}`, true);
        if (response.status !== 200) {
            return false;
        }
        const relatorio = await response.json();
        return relatorio;
    }
}