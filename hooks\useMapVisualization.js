import RelatorioService from "@/lib/services/relatorioService";
import VisualizarMapaService from "@/lib/services/visualizarMapa";
import {
  logDebug,
  logHookError,
  logInfo,
  logPerformance,
  logWarn,
} from "@/utils/logger";
import { useCallback, useEffect, useState } from "react";
import { useToast } from "./use-toast";

export const useMapVisualization = (id) => {
  const { toast } = useToast();
  const [state, setState] = useState({
    geoJsonData: {},
    data: [],
    columns: [],
    minerais: [],
    selectedMineral: "",
    mineralColorConfig: [],
    loading: true,
    error: null,
  });

  const [modals, setModals] = useState({
    dataModal: false,
    colorConfig: false,
  });

  const relatorioService = new RelatorioService();
  const visualizarMapaService = new VisualizarMapaService();

  const fetchGeoJsonData = useCallback(async () => {
    const startTime = performance.now();

    try {
      logDebug("Iniciando carregamento de dados GeoJSON", { id });

      const relatorio = await visualizarMapaService.ObterColeta(id);

      if (!relatorio?.geojson?.pontos) {
        throw new Error("Dados do mapa incompletos ou inválidos");
      }

      const pontosString = relatorio.geojson.pontos;

      if (!pontosString.endsWith("}") && !pontosString.endsWith("]")) {
        logWarn("JSON possivelmente truncado", {
          id,
          lastChars: pontosString.slice(-10),
        });
      }

      relatorio.geojson.pontos = JSON.parse(pontosString);

      if (
        !relatorio.geojson.pontos.type ||
        !relatorio.geojson.pontos.features
      ) {
        throw new Error("Estrutura do GeoJSON inválida");
      }

      logInfo("Dados GeoJSON carregados com sucesso", {
        id,
        featuresCount: relatorio.geojson.pontos.features.length,
      });

      return relatorio;
    } catch (error) {
      logHookError("useMapVisualization.fetchGeoJsonData", error, [id]);

      const fallbackData = {
        geojson: {
          pontos: {
            type: "FeatureCollection",
            features: [],
          },
        },
      };

      toast({
        title: "Aviso",
        description:
          "Dados do mapa estão corrompidos. Algumas informações podem não ser exibidas.",
        variant: "warning",
      });

      return fallbackData;
    } finally {
      logPerformance("fetchGeoJsonData", startTime);
    }
  }, [id, visualizarMapaService, toast]);

  const fetchReportData = useCallback(async () => {
    try {
      const relatorio = await relatorioService.ObterRelatorio(id);

      if (!relatorio?.jsonRelatorio) {
        console.warn("Relatório não contém dados JSON");
        return { data: [], columns: [], minerais: [] };
      }

      const jsonString = relatorio.jsonRelatorio;

      if (
        !jsonString.endsWith("}") &&
        !jsonString.endsWith("]") &&
        !jsonString.endsWith('"}') &&
        !jsonString.endsWith('"]')
      ) {
        console.warn("JSON do relatório possivelmente truncado");
      }

      const parsedData = JSON.parse(jsonString);

      if (!Array.isArray(parsedData) || parsedData.length === 0) {
        throw new Error("Estrutura do relatório inválida");
      }

      const headers = Object.keys(parsedData[0]);
      const possibleMinerals = headers.filter(
        (header) => typeof parsedData[0][header] === "number"
      );

      const columnHeaders = headers.map((key) => ({
        accessorKey: key,
        header: key,
      }));

      return {
        data: parsedData,
        columns: columnHeaders,
        minerais: possibleMinerals,
      };
    } catch (error) {
      console.error("Erro ao carregar relatório:", error);

      toast({
        title: "Aviso",
        description: "Dados do relatório estão corrompidos.",
        variant: "warning",
      });

      return { data: [], columns: [], minerais: [] };
    }
  }, [id, relatorioService, toast]);

  const loadData = useCallback(async () => {
    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const [geoJsonData, reportData] = await Promise.all([
        fetchGeoJsonData(),
        fetchReportData(),
      ]);

      setState((prev) => ({
        ...prev,
        geoJsonData,
        data: reportData.data,
        columns: reportData.columns,
        minerais: reportData.minerais,
        selectedMineral:
          reportData.minerais.length > 0 ? reportData.minerais[0] : "",
        loading: false,
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: error.message,
        loading: false,
      }));

      toast({
        title: "Erro",
        description: `Erro ao carregar dados: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [fetchGeoJsonData, fetchReportData, toast]);

  const getMineralStats = useCallback((data, mineral) => {
    if (!mineral || !data.length) {
      return { min: null, max: null, avg: null };
    }

    const values = data.map((r) => Number(r[mineral])).filter((v) => !isNaN(v));

    if (values.length === 0) {
      return { min: null, max: null, avg: null };
    }

    const min = Math.min(...values);
    const max = Math.max(...values);
    const avg = values.reduce((a, b) => a + b, 0) / values.length;

    return { min, max, avg };
  }, []);

  const setSelectedMineral = useCallback((mineral) => {
    setState((prev) => ({ ...prev, selectedMineral: mineral }));
  }, []);

  const setMineralColorConfig = useCallback((config) => {
    setState((prev) => ({ ...prev, mineralColorConfig: config }));
  }, []);

  const openModal = useCallback((modalName) => {
    setModals((prev) => ({ ...prev, [modalName]: true }));
  }, []);

  const closeModal = useCallback((modalName) => {
    setModals((prev) => ({ ...prev, [modalName]: false }));
  }, []);

  const saveColorConfiguration = useCallback(
    (config) => {
      setMineralColorConfig(config);
      closeModal("colorConfig");

      toast({
        title: "Configuração salva",
        description: "As cores foram configuradas com sucesso.",
      });
    },
    [setMineralColorConfig, closeModal, toast]
  );

  useEffect(() => {
    if (id) {
      loadData();
    }
  }, [id, loadData]);

  return {
    ...state,
    modals,
    getMineralStats,
    setSelectedMineral,
    setMineralColorConfig,
    openModal,
    closeModal,
    saveColorConfiguration,
    reload: loadData,
  };
};
