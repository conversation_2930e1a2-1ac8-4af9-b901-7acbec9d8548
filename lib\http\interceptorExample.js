import interceptor from './httpInterceptor';

/**
 * Example of how to use the HTTP interceptor
 * This file demonstrates how to add request and response interceptors
 */

// Example: Add a request interceptor that adds a custom header to all requests
const requestInterceptorId = interceptor.addRequestInterceptor((config) => {
    // Add a custom header to all requests
    const newConfig = { ...config };
    newConfig.headers = {
        ...newConfig.headers,
        'X-Custom-Header': 'CustomValue',
    };
    
    // You can also log requests, modify URLs, etc.
    console.log(`Request to: ${config.method} ${config.url}`);
    
    return newConfig;
});

// Example: Add a response interceptor that logs all responses
const responseInterceptorId = interceptor.addResponseInterceptor(async (response) => {
    // Clone the response to avoid consuming it
    const clonedResponse = response.clone();
    
    // Log response status
    console.log(`Response status: ${response.status} ${response.statusText}`);
    
    // You can modify the response or handle errors globally here
    if (!response.ok) {
        console.error('Response error:', response.status);
        // You could throw an error, redirect to login page on 401, etc.
    }
    
    return response;
});

// Example: Add an error handling interceptor
interceptor.addResponseInterceptor(async (response) => {
    if (response.status === 401) {
        // Handle unauthorized access (e.g., redirect to login)
        console.log('Unauthorized access, redirecting to login...');
        // window.location.href = '/';
    }
    
    return response;
});

// Example: How to remove interceptors when they're no longer needed
export const removeInterceptors = () => {
    interceptor.removeRequestInterceptor(requestInterceptorId);
    interceptor.removeResponseInterceptor(responseInterceptorId);
};

/**
 * Example of how to set up global loading indicators
 */
let activeRequests = 0;

// Add request interceptor to track active requests
interceptor.addRequestInterceptor((config) => {
    activeRequests++;
    updateLoadingState();
    return config;
});

// Add response interceptor to track completed requests
interceptor.addResponseInterceptor((response) => {
    activeRequests--;
    updateLoadingState();
    return response;
});

// Update loading state based on active requests
function updateLoadingState() {
    if (activeRequests > 0) {
        // Show loading indicator
        console.log('Loading...');
    } else {
        // Hide loading indicator
        console.log('Loading complete');
    }
}

export default {
    // Export any utility functions related to interceptors
    setupAuthInterceptor: () => {
        // Example: Set up authentication interceptor
        interceptor.addRequestInterceptor((config) => {
            // Get fresh token from storage
            const token = localStorage.getItem('token');
            if (token) {
                config.headers = {
                    ...config.headers,
                    'Authorization': `Bearer ${token}`
                };
            }
            return config;
        });
    }
};
