"use client";
import { AppProvider } from './AppContext';
import { DataProvider } from './DataContext';
import { NotificationProvider, NotificationDisplay } from './NotificationContext';
import { ErrorBoundary } from '@/components/base/ErrorBoundary';

export const AppProviders = ({ children }) => {
  return (
    <ErrorBoundary>
      <AppProvider>
        <DataProvider>
          <NotificationProvider>
            {children}
            <NotificationDisplay />
          </NotificationProvider>
        </DataProvider>
      </AppProvider>
    </ErrorBoundary>
  );
};

export default AppProviders;
