"use client";
import { cn } from '@/lib/utils';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/base/Button';

const PageHeader = ({ 
  title, 
  description, 
  backButton, 
  actions,
  className 
}) => (
  <div className={cn("space-y-4", className)}>
    {backButton && (
      <div>
        <Button
          variant="ghost"
          size="sm"
          onClick={backButton.onClick}
          className="gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          {backButton.text || 'Voltar'}
        </Button>
      </div>
    )}
    
    <div className="flex items-center justify-between">
      <div className="space-y-1">
        <h1 className="text-2xl font-bold tracking-tight">
          {title}
        </h1>
        {description && (
          <p className="text-muted-foreground">
            {description}
          </p>
        )}
      </div>
      
      {actions && (
        <div className="flex items-center gap-2">
          {actions}
        </div>
      )}
    </div>
  </div>
);

const PageContent = ({ 
  children, 
  className,
  maxWidth = "full" 
}) => {
  const maxWidthClasses = {
    sm: "max-w-sm",
    md: "max-w-md", 
    lg: "max-w-lg",
    xl: "max-w-xl",
    "2xl": "max-w-2xl",
    "4xl": "max-w-4xl",
    "6xl": "max-w-6xl",
    full: "max-w-full"
  };

  return (
    <div className={cn(
      "mx-auto w-full",
      maxWidthClasses[maxWidth],
      className
    )}>
      {children}
    </div>
  );
};

const PageSection = ({ 
  title, 
  description, 
  children, 
  className 
}) => (
  <section className={cn("space-y-4", className)}>
    {(title || description) && (
      <div className="space-y-1">
        {title && (
          <h2 className="text-lg font-semibold">
            {title}
          </h2>
        )}
        {description && (
          <p className="text-sm text-muted-foreground">
            {description}
          </p>
        )}
      </div>
    )}
    {children}
  </section>
);

const PageGrid = ({ 
  children, 
  columns = 1, 
  gap = 6,
  className 
}) => {
  const columnClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
    6: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6"
  };

  const gapClasses = {
    2: "gap-2",
    4: "gap-4", 
    6: "gap-6",
    8: "gap-8"
  };

  return (
    <div className={cn(
      "grid",
      columnClasses[columns],
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
};

const PageCard = ({ 
  title, 
  description, 
  children, 
  className,
  headerActions
}) => (
  <div className={cn(
    "rounded-lg border bg-card text-card-foreground shadow-sm",
    className
  )}>
    {(title || description || headerActions) && (
      <div className="p-6 pb-0">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            {title && (
              <h3 className="text-lg font-semibold">
                {title}
              </h3>
            )}
            {description && (
              <p className="text-sm text-muted-foreground">
                {description}
              </p>
            )}
          </div>
          {headerActions && (
            <div className="flex items-center gap-2">
              {headerActions}
            </div>
          )}
        </div>
      </div>
    )}
    <div className="p-6">
      {children}
    </div>
  </div>
);

export const PageLayout = ({ 
  children, 
  className,
  spacing = 6 
}) => {
  const spacingClasses = {
    4: "space-y-4",
    6: "space-y-6", 
    8: "space-y-8"
  };

  return (
    <div className={cn(
      spacingClasses[spacing],
      className
    )}>
      {children}
    </div>
  );
};

// Export all components
PageLayout.Header = PageHeader;
PageLayout.Content = PageContent;
PageLayout.Section = PageSection;
PageLayout.Grid = PageGrid;
PageLayout.Card = PageCard;

export {
  PageHeader,
  PageContent,
  PageSection,
  PageGrid,
  PageCard
};

export default PageLayout;
