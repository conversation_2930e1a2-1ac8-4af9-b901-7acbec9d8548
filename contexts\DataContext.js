"use client";
import { createContext, useContext, useReducer, useCallback } from 'react';
import { logError, logInfo } from '@/utils/logger';

// Action Types
const ActionTypes = {
  SET_COLLECTIONS: 'SET_COLLECTIONS',
  ADD_COLLECTION: 'ADD_COLLECTION',
  UPDATE_COLLECTION: 'UPDATE_COLLECTION',
  REMOVE_COLLECTION: 'REMOVE_COLLECTION',
  SET_USERS: 'SET_USERS',
  ADD_USER: 'ADD_USER',
  UPDATE_USER: 'UPDATE_USER',
  REMOVE_USER: 'REMOVE_USER',
  SET_REPORTS: 'SET_REPORTS',
  ADD_REPORT: 'ADD_REPORT',
  UPDATE_REPORT: 'UPDATE_REPORT',
  REMOVE_REPORT: 'REMOVE_REPORT',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR'
};

// Initial State
const initialState = {
  collections: [],
  users: [],
  reports: [],
  loading: {
    collections: false,
    users: false,
    reports: false
  },
  errors: {
    collections: null,
    users: null,
    reports: null
  }
};

// Reducer
const dataReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SET_COLLECTIONS:
      return {
        ...state,
        collections: action.payload,
        loading: { ...state.loading, collections: false },
        errors: { ...state.errors, collections: null }
      };

    case ActionTypes.ADD_COLLECTION:
      return {
        ...state,
        collections: [...state.collections, action.payload]
      };

    case ActionTypes.UPDATE_COLLECTION:
      return {
        ...state,
        collections: state.collections.map(collection =>
          collection.id === action.payload.id ? action.payload : collection
        )
      };

    case ActionTypes.REMOVE_COLLECTION:
      return {
        ...state,
        collections: state.collections.filter(collection => collection.id !== action.payload)
      };

    case ActionTypes.SET_USERS:
      return {
        ...state,
        users: action.payload,
        loading: { ...state.loading, users: false },
        errors: { ...state.errors, users: null }
      };

    case ActionTypes.ADD_USER:
      return {
        ...state,
        users: [...state.users, action.payload]
      };

    case ActionTypes.UPDATE_USER:
      return {
        ...state,
        users: state.users.map(user =>
          user.id === action.payload.id ? action.payload : user
        )
      };

    case ActionTypes.REMOVE_USER:
      return {
        ...state,
        users: state.users.filter(user => user.id !== action.payload)
      };

    case ActionTypes.SET_REPORTS:
      return {
        ...state,
        reports: action.payload,
        loading: { ...state.loading, reports: false },
        errors: { ...state.errors, reports: null }
      };

    case ActionTypes.ADD_REPORT:
      return {
        ...state,
        reports: [...state.reports, action.payload]
      };

    case ActionTypes.UPDATE_REPORT:
      return {
        ...state,
        reports: state.reports.map(report =>
          report.id === action.payload.id ? action.payload : report
        )
      };

    case ActionTypes.REMOVE_REPORT:
      return {
        ...state,
        reports: state.reports.filter(report => report.id !== action.payload)
      };

    case ActionTypes.SET_LOADING:
      return {
        ...state,
        loading: { ...state.loading, [action.payload.type]: action.payload.loading }
      };

    case ActionTypes.SET_ERROR:
      return {
        ...state,
        errors: { ...state.errors, [action.payload.type]: action.payload.error },
        loading: { ...state.loading, [action.payload.type]: false }
      };

    case ActionTypes.CLEAR_ERROR:
      return {
        ...state,
        errors: { ...state.errors, [action.payload]: null }
      };

    default:
      return state;
  }
};

// Context
const DataContext = createContext();

// Provider Component
export const DataProvider = ({ children }) => {
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Collections Actions
  const setCollections = useCallback((collections) => {
    dispatch({ type: ActionTypes.SET_COLLECTIONS, payload: collections });
    logInfo('Collections updated', { count: collections.length });
  }, []);

  const addCollection = useCallback((collection) => {
    dispatch({ type: ActionTypes.ADD_COLLECTION, payload: collection });
    logInfo('Collection added', { id: collection.id });
  }, []);

  const updateCollection = useCallback((collection) => {
    dispatch({ type: ActionTypes.UPDATE_COLLECTION, payload: collection });
    logInfo('Collection updated', { id: collection.id });
  }, []);

  const removeCollection = useCallback((id) => {
    dispatch({ type: ActionTypes.REMOVE_COLLECTION, payload: id });
    logInfo('Collection removed', { id });
  }, []);

  // Users Actions
  const setUsers = useCallback((users) => {
    dispatch({ type: ActionTypes.SET_USERS, payload: users });
    logInfo('Users updated', { count: users.length });
  }, []);

  const addUser = useCallback((user) => {
    dispatch({ type: ActionTypes.ADD_USER, payload: user });
    logInfo('User added', { id: user.id });
  }, []);

  const updateUser = useCallback((user) => {
    dispatch({ type: ActionTypes.UPDATE_USER, payload: user });
    logInfo('User updated', { id: user.id });
  }, []);

  const removeUser = useCallback((id) => {
    dispatch({ type: ActionTypes.REMOVE_USER, payload: id });
    logInfo('User removed', { id });
  }, []);

  // Reports Actions
  const setReports = useCallback((reports) => {
    dispatch({ type: ActionTypes.SET_REPORTS, payload: reports });
    logInfo('Reports updated', { count: reports.length });
  }, []);

  const addReport = useCallback((report) => {
    dispatch({ type: ActionTypes.ADD_REPORT, payload: report });
    logInfo('Report added', { id: report.id });
  }, []);

  const updateReport = useCallback((report) => {
    dispatch({ type: ActionTypes.UPDATE_REPORT, payload: report });
    logInfo('Report updated', { id: report.id });
  }, []);

  const removeReport = useCallback((id) => {
    dispatch({ type: ActionTypes.REMOVE_REPORT, payload: id });
    logInfo('Report removed', { id });
  }, []);

  // Loading and Error Actions
  const setLoading = useCallback((type, loading) => {
    dispatch({ type: ActionTypes.SET_LOADING, payload: { type, loading } });
  }, []);

  const setError = useCallback((type, error) => {
    dispatch({ type: ActionTypes.SET_ERROR, payload: { type, error } });
    logError(`${type} error`, { error: error.message });
  }, []);

  const clearError = useCallback((type) => {
    dispatch({ type: ActionTypes.CLEAR_ERROR, payload: type });
  }, []);

  // Computed values
  const getCollectionById = useCallback((id) => {
    return state.collections.find(collection => collection.id === id);
  }, [state.collections]);

  const getUserById = useCallback((id) => {
    return state.users.find(user => user.id === id);
  }, [state.users]);

  const getReportById = useCallback((id) => {
    return state.reports.find(report => report.id === id);
  }, [state.reports]);

  const value = {
    // State
    ...state,
    
    // Collections
    setCollections,
    addCollection,
    updateCollection,
    removeCollection,
    getCollectionById,
    
    // Users
    setUsers,
    addUser,
    updateUser,
    removeUser,
    getUserById,
    
    // Reports
    setReports,
    addReport,
    updateReport,
    removeReport,
    getReportById,
    
    // Loading and Errors
    setLoading,
    setError,
    clearError
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};

// Hook
export const useData = () => {
  const context = useContext(DataContext);
  
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  
  return context;
};
