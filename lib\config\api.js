// Configuração da API
const API_CONFIG = {
  // URLs para diferentes ambientes
  PRODUCTION: "https://apis-api-coleta.uwqcav.easypanel.host/api/",
  DEVELOPMENT_HTTPS: "https://localhost:7295/api/",
  DEVELOPMENT_HTTP: "http://localhost:7295/api/",
  FALLBACK: "http://localhost:5004/api/",
};

// Detecta automaticamente qual URL usar
export const getApiBaseUrl = () => {
  // Em produção, usar a URL de produção
  if (process.env.NODE_ENV === "production") {
    return API_CONFIG.PRODUCTION;
  }

  // Em desenvolvimento, tentar HTTPS primeiro, depois HTTP
  return API_CONFIG.DEVELOPMENT_HTTPS;
};

export default API_CONFIG;
