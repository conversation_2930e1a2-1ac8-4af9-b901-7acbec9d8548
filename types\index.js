// Domain Types
export const UserRole = {
  ADMIN: 'admin',
  FUNCIONARIO: 'funcionario',
  CLIENTE: 'cliente'
};

export const CollectionType = {
  GRID: 'grid',
  RANDOM: 'random',
  MANUAL: 'manual'
};

export const AnalysisType = {
  SOIL: 'solo',
  WATER: 'agua',
  PLANT: 'planta',
  FERTILIZER: 'fertilizante'
};

// API Response Types
export const ApiStatus = {
  SUCCESS: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_ERROR: 500
};

// Form Validation Types
export const ValidationRules = {
  REQUIRED: 'Campo obrigatório',
  EMAIL: 'Email inválido',
  MIN_LENGTH: (min) => `Mínimo ${min} caracteres`,
  MAX_LENGTH: (max) => `Máximo ${max} caracteres`,
  POSITIVE_NUMBER: 'Deve ser um número positivo',
  VALID_COORDINATES: 'Coordenadas inválidas'
};

// UI State Types
export const LoadingState = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error'
};

export const ToastVariant = {
  DEFAULT: 'default',
  SUCCESS: 'success',
  WARNING: 'warning',
  DESTRUCTIVE: 'destructive'
};

// Map Types
export const MapMode = {
  VIEW: 'view',
  DRAW: 'draw',
  EDIT: 'edit',
  SELECT: 'select'
};

export const GeometryType = {
  POINT: 'Point',
  POLYGON: 'Polygon',
  FEATURE_COLLECTION: 'FeatureCollection'
};

// Chart Types
export const ChartType = {
  HISTOGRAM: 'histogram',
  BAR: 'bar',
  LINE: 'line',
  PIE: 'pie'
};

// Color Configuration
export const ColorRange = {
  RED_TO_GREEN: 'red-to-green',
  BLUE_TO_RED: 'blue-to-red',
  CUSTOM: 'custom'
};

// Pagination
export const PaginationDefaults = {
  PAGE_SIZE: 10,
  INITIAL_PAGE: 1,
  MAX_PAGE_SIZE: 100
};

// File Types
export const FileType = {
  EXCEL: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  CSV: 'text/csv',
  JSON: 'application/json',
  GEOJSON: 'application/geo+json'
};

// Error Types
export const ErrorType = {
  VALIDATION: 'validation',
  NETWORK: 'network',
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  NOT_FOUND: 'not_found',
  SERVER: 'server',
  UNKNOWN: 'unknown'
};

// Form Field Types
export const FieldType = {
  TEXT: 'text',
  EMAIL: 'email',
  PASSWORD: 'password',
  NUMBER: 'number',
  SELECT: 'select',
  MULTI_SELECT: 'multi-select',
  CHECKBOX: 'checkbox',
  RADIO: 'radio',
  DATE: 'date',
  TEXTAREA: 'textarea',
  FILE: 'file'
};

// Modal Types
export const ModalType = {
  CONFIRMATION: 'confirmation',
  FORM: 'form',
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error'
};

// Table Types
export const SortDirection = {
  ASC: 'asc',
  DESC: 'desc'
};

export const FilterOperator = {
  EQUALS: 'equals',
  CONTAINS: 'contains',
  STARTS_WITH: 'starts_with',
  ENDS_WITH: 'ends_with',
  GREATER_THAN: 'greater_than',
  LESS_THAN: 'less_than',
  BETWEEN: 'between'
};
