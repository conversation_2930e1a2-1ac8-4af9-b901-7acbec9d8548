# Componente Spinner - G<PERSON><PERSON> de <PERSON>o

O componente `Spinner` foi atualizado com design moderno e proporções adequadas, mantendo a mesma API simples.

## Componente Principal

### Spinner (Componente Atualizado)
Componente principal para loading com design melhorado.

```jsx
import { Spinner } from '@/components/ui/spinner';

// Uso básico
<Spinner message="Carregando..." />

// Com diferentes tamanhos
<Spinner size="small" />   // 24x24px
<Spinner size="medium" />  // 32x32px (padrão)
<Spinner size="large" />   // 48x48px

// Com controle de visibilidade
<Spinner show={loading} message="Processando..." />

// Personalização com className
<Spinner
  size="large"
  message="Carregando dados..."
  className="text-emerald-600"
/>
```

## Propriedades do Componente

### Props Disponíveis

| Prop | Tipo | Padrão | Descrição |
|------|------|--------|-----------|
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | Tamanho do spinner |
| `show` | `boolean` | `true` | Controla a visibilidade |
| `message` | `string` | - | Texto exibido abaixo do spinner |
| `className` | `string` | - | Classes CSS adicionais |
| `children` | `ReactNode` | - | Conteúdo adicional |

### Tamanhos Disponíveis

- **small**: 24x24px - Para botões e elementos pequenos
- **medium**: 32x32px - Tamanho padrão para uso geral
- **large**: 48x48px - Para loading de páginas e seções grandes

## Componentes Auxiliares (LoadingSpinner.js)

Os componentes auxiliares em `components/base/LoadingSpinner.js` ainda estão disponíveis para casos específicos:

- `LoadingSpinner` - Versão base
- `FullPageLoader` - Loading de página inteira
- `InlineLoader` - Loading inline
- `CardLoader` - Loading para cards
- `TableLoader` - Skeleton para tabelas
- `SkeletonLoader` - Skeleton para texto

## Melhorias Implementadas

### ✅ Proporções Corrigidas
- Tamanhos consistentes e proporcionais (24px, 32px, 48px)
- Espaçamento uniforme de 12px entre spinner e texto
- Responsividade melhorada

### ✅ Design Moderno
- Animações suaves com `transition-all duration-200`
- Espaçamento vertical com `gap-3`
- Texto com opacidade reduzida para melhor hierarquia visual

### ✅ Simplicidade
- **API mantida**: Não foi necessário alterar imports existentes
- **Compatibilidade**: Funciona com código existente
- **Flexibilidade**: Usa `text-current` para herdar cor do contexto

### ✅ Melhor UX
- Texto centralizado e com espaçamento adequado
- Animação fluida do spinner
- Contraste adequado em qualquer background

## Exemplos de Uso nas Páginas

### Suspense Fallback
```jsx
<Suspense
  fallback={
    <div className="min-h-screen p-4 flex items-center justify-center">
      <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
        <Spinner
          message="Carregando página..."
          size="large"
          className="text-emerald-600"
        />
      </div>
    </div>
  }
>
  <PageContent />
</Suspense>
```

### Loading State em Seções
```jsx
{loading ? (
  <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
    <Spinner
      message="Carregando dados..."
      size="large"
      className="text-emerald-600"
    />
  </div>
) : (
  <Content />
)}
```

### Loading em Botões
```jsx
<Button disabled={loading}>
  {loading ? (
    <div className="flex items-center space-x-2 text-white">
      <Spinner size="small" />
      <span>Salvando...</span>
    </div>
  ) : (
    "Salvar"
  )}
</Button>
```

### Overlay Loading
```jsx
{saving && (
  <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
    <div className="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border p-8 max-w-sm mx-4">
      <Spinner
        message="Salvando alterações..."
        size="large"
        className="text-emerald-600"
      />
    </div>
  </div>
)}
```

## Vantagens da Abordagem Simplificada

✅ **Sem Breaking Changes**: Código existente continua funcionando
✅ **Imports Mantidos**: Não foi necessário alterar imports
✅ **API Simples**: Mesma interface, design melhorado
✅ **Flexibilidade**: Usa `text-current` para herdar cores do contexto
✅ **Manutenibilidade**: Um componente principal ao invés de vários
