# Componentes de Loading - <PERSON><PERSON><PERSON> de Uso

Este arquivo contém todos os componentes de loading atualizados com design moderno e proporções adequadas.

## Componentes Disponíveis

### 1. LoadingSpinner (Componente Base)
Componente principal para spinners simples.

```jsx
import { LoadingSpinner } from '@/components/base/LoadingSpinner';

// Uso básico
<LoadingSpinner message="Carregando..." />

// Com diferentes tamanhos
<LoadingSpinner size="xs" />      // 16x16px
<LoadingSpinner size="sm" />      // 24x24px  
<LoadingSpinner size="default" /> // 32x32px
<LoadingSpinner size="lg" />      // 48x48px
<LoadingSpinner size="xl" />      // 64x64px

// Com diferentes cores
<LoadingSpinner color="primary" />
<LoadingSpinner color="white" />
<LoadingSpinner color="current" />
<LoadingSpinner color="muted" />
```

### 2. ModernLoader
Componente com design moderno e visual aprimorado.

```jsx
import { ModernLoader } from '@/components/base/LoadingSpinner';

<ModernLoader 
  message="Carregando dados..." 
  subtitle="Aguarde enquanto processamos"
  variant="gradient"  // gradient, solid, outline, glass
  size="lg"
/>
```

**Variantes disponíveis:**
- `gradient`: Fundo gradiente verde/teal
- `solid`: Fundo sólido primário
- `outline`: Borda com fundo transparente
- `glass`: Efeito vidro com blur

### 3. OverlayLoader
Loading em overlay para tela inteira.

```jsx
import { OverlayLoader } from '@/components/base/LoadingSpinner';

<OverlayLoader 
  message="Salvando dados..." 
  subtitle="Não feche a página"
  blur={true}
/>
```

### 4. FullPageLoader
Loading para páginas inteiras com container.

```jsx
import { FullPageLoader } from '@/components/base/LoadingSpinner';

<FullPageLoader 
  message="Carregando página..." 
  size="lg"
/>
```

### 5. InlineLoader
Loading inline para seções específicas.

```jsx
import { InlineLoader } from '@/components/base/LoadingSpinner';

// Horizontal (padrão)
<InlineLoader message="Carregando lista..." />

// Vertical
<InlineLoader 
  message="Processando..." 
  orientation="vertical"
  size="sm"
/>
```

### 6. CardLoader
Loading para cards e containers.

```jsx
import { CardLoader } from '@/components/base/LoadingSpinner';

<CardLoader 
  message="Carregando dados..." 
  height="h-48"
  size="default"
/>
```

### 7. TableLoader
Loading para tabelas com skeleton.

```jsx
import { TableLoader } from '@/components/base/LoadingSpinner';

<TableLoader 
  rows={5} 
  columns={4} 
  showHeader={true}
/>
```

### 8. SkeletonLoader
Loading skeleton para texto e conteúdo.

```jsx
import { SkeletonLoader } from '@/components/base/LoadingSpinner';

<SkeletonLoader 
  lines={3} 
  variant="text"  // text, title, paragraph, card
/>
```

## Componente Spinner (UI)
Componente atualizado com melhor API.

```jsx
import { Spinner } from '@/components/ui/spinner';

<Spinner 
  size="medium"
  color="primary"
  message="Carregando..."
  show={true}
/>
```

## Melhorias Implementadas

### ✅ Proporções Corrigidas
- Tamanhos consistentes e proporcionais
- Espaçamento adequado entre spinner e texto
- Responsividade melhorada

### ✅ Design Moderno
- Efeitos de blur e transparência
- Gradientes suaves
- Animações fluidas com delays escalonados

### ✅ Acessibilidade
- Cores com contraste adequado
- Textos legíveis em diferentes backgrounds
- Suporte a temas claro/escuro

### ✅ Performance
- Animações otimizadas
- Componentes leves
- Lazy loading quando possível

### ✅ Flexibilidade
- Múltiplas variantes e tamanhos
- Customização via props
- Compatibilidade com Tailwind CSS

## Exemplos de Uso nas Páginas

### Suspense Fallback
```jsx
<Suspense
  fallback={
    <div className="min-h-screen p-4 flex items-center justify-center">
      <ModernLoader 
        message="Carregando página..." 
        subtitle="Aguarde enquanto buscamos os dados"
        variant="glass"
        size="lg"
      />
    </div>
  }
>
  <PageContent />
</Suspense>
```

### Loading State
```jsx
{loading ? (
  <ModernLoader 
    message="Processando..." 
    subtitle="Isso pode levar alguns segundos"
    variant="glass"
    size="lg"
  />
) : (
  <Content />
)}
```

### Overlay Loading
```jsx
{saving && (
  <OverlayLoader 
    message="Salvando alterações..." 
    subtitle="Não feche a página"
  />
)}
```
