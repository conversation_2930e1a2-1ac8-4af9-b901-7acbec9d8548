"use client";
import { useState, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { ChevronUp, ChevronDown, Search, Filter } from 'lucide-react';
import { Button } from './Button';
import { Input } from './Input';
import {
  Table as UITable,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

export const Table = ({ 
  data = [],
  columns = [],
  loading = false,
  emptyMessage = "Nenhum dado encontrado",
  className,
  onRowClick,
  sortable = true,
  filterable = true,
  ...props 
}) => {
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [filterText, setFilterText] = useState('');

  const sortedData = useMemo(() => {
    if (!sortConfig.key) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [data, sortConfig]);

  const filteredData = useMemo(() => {
    if (!filterText) return sortedData;

    return sortedData.filter(row =>
      columns.some(column => {
        const value = row[column.key];
        return value?.toString().toLowerCase().includes(filterText.toLowerCase());
      })
    );
  }, [sortedData, filterText, columns]);

  const handleSort = (key) => {
    if (!sortable) return;

    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const getSortIcon = (key) => {
    if (sortConfig.key !== key) return null;
    return sortConfig.direction === 'asc' ? 
      <ChevronUp className="h-4 w-4" /> : 
      <ChevronDown className="h-4 w-4" />;
  };

  const handleRowClick = (row, index) => {
    onRowClick?.(row, index);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {filterable && (
        <div className="flex items-center gap-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Filtrar dados..."
              value={filterText}
              onChange={(e) => setFilterText(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      )}

      <div className="rounded-md border">
        <UITable className={className} {...props}>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead
                  key={column.key}
                  className={cn(
                    sortable && "cursor-pointer hover:bg-muted/50",
                    column.className
                  )}
                  onClick={() => handleSort(column.key)}
                >
                  <div className="flex items-center gap-2">
                    {column.label}
                    {sortable && getSortIcon(column.key)}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.length === 0 ? (
              <TableRow>
                <TableCell 
                  colSpan={columns.length} 
                  className="text-center py-8 text-muted-foreground"
                >
                  {emptyMessage}
                </TableCell>
              </TableRow>
            ) : (
              filteredData.map((row, index) => (
                <TableRow
                  key={row.id || index}
                  className={cn(
                    onRowClick && "cursor-pointer hover:bg-muted/50"
                  )}
                  onClick={() => handleRowClick(row, index)}
                >
                  {columns.map((column) => (
                    <TableCell key={column.key} className={column.cellClassName}>
                      {column.render ? 
                        column.render(row[column.key], row, index) : 
                        row[column.key]
                      }
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </UITable>
      </div>
    </div>
  );
};

export const DataTable = ({ 
  data,
  columns,
  pagination,
  onPageChange,
  onPageSizeChange,
  ...props 
}) => {
  return (
    <div className="space-y-4">
      <Table data={data} columns={columns} {...props} />
      
      {pagination && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Mostrando {pagination.startIndex + 1} a {pagination.endIndex} de {pagination.totalItems} resultados
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(pagination.currentPage - 1)}
              disabled={!pagination.hasPreviousPage}
            >
              Anterior
            </Button>
            
            <span className="text-sm">
              Página {pagination.currentPage} de {pagination.totalPages}
            </span>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(pagination.currentPage + 1)}
              disabled={!pagination.hasNextPage}
            >
              Próxima
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export const createColumn = (key, label, options = {}) => {
  return {
    key,
    label,
    ...options
  };
};

export const createActionColumn = (actions) => {
  return createColumn('actions', 'Ações', {
    render: (value, row, index) => (
      <div className="flex items-center gap-2">
        {actions.map((action, actionIndex) => (
          <Button
            key={actionIndex}
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              action.onClick(row, index);
            }}
          >
            {action.icon}
            {action.label}
          </Button>
        ))}
      </div>
    )
  });
};
