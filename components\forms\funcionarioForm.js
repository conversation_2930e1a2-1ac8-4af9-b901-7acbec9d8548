import { Label } from "@radix-ui/react-label";
import { Input } from "../ui/input";
import { useEffect, useState } from "react";
import { maskCep, maskCpf, maskPhone } from "@/lib/mask";

export default function FuncionarioForm({ register, errors, funcionario, setValue }) {
    useEffect(() => {
        if (funcionario) {
            setValue("nome", funcionario.nome);
            setValue("cpf", maskCpf(funcionario.cpf));
            setValue("telefone", maskPhone(funcionario.telefone));
            setValue("email", funcionario.email);
            setValue("senha", maskCep(funcionario.senha));
        }
    }, [funcionario]);


    return (
        <div>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                    <Label htmlFor="nome">Nome Completo</Label>
                    <Input
                        id="nome"
                        type="text"
                        {...register("nomeCompleto")}
                        placeholder="Ex: <PERSON>"
                    />
                    {errors.nomeCompleto && (<p className="text-red-500 text-sm">*{errors.nomeCompleto.message}</p>)}
                </div>
                <div>
                    <Label htmlFor="cpf">CPF</Label>
                    <Input
                        id="cpf"
                        type="text"
                        {...register("cpf")}
                        placeholder="000.000.000-00"
                        onChange={(e) => setValue("cpf", maskCpf(e.target.value))}
                    />
                    {errors.cpf && (<p className="text-red-500 text-sm">*{errors.cpf.message}</p>)}
                </div>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 mt-6">
                <div>
                    <Label htmlFor="telefone">Telefone</Label>
                    <Input
                        id="telefone"
                        type="text"
                        {...register("telefone")}
                        placeholder="(00) 00000-0000"
                        onChange={(e) => setValue("telefone", maskPhone(e.target.value))}
                    />
                    {errors.telefone && (<p className="text-red-500 text-sm">*{errors.telefone.message}</p>)}
                </div>
                <div>
                    <Label htmlFor="email">E-mail</Label>
                    <Input
                        id="email"
                        type="text"
                        {...register("email")}
                        placeholder="<EMAIL>"
                    />
                    {errors.email && (<p className="text-red-500 text-sm">*{errors.email.message}</p>)}
                </div>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 mt-6">
                <div>
                    <Label htmlFor="password">Senha</Label>
                    <Input
                        id="password"
                        type="password"
                        {...register("senha")}
                        placeholder="******"
                    />
                    {errors.senha && (<p className="text-red-500 text-sm">*{errors.senha.message}</p>)}
                </div>
            </div>
        </div>
    );
}
