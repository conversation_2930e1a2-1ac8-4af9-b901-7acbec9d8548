"use client";
import MapaContainer from "../forms/MapaContainer";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import MineralForm from "@/components/forms/mineralForm";
import { coletaSchema } from "@/lib/schema/zod";
import { Button } from "@/components/ui/button";
import { Controller } from "react-hook-form";
import { SelectTrigger, SelectValue, SelectItem, SelectContent, Select } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogClose } from "@/components/ui/dialog"
import { LucideChartNoAxesGantt } from "lucide-react";
import { useState } from "react";

export default function Coleta({ minerais, initial }) {
    const [mineralSelected, setMineralSelected] = useState("");
    const { control, register, handleSubmit, watch, setValue, getValues, formState: { errors } } = useForm({
        resolver: zodResolver(coletaSchema),
        defaultValues: {
            mineralID: "",
            limites: [{ minimo: "", maximo: "", cor: "#ffffff" }]
        }
    });

    const { fields, append, remove } = useFieldArray({
        control,
        name: "limites"
    });


    return (
        <div>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <Dialog className="w-full max-w-6xl">
                    <DialogTrigger asChild>
                        <Button>
                            <LucideChartNoAxesGantt className="mr-2 h-4 w-4" />
                            Configuração Personalizada
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                            <DialogTitle>Configuração Personalizada</DialogTitle>
                            <DialogDescription>
                                Configure os limites de cores para o mineral selecionado.
                            </DialogDescription>
                        </DialogHeader>
                        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                            {mineralSelected ? (
                                <div className="grid gap-4">
                                    {fields.map((field, index) => (
                                        <div key={field.id} className="relative border p-4 rounded-md shadow-sm bg-muted min-w-[320px]">
                                            <div>
                                                <div>
                                                    <MineralForm
                                                        mineral={mineralSelected}
                                                        control={control}
                                                        register={register}
                                                        errors={errors}
                                                        setValue={setValue}
                                                        index={index}
                                                        watch={watch}
                                                    />
                                                </div>
                                                <div>
                                                    <Button
                                                        type="button"
                                                        onClick={() => remove(index)}
                                                        variant="destructive"
                                                        className="absolute top-2 right-2"
                                                    >
                                                        Remover
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="flex items-center justify-center">
                                    <p className="text-red-500 text-sm">*Selecione um mineral, para configurar os limites.</p>
                                </div>
                            )}
                            {mineralSelected ? (
                                <div className="mt-4">
                                    <Button className="w-full" type="button" onClick={() => append({ minimo: "", maximo: "", cor: "#ffffff" })}>
                                        Adicionar Limite
                                    </Button>
                                    <Button type="submit" variant="outline" className="mt-4 w-full">
                                        Salvar
                                    </Button>
                                </div>
                            ) : null}
                        </form>
                    </DialogContent>
                </Dialog>

                <Controller
                    name="mineralID"
                    control={control}
                    rules={{ required: "Campo obrigatório" }}
                    render={({ field }) => (
                        <Select
                            name="mineralID"
                            onValueChange={(value) => {
                                field.onChange(value);
                                setValue("mineralID", value);
                                setMineralSelected(value);
                            }}
                            value={field.value || ""}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Selecione um mineral..." />
                            </SelectTrigger>
                            <SelectContent>
                                {Array.isArray(minerais) && minerais.length > 0 ? (
                                    minerais.map((mineral, index) => (
                                        <SelectItem key={index} value={mineral}>
                                            {mineral}
                                        </SelectItem>
                                    ))
                                ) : (
                                    <p className="p-2 text-gray-500">
                                        Nenhum mineral encontrado.
                                    </p>
                                )}
                            </SelectContent>
                        </Select>
                    )}
                />
            </form>
            <MapaContainer initialValue={initial} />
        </div>
    )
}
