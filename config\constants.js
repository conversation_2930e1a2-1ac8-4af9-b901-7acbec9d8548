// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000
};

// Application Routes
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  DASHBOARD: '/admin',
  USERS: '/admin/usuarios',
  COLLECTIONS: '/admin/visualizarmapa',
  COLLECTION_VIEW: (id) => `/admin/visualizarmapa/visualizar/${id}`,
  REPORTS: '/admin/relatorios',
  SETTINGS: '/admin/configuracoes'
};

// User Roles
export const USER_ROLES = {
  ADMIN: 'admin',
  FUNCIONARIO: 'funcionario',
  CLIENTE: 'cliente'
};

// Collection Types
export const COLLECTION_TYPES = {
  GRID: 'grid',
  RANDOM: 'random',
  MANUAL: 'manual'
};

// Analysis Types
export const ANALYSIS_TYPES = {
  SOIL: 'solo',
  WATER: 'agua',
  PLANT: 'planta',
  FERTILIZER: 'fertilizante'
};

// Map Configuration
export const MAP_CONFIG = {
  DEFAULT_CENTER: [-15.7942, -47.8822], // Brasília
  DEFAULT_ZOOM: 4,
  MIN_ZOOM: 2,
  MAX_ZOOM: 18,
  TILE_LAYER: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
  ATTRIBUTION: '© OpenStreetMap contributors'
};

// Chart Configuration
export const CHART_CONFIG = {
  DEFAULT_COLORS: [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b',
    '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
  ],
  HISTOGRAM: {
    DEFAULT_BINS: 10,
    MIN_BINS: 5,
    MAX_BINS: 50,
    DEFAULT_COLOR: '#3b82f6'
  }
};

// File Upload Configuration
export const FILE_CONFIG = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: {
    EXCEL: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    CSV: 'text/csv',
    JSON: 'application/json',
    GEOJSON: 'application/geo+json'
  },
  ALLOWED_EXTENSIONS: ['.xlsx', '.xls', '.csv', '.json', '.geojson']
};

// Pagination Configuration
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [5, 10, 20, 50, 100],
  MAX_PAGE_SIZE: 100
};

// Validation Rules
export const VALIDATION_RULES = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  CPF: /^\d{3}\.\d{3}\.\d{3}-\d{2}$/,
  CNPJ: /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/,
  PHONE: /^\(\d{2}\)\s\d{4,5}-\d{4}$/,
  PASSWORD: {
    MIN_LENGTH: 8,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SPECIAL_CHARS: false
  }
};

// Toast Configuration
export const TOAST_CONFIG = {
  DURATION: {
    SHORT: 3000,
    MEDIUM: 5000,
    LONG: 8000
  },
  POSITION: 'top-right'
};

// Theme Configuration
export const THEME_CONFIG = {
  DEFAULT_THEME: 'light',
  AVAILABLE_THEMES: ['light', 'dark', 'system'],
  STORAGE_KEY: 'theme-preference'
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK: 'Erro de conexão. Verifique sua internet.',
  UNAUTHORIZED: 'Acesso não autorizado. Faça login novamente.',
  FORBIDDEN: 'Você não tem permissão para esta ação.',
  NOT_FOUND: 'Recurso não encontrado.',
  VALIDATION: 'Dados inválidos. Verifique os campos.',
  SERVER: 'Erro interno do servidor. Tente novamente.',
  UNKNOWN: 'Erro desconhecido. Entre em contato com o suporte.'
};

// Success Messages
export const SUCCESS_MESSAGES = {
  SAVE: 'Dados salvos com sucesso!',
  UPDATE: 'Dados atualizados com sucesso!',
  DELETE: 'Item removido com sucesso!',
  CREATE: 'Item criado com sucesso!',
  UPLOAD: 'Arquivo enviado com sucesso!',
  LOGIN: 'Login realizado com sucesso!',
  LOGOUT: 'Logout realizado com sucesso!'
};

// Loading Messages
export const LOADING_MESSAGES = {
  DEFAULT: 'Carregando...',
  SAVING: 'Salvando dados...',
  LOADING_DATA: 'Carregando dados...',
  UPLOADING: 'Enviando arquivo...',
  PROCESSING: 'Processando...',
  AUTHENTICATING: 'Autenticando...'
};

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'dd/MM/yyyy',
  DISPLAY_WITH_TIME: 'dd/MM/yyyy HH:mm',
  API: 'yyyy-MM-dd',
  API_WITH_TIME: 'yyyy-MM-dd HH:mm:ss'
};

// Number Formats
export const NUMBER_FORMATS = {
  CURRENCY: {
    LOCALE: 'pt-BR',
    CURRENCY: 'BRL'
  },
  DECIMAL_PLACES: {
    CURRENCY: 2,
    PERCENTAGE: 1,
    COORDINATES: 6,
    MEASUREMENTS: 2
  }
};

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_PREFERENCES: 'user_preferences',
  THEME: 'theme',
  LANGUAGE: 'language',
  SIDEBAR_STATE: 'sidebar_state'
};

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_DARK_MODE: true,
  ENABLE_NOTIFICATIONS: true,
  ENABLE_ANALYTICS: false,
  ENABLE_BETA_FEATURES: false
};

export default {
  API_CONFIG,
  ROUTES,
  USER_ROLES,
  COLLECTION_TYPES,
  ANALYSIS_TYPES,
  MAP_CONFIG,
  CHART_CONFIG,
  FILE_CONFIG,
  PAGINATION_CONFIG,
  VALIDATION_RULES,
  TOAST_CONFIG,
  THEME_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  LOADING_MESSAGES,
  DATE_FORMATS,
  NUMBER_FORMATS,
  STORAGE_KEYS,
  FEATURE_FLAGS
};
