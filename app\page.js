"use client";
import <PERSON><PERSON> from "@/components/registro/registro";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import { loginSchema } from "@/lib/schema/zod";
import AuthService from "@/lib/services/authService";
import { zodResolver } from "@hookform/resolvers/zod";
import Cookies from "js-cookie";
import { Building2, Eye, EyeOff, Leaf, Sprout, TreePine } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";

export default function Home() {
  const [loading, setLoading] = useState(false);
  const [registro, setRegistro] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(loginSchema),
  });

  const { toast } = useToast();
  const router = useRouter();

  const data = async (data) => {
    setLoading(true);
    const authService = new AuthService();
    const response = await authService.Login(data);
    if (!response) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "E-mail ou senha inválidos",
        variant: "destructive",
      });
    }
    Cookies.set("token", response.token);
    toast({
      title: "Sucesso",
      description: "Usuário autenticado com sucesso",
    });
    setLoading(false);
    router.push("/admin");
  };

  const handleRegistro = () => {
    setRegistro(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-green-100 flex">
      {/* Left Side - Branding */}
      <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-600 via-teal-600 to-green-700"></div>
        <div className="absolute inset-0 bg-black/20"></div>

        {/* Decorative Elements */}
        <div className="absolute top-20 left-20 opacity-20">
          <Leaf className="h-32 w-32 text-white transform rotate-12" />
        </div>
        <div className="absolute bottom-32 right-20 opacity-15">
          <TreePine className="h-40 w-40 text-white transform -rotate-12" />
        </div>
        <div className="absolute top-1/2 left-1/3 opacity-10">
          <Sprout className="h-24 w-24 text-white transform rotate-45" />
        </div>

        {/* Content */}
        <div className="relative z-10 flex flex-col justify-center items-center text-center text-white p-12 animate-slideInLeft">
          <div className="mb-8">
            <div className="bg-white/20 backdrop-blur-sm p-6 rounded-full mb-6 animate-float">
              <Building2 className="h-16 w-16 text-white" />
            </div>
            <h1 className="text-5xl font-bold mb-4 tracking-tight animate-fadeInUp">
              AgroSyste
            </h1>
            <p className="text-xl text-emerald-100 max-w-md leading-relaxed animate-fadeInUp">
              Gerencie sua produção agrícola com tecnologia de ponta e dados
              precisos
            </p>
          </div>

          <div className="grid grid-cols-3 gap-8 mt-12 opacity-80">
            <div className="text-center">
              <div className="bg-white/10 backdrop-blur-sm p-4 rounded-lg mb-3">
                <Leaf className="h-8 w-8 text-emerald-200 mx-auto" />
              </div>
              <p className="text-sm text-emerald-100">Sustentável</p>
            </div>
            <div className="text-center">
              <div className="bg-white/10 backdrop-blur-sm p-4 rounded-lg mb-3">
                <Sprout className="h-8 w-8 text-emerald-200 mx-auto" />
              </div>
              <p className="text-sm text-emerald-100">Inovador</p>
            </div>
            <div className="text-center">
              <div className="bg-white/10 backdrop-blur-sm p-4 rounded-lg mb-3">
                <TreePine className="h-8 w-8 text-emerald-200 mx-auto" />
              </div>
              <p className="text-sm text-emerald-100">Eficiente</p>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md animate-slideInRight">
          {/* Mobile Logo */}
          <div className="lg:hidden text-center mb-8 animate-fadeInUp">
            <div className="bg-gradient-to-r from-emerald-600 to-teal-600 p-4 rounded-full inline-block mb-4 animate-float">
              <Building2 className="h-12 w-12 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">AgroSyste</h1>
            <p className="text-gray-600">Sua plataforma agrícola inteligente</p>
          </div>

          {/* Form Card */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-8 transition-all duration-300 hover:shadow-3xl animate-fadeInUp">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                {registro ? "Criar Conta" : "Bem-vindo de volta"}
              </h2>
              <p className="text-gray-600">
                {registro
                  ? "Preencha seus dados para criar uma conta"
                  : "Entre com suas credenciais para acessar o sistema"}
              </p>
            </div>
            {registro ? (
              <Registro setRegistro={setRegistro} />
            ) : (
              <form onSubmit={handleSubmit(data)} className="space-y-6">
                <div className="space-y-4">
                  <div className="group">
                    <Label className="text-sm font-medium text-gray-700 mb-2 block">
                      E-mail
                    </Label>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      {...register("email")}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-200 transition-all duration-200 bg-white/50 backdrop-blur-sm"
                    />
                    {errors.email && (
                      <p className="text-red-500 text-sm mt-1 animate-pulse">
                        {errors.email.message}
                      </p>
                    )}
                  </div>

                  <div className="group">
                    <Label className="text-sm font-medium text-gray-700 mb-2 block">
                      Senha
                    </Label>
                    <div className="relative">
                      <Input
                        type={showPassword ? "text" : "password"}
                        placeholder="••••••••"
                        {...register("password")}
                        className="w-full px-4 py-3 pr-12 rounded-xl border border-gray-200 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-200 transition-all duration-200 bg-white/50 backdrop-blur-sm"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        {showPassword ? (
                          <EyeOff className="h-5 w-5" />
                        ) : (
                          <Eye className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                    {errors.password && (
                      <p className="text-red-500 text-sm mt-1 animate-pulse">
                        {errors.password.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="remember"
                      className="rounded border-gray-300"
                    />
                    <Label
                      htmlFor="remember"
                      className="text-sm text-gray-600 cursor-pointer"
                    >
                      Lembrar-me
                    </Label>
                  </div>
                  <button
                    type="button"
                    className="text-sm text-emerald-600 hover:text-emerald-700 font-medium transition-colors"
                  >
                    Esqueceu sua senha?
                  </button>
                </div>

                <Button
                  type="submit"
                  disabled={loading}
                  className="w-full py-3 px-4 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white font-medium rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {loading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <Spinner size="small" color="white" />
                      <span>Entrando...</span>
                    </div>
                  ) : (
                    "Entrar"
                  )}
                </Button>

                <div className="text-center pt-4">
                  <p className="text-gray-600">
                    Não tem uma conta?{" "}
                    <button
                      type="button"
                      onClick={handleRegistro}
                      className="text-emerald-600 hover:text-emerald-700 font-medium transition-colors"
                    >
                      Registre-se
                    </button>
                  </p>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
