"use client";
import Back from "@/components/back";
import TalhaoFormContainer from "@/components/forms/TalhaoFormContainer";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import ClienteService from "@/lib/services/clienteService";
import FazendaService from "@/lib/services/fazendaService";
import TalhaoService from "@/lib/services/talhaoService";
import { ArrowLeft, Map } from "lucide-react";
import { useRouter } from "next/navigation";
import { use, useEffect, useState } from "react";

export default function Talhao({ params }) {
  const { id } = use(params);
  const [clientes, setClientes] = useState([]);
  const [fazendas, setFazendas] = useState([]);
  const [talhao, setTalhao] = useState({});
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const fetchClientes = async () => {
    const clienteService = new ClienteService();
    const clientes = await clienteService.ListarTodosClientes();
    if (!clientes) {
      return toast({
        title: "Erro",
        description: "Erro ao listar clientes",
        variant: "destructive",
      });
    }
    setClientes(clientes);
  };

  const fetchFazendas = async () => {
    const fazendaService = new FazendaService();
    const fazendas = await fazendaService.ListarTodasFazendas();
    if (!fazendas) {
      return toast({
        title: "Erro",
        description: "Erro ao listar fazendas",
        variant: "destructive",
      });
    }
    setFazendas(fazendas);
  };

  const fetchTalhao = async (id) => {
    const talhaoService = new TalhaoService();
    const talhao = await talhaoService.BuscarTalhao(id);
    if (!talhao) {
      return toast({
        title: "Erro",
        description: "Erro ao obter talhão",
        variant: "destructive",
      });
    }
    setTalhao(talhao);
  };

  useEffect(() => {
    fetchClientes();
    fetchFazendas();
    fetchTalhao(id);
  }, [id]);

  const onSubmit = async (data) => {
    setLoading(true);
    let obj = {
      ...data,
      id: id,
    };
    const talhaoService = new TalhaoService();
    const response = await talhaoService.AtualizarTalhao(obj);
    if (!response) {
      return toast({
        title: "Erro",
        description: "Erro ao editar talhão",
        variant: "destructive",
      });
    }
    toast({
      title: "Sucesso",
      description: "Talhão editado com sucesso",
      variant: "default",
    });
    router.push("/admin/talhao");
  };

  return (
    <div className="min-h-screen p-4">
      {loading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
          <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
            <Spinner message="carregando..." className="text-white w-10 h-10" />
          </div>
        </div>
      )}

      {/* Header moderno - compacto */}
      <div className="mb-4 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <Back
                  icon={<ArrowLeft className="h-4 w-4" />}
                  text="Voltar"
                  href="/admin/talhao"
                />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Editar Talhão
              </h1>
              <p className="text-gray-600 text-sm">
                Atualize as informações do talhão
              </p>
            </div>
            <div className="mt-2 lg:mt-0 flex items-center">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-lg flex items-center space-x-2 text-sm">
                <Map className="h-4 w-4" />
                <span>Sistema Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo principal - compacto */}
      <div className="animate-slideInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                <Map className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900">
                  Editar Dados do Talhão
                </h3>
                <p className="text-gray-600 text-xs">
                  Atualize as informações necessárias
                </p>
              </div>
            </div>
          </div>

          <div className="p-4">
            <TalhaoFormContainer
              initialValue={talhao}
              clientes={clientes}
              fazendas={fazendas}
              onSubmit={onSubmit}
              setLoadingFazendas={setLoading}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
