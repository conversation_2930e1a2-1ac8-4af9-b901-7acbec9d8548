"use client";
import Back from "@/components/back";
import FazendaForm from "@/components/forms/fazendaForm";
import Map from "@/components/maps/map";
import { Button } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import { fazendaSchema } from "@/lib/schema/zod";
import ClienteService from "@/lib/services/clienteService";
import FazendaService from "@/lib/services/fazendaService";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft, Users } from "lucide-react";
import { useRouter } from "next/navigation";
import { use, useEffect, useState } from "react";
import { useForm } from "react-hook-form";

export default function Fazenda({ params }) {
  const {
    register,
    handleSubmit,
    setValue,
    control,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(fazendaSchema),
  });

  const { id } = use(params);

  const [location, setLocation] = useState(null);
  const [fazenda, setFazenda] = useState({});
  const [clientes, setClientes] = useState([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const onSubmit = async (data) => {
    setLoading(true);
    data.lat = location?.lat ?? fazenda?.lat;
    data.lng = location?.lng ?? fazenda?.lng;
    data.id = id;
    const fazendaService = new FazendaService();
    const cadastrar = await fazendaService.AtualizarFazenda(data);
    if (!cadastrar) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao editar fazenda",
        variant: "destructive",
      });
    }
    setLoading(false);
    toast({
      title: "Sucesso",
      description: "Fazenda editada com sucesso",
      variant: "success",
    });
    return router.push("/admin/fazenda");
  };

  const fetchClientes = async () => {
    setLoading(true);
    const clienteService = new ClienteService();
    const clientes = await clienteService.ListarTodosClientes();
    if (!clientes) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao listar clientes",
        variant: "destructive",
      });
    }
    setLoading(true);
    setClientes(clientes);
  };

  const fetchFazenda = async (id) => {
    setLoading(true);
    const fazendaService = new FazendaService();
    const fazenda = await fazendaService.BuscarFazenda(id);
    if (!fazenda) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao buscar fazenda",
        variant: "destructive",
      });
    }
    setLoading(false);
    setFazenda(fazenda);
    setLocation({ lat: fazenda.lat, lng: fazenda.lng });
  };

  useEffect(() => {
    fetchClientes();
    fetchFazenda(id);
  }, [id]);

  return (
    <div className="min-h-screen p-4">
      {/* Header moderno - compacto */}
      <div className="mb-4 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <Back
                  icon={<ArrowLeft className="h-4 w-4" />}
                  text="Voltar"
                  href="/admin/fazenda"
                />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Editar Fazenda
              </h1>
              <p className="text-gray-600 text-sm">
                Atualize as informações da fazenda
              </p>
            </div>
            <div className="mt-2 lg:mt-0 flex items-center">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-lg flex items-center space-x-2 text-sm">
                <Users className="h-4 w-4" />
                <span>Sistema Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo principal - compacto */}
      <div className="animate-slideInUp">
        {loading ? (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
            <div className="flex flex-col justify-center items-center space-y-3">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                <Spinner className="text-white" size="medium" />
              </div>
              <div className="text-center">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  Carregando dados da fazenda...
                </h3>
                <p className="text-gray-600 text-sm">
                  Aguarde enquanto buscamos as informações
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                  <Users className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900">
                    Editar Dados da Fazenda
                  </h3>
                  <p className="text-gray-600 text-xs">
                    Atualize as informações necessárias
                  </p>
                </div>
              </div>
            </div>

            <div className="p-4">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <FazendaForm
                  control={control}
                  register={register}
                  initialValue={fazenda}
                  errors={errors}
                  clientes={clientes}
                  setValue={setValue}
                  children={
                    <Map initial={fazenda} onLocationSelect={setLocation} />
                  }
                />

                <div className="flex justify-end pt-4 border-t border-gray-200">
                  <Button
                    type="submit"
                    disabled={loading}
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center space-x-2 min-w-[140px]"
                  >
                    {loading ? (
                      <>
                        <Spinner size="small" className="text-white" />
                        <span>Salvando...</span>
                      </>
                    ) : (
                      <>
                        <Users className="h-4 w-4" />
                        <span>Salvar Alterações</span>
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
