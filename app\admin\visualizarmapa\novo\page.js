"use client";
import Back from "@/components/back";
import VisualizarMapaFormContainer from "@/components/forms/visualizarMapaFormContainer";
import { Spinner } from "@/components/ui/spinner";
import { Activity, ArrowLeft, Map } from "lucide-react";
import { useState } from "react";

export default function VisualizarMapa() {
  const [loading, setLoading] = useState(false);
  const [talhao, setTalhao] = useState({});

  return (
    <div className="min-h-screen p-4">
      {loading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
          <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
            <Spinner className="text-white w-10 h-10" />
          </div>
        </div>
      )}

      {/* Header moderno - compacto */}
      <div className="mb-4 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <Back
                  icon={<ArrowLeft className="h-4 w-4" />}
                  text="Voltar"
                  href="/admin/visualizarmapa"
                />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Gerar Nova Coleta
              </h1>
              <p className="text-gray-600 text-sm">
                Configure os parâmetros para gerar uma nova coleta de solo
              </p>
            </div>
            <div className="mt-2 lg:mt-0 flex items-center">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-lg flex items-center space-x-2 text-sm">
                <Activity className="h-4 w-4" />
                <span>Sistema Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo principal - compacto */}
      <div className="animate-slideInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                <Map className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900">
                  Configuração da Coleta
                </h3>
                <p className="text-gray-600 text-xs">
                  Defina os parâmetros para gerar sua coleta
                </p>
              </div>
            </div>
          </div>

          <div className="p-4">
            <VisualizarMapaFormContainer />
          </div>
        </div>
      </div>
    </div>
  );
}
