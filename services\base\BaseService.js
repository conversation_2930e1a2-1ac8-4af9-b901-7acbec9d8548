import { ApiUtils } from '@/utils/api';

export class BaseService {
  constructor(baseUrl, endpoint) {
    this.baseUrl = baseUrl;
    this.endpoint = endpoint;
  }

  buildUrl(path = '') {
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    return cleanPath ? `${this.endpoint}/${cleanPath}` : this.endpoint;
  }

  async get(path = '', params = {}, includeAuth = true) {
    const url = this.buildUrl(path);
    return ApiUtils.get(`${this.baseUrl}/${url}`, includeAuth, params);
  }

  async post(path = '', data = {}, includeAuth = true) {
    const url = this.buildUrl(path);
    return ApiUtils.post(`${this.baseUrl}/${url}`, data, includeAuth);
  }

  async put(path = '', data = {}, includeAuth = true) {
    const url = this.buildUrl(path);
    return ApiUtils.put(`${this.baseUrl}/${url}`, data, includeAuth);
  }

  async delete(path = '', includeAuth = true) {
    const url = this.buildUrl(path);
    return ApiUtils.delete(`${this.baseUrl}/${url}`, includeAuth);
  }

  async postFormData(path = '', formData, includeAuth = true) {
    const url = this.buildUrl(path);
    return ApiUtils.postFormData(`${this.baseUrl}/${url}`, formData, includeAuth);
  }

  async putFormData(path = '', formData, includeAuth = true) {
    const url = this.buildUrl(path);
    return ApiUtils.putFormData(`${this.baseUrl}/${url}`, formData, includeAuth);
  }
}

export class CrudService extends BaseService {
  async list(params = {}) {
    return this.get('', params);
  }

  async getById(id) {
    return this.get(id.toString());
  }

  async create(data) {
    return this.post('', data);
  }

  async update(id, data) {
    return this.put(id.toString(), data);
  }

  async remove(id) {
    return this.delete(id.toString());
  }

  async search(query, params = {}) {
    return this.get('search', { q: query, ...params });
  }

  async count(params = {}) {
    return this.get('count', params);
  }

  async exists(id) {
    try {
      await this.getById(id);
      return true;
    } catch {
      return false;
    }
  }
}

export class PaginatedService extends CrudService {
  async getPaginated(page = 1, pageSize = 10, params = {}) {
    return this.get('', {
      page,
      pageSize,
      ...params
    });
  }

  async searchPaginated(query, page = 1, pageSize = 10, params = {}) {
    return this.get('search', {
      q: query,
      page,
      pageSize,
      ...params
    });
  }
}

export class FileUploadService extends BaseService {
  async uploadFile(file, path = 'upload') {
    const formData = new FormData();
    formData.append('file', file);
    return this.postFormData(path, formData);
  }

  async uploadFiles(files, path = 'upload') {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`file${index}`, file);
    });
    return this.postFormData(path, formData);
  }

  async uploadWithData(file, data, path = 'upload') {
    const formData = new FormData();
    formData.append('file', file);
    
    Object.entries(data).forEach(([key, value]) => {
      formData.append(key, value);
    });
    
    return this.postFormData(path, formData);
  }
}

export class AuthenticatedService extends BaseService {
  constructor(baseUrl, endpoint) {
    super(baseUrl, endpoint);
    this.includeAuth = true;
  }

  async get(path = '', params = {}) {
    return super.get(path, params, this.includeAuth);
  }

  async post(path = '', data = {}) {
    return super.post(path, data, this.includeAuth);
  }

  async put(path = '', data = {}) {
    return super.put(path, data, this.includeAuth);
  }

  async delete(path = '') {
    return super.delete(path, this.includeAuth);
  }

  async postFormData(path = '', formData) {
    return super.postFormData(path, formData, this.includeAuth);
  }

  async putFormData(path = '', formData) {
    return super.putFormData(path, formData, this.includeAuth);
  }
}

export const createService = (baseUrl, endpoint, options = {}) => {
  const {
    withAuth = true,
    withCrud = true,
    withPagination = false,
    withFileUpload = false
  } = options;

  let ServiceClass = BaseService;

  if (withFileUpload) {
    ServiceClass = FileUploadService;
  } else if (withPagination) {
    ServiceClass = PaginatedService;
  } else if (withCrud) {
    ServiceClass = CrudService;
  }

  if (withAuth) {
    class AuthService extends ServiceClass {
      constructor() {
        super(baseUrl, endpoint);
        this.includeAuth = true;
      }

      async get(path = '', params = {}) {
        return super.get(path, params, this.includeAuth);
      }

      async post(path = '', data = {}) {
        return super.post(path, data, this.includeAuth);
      }

      async put(path = '', data = {}) {
        return super.put(path, data, this.includeAuth);
      }

      async delete(path = '') {
        return super.delete(path, this.includeAuth);
      }

      async postFormData(path = '', formData) {
        return super.postFormData(path, formData, this.includeAuth);
      }

      async putFormData(path = '', formData) {
        return super.putFormData(path, formData, this.includeAuth);
      }
    }

    return new AuthService();
  }

  return new ServiceClass(baseUrl, endpoint);
};
