"use client";

import { Sidebar } from "@/components/layout/sidebar";
import { useState } from "react";
import { cn } from "@/lib/utils";

export default function AdminLayout({ children }) {
    const [desktopOpen, setDesktopOpen] = useState(true);

    return (
        <div className="flex min-h-screen w-full">
            <Sidebar onSidebarToggle={(isOpen) => setDesktopOpen(isOpen)} />
            <div className={cn(
                "hidden lg:block transition-all duration-300",
                desktopOpen ? "w-64" : "w-16"
            )} />
            <main className={cn(
                "flex-1 p-4 lg:p-8 transition-all duration-300",
                desktopOpen ? "lg:ml-0" : "lg:ml-0"
            )}>

export default function AdminLayout({ children }) {
    return (
        <div className="flex min-h-screen">
            <Sidebar />
            <div className="hidden lg:block lg:w-64" />
            <main className="flex-1 p-4 lg:p-8">
                {children}
            </main>
        </div>
    );
}
}
