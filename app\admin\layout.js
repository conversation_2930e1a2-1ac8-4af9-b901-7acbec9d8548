"use client";

import { Sidebar } from "@/components/layout/sidebar";
import { cn } from "@/lib/utils";
import { useState } from "react";

export default function AdminLayout({ children }) {
  const [desktopOpen, setDesktopOpen] = useState(true);

  return (
    <div className="flex min-h-screen w-full bg-gradient-to-br from-emerald-50 via-teal-50 to-green-100">
      <Sidebar onSidebarToggle={(isOpen) => setDesktopOpen(isOpen)} />
      <div
        className={cn(
          "hidden lg:block transition-all duration-300",
          desktopOpen ? "w-64" : "w-16"
        )}
      />
      <main
        className={cn(
          "flex-1 transition-all duration-300 overflow-hidden",
          desktopOpen ? "lg:ml-0" : "lg:ml-0"
        )}
      >
        {children}
      </main>
    </div>
  );
}
