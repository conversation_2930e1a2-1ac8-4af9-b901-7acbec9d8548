import React from 'react';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { Loader2 } from 'lucide-react';

/**
 * Beautiful Professional Spinner Component
 * Redesigned with modern aesthetics and smooth animations
 */

const spinnerContainerVariants = cva(
    [
        // Base: Layout e transições suaves
        "inline-flex flex-col items-center justify-center",
        "select-none font-medium antialiased",
        "transition-all duration-500 ease-out transform-gpu"
    ],
    {
        variants: {
            show: {
                true: "opacity-100 scale-100 translate-y-0",
                false: "opacity-0 scale-90 translate-y-2 pointer-events-none"
            },
            size: {
                small: "gap-2 min-w-[70px]",
                medium: "gap-3 min-w-[90px]",
                large: "gap-4 min-w-[110px]"
            },
            variant: {
                default: "p-2",
                contained: [
                    "p-6 rounded-2xl backdrop-blur-md",
                    "bg-gradient-to-br from-white/90 to-white/70",
                    "border border-white/30 shadow-xl shadow-black/5",
                    "ring-1 ring-black/5"
                ],
                glass: [
                    "p-5 rounded-xl backdrop-blur-xl",
                    "bg-white/10 border border-white/20",
                    "shadow-2xl shadow-emerald-500/10"
                ]
            }
        },
        defaultVariants: {
            show: true,
            size: "medium",
            variant: "default"
        }
    }
);

const spinnerIconVariants = cva(
    [
        // Base: Animação e efeitos visuais
        "flex-shrink-0 drop-shadow-sm",
        "transition-all duration-700 ease-in-out"
    ],
    {
        variants: {
            size: {
                small: "w-5 h-5",
                medium: "w-6 h-6",
                large: "w-8 h-8"
            },
            animation: {
                spin: "animate-spin",
                pulse: "animate-pulse",
                bounce: "animate-bounce"
            }
        },
        defaultVariants: {
            size: "medium",
            animation: "spin"
        }
    }
);

const spinnerTextVariants = cva(
    [
        // Base: Tipografia elegante
        "text-center font-medium tracking-wide",
        "transition-all duration-300 ease-out",
        "max-w-[200px] leading-relaxed"
    ],
    {
        variants: {
            size: {
                small: "text-xs",
                medium: "text-sm",
                large: "text-base"
            },
            variant: {
                default: "text-foreground/70",
                muted: "text-muted-foreground/80",
                accent: "text-emerald-600/80"
            }
        },
        defaultVariants: {
            size: "medium",
            variant: "default"
        }
    }
);

export function Spinner({
    size = 'medium',
    variant = 'default',
    show = true,
    children,
    className,
    message,
    // Backward compatibility
    label,
    ...props
}) {
    const displayText = label || message;

    return (
        <div
            className={cn(
                spinnerContainerVariants({
                    show,
                    size,
                    variant
                }),
                className
            )}
            role="status"
            aria-label={displayText ? `Loading: ${displayText}` : 'Loading'}
            {...props}
        >
            {/* Spinner Icon com efeito de brilho */}
            <div className="relative">
                {/* Ícone principal */}
                <Loader2
                    className={cn(
                        spinnerIconVariants({ size, animation: 'spin' }),
                        "text-emerald-500"
                    )}
                    aria-hidden="true"
                />

                {/* Efeito de brilho rotativo */}
                <div className="absolute inset-0 opacity-30">
                    <Loader2
                        className={cn(
                            spinnerIconVariants({ size, animation: 'spin' }),
                            "text-emerald-300 animate-spin",
                            "animation-delay-300"
                        )}
                        style={{ animationDuration: '1.5s' }}
                        aria-hidden="true"
                    />
                </div>

                {/* Pulso de fundo */}
                <div className="absolute inset-0 -z-10 opacity-20">
                    <div className={cn(
                        spinnerIconVariants({ size }),
                        "bg-emerald-500/20 rounded-full animate-ping"
                    )} />
                </div>
            </div>

            {/* Texto com animação de entrada */}
            {displayText && (
                <p className={cn(
                    spinnerTextVariants({
                        size,
                        variant: variant === 'glass' ? 'accent' : 'default'
                    }),
                    "animate-fade-in-up"
                )}>
                    {displayText}
                </p>
            )}

            {/* Conteúdo adicional */}
            {children}

            {/* Screen reader text */}
            <span className="sr-only">
                {displayText ? `Loading: ${displayText}` : 'Loading, please wait'}
            </span>
        </div>
    );
}