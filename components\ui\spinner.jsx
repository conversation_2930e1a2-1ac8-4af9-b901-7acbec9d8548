import React from 'react';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { Loader2 } from 'lucide-react';

const spinnerVariants = cva('flex flex-col items-center justify-center gap-3', {
    variants: {
        show: {
            true: 'flex',
            false: 'hidden',
        },
    },
    defaultVariants: {
        show: true,
    },
});

const loaderVariants = cva('animate-spin transition-all duration-200', {
    variants: {
        size: {
            xs: 'w-4 h-4',
            small: 'w-6 h-6',
            medium: 'w-8 h-8',
            large: 'w-12 h-12',
            xl: 'w-16 h-16',
        },
        color: {
            primary: 'text-primary',
            white: 'text-white',
            current: 'text-current',
            muted: 'text-muted-foreground',
        },
    },
    defaultVariants: {
        size: 'medium',
        color: 'primary',
    },
});

const messageVariants = cva('text-center font-medium', {
    variants: {
        size: {
            xs: 'text-xs',
            small: 'text-sm',
            medium: 'text-sm',
            large: 'text-base',
            xl: 'text-lg',
        },
        color: {
            primary: 'text-foreground',
            white: 'text-white',
            current: 'text-current',
            muted: 'text-muted-foreground',
        },
    },
    defaultVariants: {
        size: 'medium',
        color: 'muted',
    },
});

export function Spinner({
    size = 'medium',
    color = 'primary',
    show = true,
    children,
    className,
    message,
    messageColor,
    ...props
}) {
    const finalMessageColor = messageColor || (color === 'white' ? 'white' : 'muted');

    return (
        <div className={cn(spinnerVariants({ show }), className)} {...props}>
            <Loader2
                className={cn(loaderVariants({ size, color }))}
            />
            {message && (
                <p className={cn(messageVariants({ size, color: finalMessageColor }))}>
                    {message}
                </p>
            )}
            {children}
        </div>
    );
}