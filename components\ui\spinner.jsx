import React from 'react';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { Loader2 } from 'lucide-react';

const spinnerContainerVariants = cva(
    'inline-flex flex-col items-center justify-center min-w-0 w-full',
    {
        variants: {
            show: {
                true: 'flex',
                false: 'hidden',
            },
            size: {
                small: 'gap-2 p-2',
                medium: 'gap-3 p-3',
                large: 'gap-4 p-4',
            },
        },
        defaultVariants: {
            show: true,
            size: 'medium',
        },
    }
);

const spinnerIconVariants = cva(
    'animate-spin transition-transform duration-700 ease-in-out flex-shrink-0',
    {
        variants: {
            size: {
                small: 'w-4 h-4',
                medium: 'w-6 h-6',
                large: 'w-10 h-10',
            },
        },
        defaultVariants: {
            size: 'medium',
        },
    }
);

const spinnerMessageVariants = cva(
    'text-center font-medium leading-tight break-words hyphens-auto',
    {
        variants: {
            size: {
                small: 'text-xs max-w-20',
                medium: 'text-sm max-w-32',
                large: 'text-base max-w-40',
            },
        },
        defaultVariants: {
            size: 'medium',
        },
    }
);

export function Spinner({
    size = 'medium',
    show = true,
    children,
    className,
    message,
    ...props
}) {
    return (
        <div className={cn(spinnerContainerVariants({ show, size }), className)} {...props}>
            {/* Container do ícone com efeito visual */}
            <div className="relative">
                <Loader2
                    className={cn(spinnerIconVariants({ size }), 'text-current')}
                />
                {/* Efeito de brilho rotativo */}
                <div className="absolute inset-0 opacity-30">
                    <Loader2
                        className={cn(
                            spinnerIconVariants({ size }),
                            'text-current animate-spin',
                            'animation-delay-150'
                        )}
                    />
                </div>
            </div>

            {/* Mensagem com container próprio */}
            {message && (
                <div className="w-full flex justify-center">
                    <p className={cn(
                        spinnerMessageVariants({ size }),
                        'text-current opacity-80'
                    )}>
                        {message}
                    </p>
                </div>
            )}

            {children}
        </div>
    );
}