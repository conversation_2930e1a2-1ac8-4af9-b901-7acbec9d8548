import React from 'react';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { Loader2 } from 'lucide-react';

const spinnerVariants = cva('flex flex-col items-center justify-center', {
    variants: {
        show: {
            true: 'flex',
            false: 'hidden',
        },
        size: {
            small: 'gap-2',
            medium: 'gap-2.5',
            large: 'gap-3',
        },
    },
    defaultVariants: {
        show: true,
        size: 'medium',
    },
});

const loaderVariants = cva('animate-spin', {
    variants: {
        size: {
            small: 'w-4 h-4',
            medium: 'w-6 h-6',
            large: 'w-8 h-8',
        },
    },
    defaultVariants: {
        size: 'medium',
    },
});

const messageVariants = cva('text-center font-normal', {
    variants: {
        size: {
            small: 'text-xs',
            medium: 'text-sm',
            large: 'text-sm',
        },
    },
    defaultVariants: {
        size: 'medium',
    },
});

export function Spinner({
    size = 'medium',
    show = true,
    children,
    className,
    message,
    ...props
}) {
    return (
        <div className={cn(spinnerVariants({ show, size }), className)} {...props}>
            <Loader2
                className={cn(loaderVariants({ size }), 'text-current')}
            />
            {message && (
                <p className={cn(messageVariants({ size }), 'text-current opacity-70')}>
                    {message}
                </p>
            )}
            {children}
        </div>
    );
}