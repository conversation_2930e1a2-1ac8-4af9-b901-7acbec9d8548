import React from 'react';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { Loader2 } from 'lucide-react';

const spinnerVariants = cva('flex flex-col items-center justify-center', {
    variants: {
        show: {
            true: 'flex',
            false: 'hidden',
        },
        size: {
            small: 'gap-2',
            medium: 'gap-3',
            large: 'gap-4',
        },
    },
    defaultVariants: {
        show: true,
        size: 'medium',
    },
});

const loaderVariants = cva('animate-spin transition-all duration-300 ease-in-out drop-shadow-sm', {
    variants: {
        size: {
            small: 'w-5 h-5',
            medium: 'w-8 h-8',
            large: 'w-12 h-12',
        },
    },
    defaultVariants: {
        size: 'medium',
    },
});

const messageVariants = cva('text-center font-medium leading-relaxed max-w-xs', {
    variants: {
        size: {
            small: 'text-xs',
            medium: 'text-sm',
            large: 'text-base',
        },
    },
    defaultVariants: {
        size: 'medium',
    },
});

export function Spinner({
    size = 'medium',
    show = true,
    children,
    className,
    message,
    ...props
}) {
    return (
        <div className={cn(spinnerVariants({ show, size }), className)} {...props}>
            <div className="relative">
                <Loader2
                    className={cn(loaderVariants({ size }), 'text-current')}
                />
                {/* Efeito de brilho sutil */}
                <div className={cn(
                    "absolute inset-0 animate-pulse opacity-20",
                    loaderVariants({ size })
                )}>
                    <Loader2 className="w-full h-full text-current" />
                </div>
            </div>
            {message && (
                <p className={cn(
                    messageVariants({ size }),
                    'text-current opacity-75 animate-fade-in'
                )}>
                    {message}
                </p>
            )}
            {children}
        </div>
    );
}