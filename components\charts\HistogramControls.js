import React from 'react';
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const HistogramControls = ({ 
    bins, 
    setBins, 
    selectedAttribute, 
    setSelectedAttribute, 
    availableAttributes,
    color,
    setColor 
}) => {
    const colorOptions = [
        { value: '#3b82f6', label: 'Azul', color: '#3b82f6' },
        { value: '#ef4444', label: 'Vermelho', color: '#ef4444' },
        { value: '#10b981', label: 'Verde', color: '#10b981' },
        { value: '#f59e0b', label: '<PERSON><PERSON>', color: '#f59e0b' },
        { value: '#8b5cf6', label: 'Roxo', color: '#8b5cf6' },
        { value: '#06b6d4', label: '<PERSON><PERSON>', color: '#06b6d4' },
        { value: '#84cc16', label: 'Lima', color: '#84cc16' },
        { value: '#f97316', label: 'Laranja', color: '#f97316' },
    ];

    return (
        <div className="space-y-4 p-4 bg-muted rounded-md">
            <h4 className="font-medium">Configurações do Histograma</h4>
            
            {/* Seleção do Atributo */}
            <div className="space-y-2">
                <Label htmlFor="attribute-select">Atributo</Label>
                <Select value={selectedAttribute} onValueChange={setSelectedAttribute}>
                    <SelectTrigger id="attribute-select">
                        <SelectValue placeholder="Selecione um atributo..." />
                    </SelectTrigger>
                    <SelectContent>
                        {availableAttributes.map((attribute, index) => (
                            <SelectItem key={index} value={attribute}>
                                {attribute}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>

            {/* Controle de Bins */}
            <div className="space-y-2">
                <Label htmlFor="bins-slider">
                    Número de Intervalos (Bins): {bins}
                </Label>
                <Slider
                    id="bins-slider"
                    min={5}
                    max={30}
                    step={1}
                    value={[bins]}
                    onValueChange={(value) => setBins(value[0])}
                    className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                    <span>5</span>
                    <span>30</span>
                </div>
            </div>

            {/* Seleção de Cor */}
            <div className="space-y-2">
                <Label htmlFor="color-select">Cor</Label>
                <Select value={color} onValueChange={setColor}>
                    <SelectTrigger id="color-select">
                        <SelectValue placeholder="Selecione uma cor..." />
                    </SelectTrigger>
                    <SelectContent>
                        {colorOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                                <div className="flex items-center gap-2">
                                    <div 
                                        className="w-4 h-4 rounded-full border" 
                                        style={{ backgroundColor: option.color }}
                                    />
                                    {option.label}
                                </div>
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>
        </div>
    );
};

export default HistogramControls;
