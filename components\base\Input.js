"use client";
import { forwardRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { Eye, EyeOff } from 'lucide-react';
import { FormField, FormLabel, FormError, FormHelperText } from './Form';

export const BaseInput = forwardRef(({ 
  className,
  type = "text",
  error,
  ...props 
}, ref) => {
  return (
    <input
      type={type}
      className={cn(
        "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        error && "border-red-500 focus-visible:ring-red-500",
        className
      )}
      ref={ref}
      {...props}
    />
  );
});

BaseInput.displayName = "BaseInput";

export const Input = forwardRef(({ 
  label,
  error,
  helperText,
  required,
  className,
  fieldClassName,
  ...props 
}, ref) => {
  return (
    <FormField className={fieldClassName}>
      {label && (
        <FormLabel required={required}>
          {label}
        </FormLabel>
      )}
      <BaseInput
        ref={ref}
        error={error}
        className={className}
        {...props}
      />
      <FormError>{error}</FormError>
      <FormHelperText>{helperText}</FormHelperText>
    </FormField>
  );
});

Input.displayName = "Input";

export const PasswordInput = forwardRef(({ 
  label = "Senha",
  showToggle = true,
  ...props 
}, ref) => {
  const [showPassword, setShowPassword] = useState(false);

  const togglePassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <FormField className={props.fieldClassName}>
      {label && (
        <FormLabel required={props.required}>
          {label}
        </FormLabel>
      )}
      <div className="relative">
        <BaseInput
          ref={ref}
          type={showPassword ? "text" : "password"}
          error={props.error}
          className={cn("pr-10", props.className)}
          {...props}
        />
        {showToggle && (
          <button
            type="button"
            onClick={togglePassword}
            className="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground hover:text-foreground"
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>
        )}
      </div>
      <FormError>{props.error}</FormError>
      <FormHelperText>{props.helperText}</FormHelperText>
    </FormField>
  );
});

PasswordInput.displayName = "PasswordInput";

export const NumberInput = forwardRef(({ 
  min,
  max,
  step = 1,
  ...props 
}, ref) => {
  return (
    <Input
      ref={ref}
      type="number"
      min={min}
      max={max}
      step={step}
      {...props}
    />
  );
});

NumberInput.displayName = "NumberInput";

export const EmailInput = forwardRef((props, ref) => {
  return (
    <Input
      ref={ref}
      type="email"
      label="Email"
      placeholder="<EMAIL>"
      {...props}
    />
  );
});

EmailInput.displayName = "EmailInput";

export const PhoneInput = forwardRef(({ 
  mask = true,
  ...props 
}, ref) => {
  const handleChange = (e) => {
    if (mask) {
      let value = e.target.value.replace(/\D/g, '');
      
      if (value.length <= 10) {
        value = value.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
      } else {
        value = value.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
      }
      
      e.target.value = value;
    }
    
    props.onChange?.(e);
  };

  return (
    <Input
      ref={ref}
      type="tel"
      label="Telefone"
      placeholder="(11) 99999-9999"
      onChange={handleChange}
      {...props}
    />
  );
});

PhoneInput.displayName = "PhoneInput";

export const CurrencyInput = forwardRef(({ 
  currency = "BRL",
  ...props 
}, ref) => {
  const handleChange = (e) => {
    let value = e.target.value.replace(/\D/g, '');
    value = (parseInt(value) / 100).toFixed(2);
    
    if (value === "0.00") {
      e.target.value = "";
    } else {
      e.target.value = new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: currency
      }).format(value);
    }
    
    props.onChange?.(e);
  };

  return (
    <Input
      ref={ref}
      type="text"
      onChange={handleChange}
      {...props}
    />
  );
});

CurrencyInput.displayName = "CurrencyInput";

export const SearchInput = forwardRef(({ 
  onSearch,
  debounceMs = 300,
  ...props 
}, ref) => {
  const [timeoutId, setTimeoutId] = useState(null);

  const handleChange = (e) => {
    const value = e.target.value;
    
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    const newTimeoutId = setTimeout(() => {
      onSearch?.(value);
    }, debounceMs);
    
    setTimeoutId(newTimeoutId);
    props.onChange?.(e);
  };

  return (
    <Input
      ref={ref}
      type="search"
      placeholder="Pesquisar..."
      onChange={handleChange}
      {...props}
    />
  );
});

SearchInput.displayName = "SearchInput";
