import { Input } from "../ui/input";
import { Label } from "../ui/label";

export default function RegistroForm({ register, errors }) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div className="group">
          <Label
            htmlFor="nome"
            className="text-sm font-medium text-gray-700 mb-2 block"
          >
            Nome Completo
          </Label>
          <Input
            type="text"
            id="nome"
            placeholder="Ex: <PERSON>"
            {...register("nomeCompleto")}
            className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-200 transition-all duration-200 bg-white/50 backdrop-blur-sm"
          />
          {errors.nomeCompleto && (
            <p className="text-red-500 text-sm mt-1 animate-pulse">
              {errors.nomeCompleto.message}
            </p>
          )}
        </div>
        <div className="group">
          <Label
            htmlFor="telefone"
            className="text-sm font-medium text-gray-700 mb-2 block"
          >
            Telefone
          </Label>
          <Input
            type="text"
            id="telefone"
            placeholder="(11) 99999-9999"
            {...register("telefone")}
            className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-200 transition-all duration-200 bg-white/50 backdrop-blur-sm"
          />
          {errors.telefone && (
            <p className="text-red-500 text-sm mt-1 animate-pulse">
              {errors.telefone.message}
            </p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div className="group">
          <Label
            htmlFor="cpf"
            className="text-sm font-medium text-gray-700 mb-2 block"
          >
            CPF
          </Label>
          <Input
            type="text"
            id="cpf"
            placeholder="000.000.000-00"
            {...register("cpf")}
            className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-200 transition-all duration-200 bg-white/50 backdrop-blur-sm"
          />
          {errors.cpf && (
            <p className="text-red-500 text-sm mt-1 animate-pulse">
              {errors.cpf.message}
            </p>
          )}
        </div>
        <div className="group">
          <Label
            htmlFor="email"
            className="text-sm font-medium text-gray-700 mb-2 block"
          >
            E-mail
          </Label>
          <Input
            type="email"
            id="email"
            placeholder="<EMAIL>"
            {...register("email")}
            className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-200 transition-all duration-200 bg-white/50 backdrop-blur-sm"
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1 animate-pulse">
              {errors.email.message}
            </p>
          )}
        </div>
      </div>

      <div className="group">
        <Label
          htmlFor="senha"
          className="text-sm font-medium text-gray-700 mb-2 block"
        >
          Senha
        </Label>
        <Input
          type="password"
          id="senha"
          placeholder="••••••••"
          {...register("senha")}
          className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-200 transition-all duration-200 bg-white/50 backdrop-blur-sm"
        />
        {errors.senha && (
          <p className="text-red-500 text-sm mt-1 animate-pulse">
            {errors.senha.message}
          </p>
        )}
      </div>
    </div>
  );
}
