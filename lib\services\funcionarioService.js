import httpClient from "../http/httpClient";

export default class FuncionarioService {
  async listarFuncionarios(page) {
    const response = await httpClient.get(`usuario/funcionario?${page}`, true);
    if (response.status !== 200) {
      return false;
    }
    const funcionarios = await response.json();
    return funcionarios;
  }

  async CadastrarFuncionario(funcionario) {
    const response = await httpClient.post(
      "usuario/cadastrar/funcionario",
      funcionario,
      true
    );

    if (response.status === 409) {
      let erro = await response.json();
      return [{ status: false, message: erro.message }];
    }
    if (response.status !== 200) {
      return [{ status: false, message: "Erro ao cadastrar funcionario" }];
    }
    return [{ status: true, message: "Funcionario cadastrado com sucesso" }];
  }

  async BuscarFuncionario(id) {
    const response = await httpClient.get(
      `usuario/funcionario/buscar/${id}`,
      true
    );
    if (response.status !== 200) {
      return false;
    }
    const funcionario = await response.json();

    return {
      ...funcionario,
      nomeCompleto: funcionario.nome,
    };
  }

  async AtualizarFuncionario(funcionario) {
    const funcionarioData = {
      ...funcionario,
      nome: funcionario.nomeCompleto,
    };

    const response = await httpClient.put(
      "usuario/funcionario/atualizar",
      funcionarioData,
      true
    );

    if (response.status === 409) {
      let erro = await response.json();
      return [{ status: false, message: erro.message }];
    }
    if (response.status !== 200) {
      return [{ status: false, message: "Erro ao atualizar funcionário" }];
    }
    return [{ status: true, message: "Funcionário atualizado com sucesso" }];
  }

  async DeletarFuncionario(id) {
    const response = await httpClient.delete(
      `usuario/funcionario/deletar/${id}`,
      null,
      true
    );
    if (response.status !== 200) {
      return false;
    }
    return true;
  }
}
