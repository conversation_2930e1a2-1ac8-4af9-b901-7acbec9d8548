"use client";
import { forwardRef } from 'react';
import { cn } from '@/lib/utils';

export const Form = forwardRef(({ 
  children, 
  onSubmit, 
  className,
  ...props 
}, ref) => {
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit?.(e);
  };

  return (
    <form
      ref={ref}
      onSubmit={handleSubmit}
      className={cn("space-y-4", className)}
      {...props}
    >
      {children}
    </form>
  );
});

Form.displayName = "Form";

export const FormField = ({ 
  children, 
  className,
  ...props 
}) => {
  return (
    <div className={cn("space-y-2", className)} {...props}>
      {children}
    </div>
  );
};

export const FormLabel = forwardRef(({ 
  children, 
  required,
  className,
  ...props 
}, ref) => {
  return (
    <label
      ref={ref}
      className={cn(
        "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
        className
      )}
      {...props}
    >
      {children}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
  );
});

FormLabel.displayName = "FormLabel";

export const FormError = ({ 
  children, 
  className,
  ...props 
}) => {
  if (!children) return null;

  return (
    <p
      className={cn("text-sm text-red-500", className)}
      {...props}
    >
      {children}
    </p>
  );
};

export const FormHelperText = ({ 
  children, 
  className,
  ...props 
}) => {
  if (!children) return null;

  return (
    <p
      className={cn("text-sm text-muted-foreground", className)}
      {...props}
    >
      {children}
    </p>
  );
};

export const FormGroup = ({ 
  children, 
  className,
  ...props 
}) => {
  return (
    <div className={cn("grid gap-4", className)} {...props}>
      {children}
    </div>
  );
};

export const FormRow = ({ 
  children, 
  className,
  columns = 2,
  ...props 
}) => {
  const gridCols = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
  };

  return (
    <div 
      className={cn("grid gap-4", gridCols[columns], className)} 
      {...props}
    >
      {children}
    </div>
  );
};

export const FormActions = ({ 
  children, 
  className,
  align = "right",
  ...props 
}) => {
  const alignments = {
    left: "justify-start",
    center: "justify-center",
    right: "justify-end",
    between: "justify-between"
  };

  return (
    <div 
      className={cn(
        "flex gap-2 pt-4",
        alignments[align],
        className
      )} 
      {...props}
    >
      {children}
    </div>
  );
};

export const FormSection = ({ 
  title,
  description,
  children, 
  className,
  ...props 
}) => {
  return (
    <div className={cn("space-y-4", className)} {...props}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-lg font-medium">{title}</h3>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      )}
      {children}
    </div>
  );
};

export const FormCard = ({ 
  title,
  description,
  children, 
  className,
  ...props 
}) => {
  return (
    <div 
      className={cn(
        "rounded-lg border bg-card text-card-foreground shadow-sm",
        className
      )} 
      {...props}
    >
      {(title || description) && (
        <div className="p-6 pb-0">
          {title && (
            <h3 className="text-lg font-semibold">{title}</h3>
          )}
          {description && (
            <p className="text-sm text-muted-foreground mt-1">{description}</p>
          )}
        </div>
      )}
      <div className="p-6">
        {children}
      </div>
    </div>
  );
};
