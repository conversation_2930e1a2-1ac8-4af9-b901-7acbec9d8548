import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  Activity,
  Building2,
  Calendar,
  Map,
  TrendingUp,
  UserCircle,
  Users,
  Wheat,
} from "lucide-react";

const stats = [
  {
    name: "Clientes",
    value: "12",
    icon: Users,
    change: "+2.5%",
    trend: "up",
    color: "from-blue-500 to-blue-600",
  },
  {
    name: "Fazendas",
    value: "24",
    icon: Building2,
    change: "+5.2%",
    trend: "up",
    color: "from-emerald-500 to-emerald-600",
  },
  {
    name: "<PERSON><PERSON>s Ativas",
    value: "8",
    icon: Wheat,
    change: "+12.3%",
    trend: "up",
    color: "from-amber-500 to-amber-600",
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    value: "148",
    icon: Map,
    change: "+8.1%",
    trend: "up",
    color: "from-teal-500 to-teal-600",
  },
  {
    name: "Funcion<PERSON><PERSON>s",
    value: "32",
    icon: UserCircle,
    change: "+1.2%",
    trend: "up",
    color: "from-purple-500 to-purple-600",
  },
];

const recentActivities = [
  {
    id: 1,
    action: "Nova coleta realizada",
    location: "Fazenda São João - Talhão A1",
    time: "2 horas atrás",
    icon: Activity,
    type: "success",
  },
  {
    id: 2,
    action: "Safra de milho iniciada",
    location: "Fazenda Vista Alegre",
    time: "1 dia atrás",
    icon: Wheat,
    type: "info",
  },
  {
    id: 3,
    action: "Novo cliente cadastrado",
    location: "João Silva - Produtor Rural",
    time: "2 dias atrás",
    icon: Users,
    type: "success",
  },
  {
    id: 4,
    action: "Análise de solo concluída",
    location: "Fazenda Esperança - Talhão B3",
    time: "3 dias atrás",
    icon: Map,
    type: "info",
  },
];

const upcomingTasks = [
  {
    id: 1,
    task: "Coleta de amostras",
    location: "Fazenda Boa Vista",
    date: "Hoje, 14:00",
    priority: "high",
    icon: Calendar,
  },
  {
    id: 2,
    task: "Análise de produtividade",
    location: "Fazenda São Pedro",
    date: "Amanhã, 09:00",
    priority: "medium",
    icon: TrendingUp,
  },
  {
    id: 3,
    task: "Reunião com cliente",
    location: "Escritório Central",
    date: "Sexta, 15:30",
    priority: "low",
    icon: Users,
  },
];

export default function Admin() {
  const currentDate = new Date().toLocaleDateString("pt-BR", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <div className="min-h-screen p-6">
      {/* Header moderno */}
      <div className="mb-8 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Dashboard AgroSyste
              </h1>
              <p className="text-gray-600 mt-2 text-lg">
                {currentDate} • Bem-vindo de volta!
              </p>
            </div>
            <div className="mt-4 lg:mt-0 flex items-center space-x-4">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-4 py-2 rounded-xl flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span className="font-medium">Sistema Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Cards de estatísticas modernos */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-5 mb-8 animate-slideInUp">
        {stats.map((item, index) => {
          const Icon = item.icon;
          return (
            <Card
              key={item.name}
              className="bg-white/80 backdrop-blur-sm border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 animate-fadeInUp"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      {item.name}
                    </p>
                    <p className="text-3xl font-bold text-gray-900 mb-2">
                      {item.value}
                    </p>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="h-4 w-4 text-emerald-500" />
                      <span className="text-sm font-medium text-emerald-600">
                        {item.change}
                      </span>
                    </div>
                  </div>
                  <div
                    className={`p-4 rounded-2xl bg-gradient-to-r ${item.color} shadow-lg`}
                  >
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Seção principal com 3 colunas */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 animate-slideInUp">
        {/* Atividades Recentes */}
        <div className="lg:col-span-2">
          <Card className="bg-white/80 backdrop-blur-sm border border-white/20 shadow-xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl font-bold text-gray-900 flex items-center space-x-2">
                  <Activity className="h-6 w-6 text-emerald-600" />
                  <span>Atividades Recentes</span>
                </CardTitle>
                <button className="text-emerald-600 hover:text-emerald-700 text-sm font-medium">
                  Ver todas
                </button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map((activity) => {
                  const Icon = activity.icon;
                  return (
                    <div
                      key={activity.id}
                      className="flex items-start space-x-4 p-4 rounded-xl bg-gray-50/50 hover:bg-gray-100/50 transition-colors"
                    >
                      <div
                        className={`p-2 rounded-lg ${
                          activity.type === "success"
                            ? "bg-emerald-100 text-emerald-600"
                            : "bg-blue-100 text-blue-600"
                        }`}
                      >
                        <Icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900">
                          {activity.action}
                        </p>
                        <p className="text-sm text-gray-600">
                          {activity.location}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {activity.time}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Próximas Tarefas */}
        <div>
          <Card className="bg-white/80 backdrop-blur-sm border border-white/20 shadow-xl mb-6">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-bold text-gray-900 flex items-center space-x-2">
                <Calendar className="h-6 w-6 text-amber-600" />
                <span>Próximas Tarefas</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {upcomingTasks.map((task) => {
                  const Icon = task.icon;
                  const priorityColors = {
                    high: "border-l-red-500 bg-red-50/50",
                    medium: "border-l-amber-500 bg-amber-50/50",
                    low: "border-l-green-500 bg-green-50/50",
                  };

                  return (
                    <div
                      key={task.id}
                      className={`p-3 rounded-lg border-l-4 ${
                        priorityColors[task.priority]
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <Icon className="h-5 w-5 text-gray-600 mt-0.5" />
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-900 text-sm">
                            {task.task}
                          </p>
                          <p className="text-xs text-gray-600">
                            {task.location}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {task.date}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Card de clima/informações rápidas */}
          <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-xl">
            <CardContent className="p-6">
              <div className="text-center">
                <div className="mb-4">
                  <div className="text-4xl mb-2">☀️</div>
                  <p className="text-2xl font-bold">28°C</p>
                  <p className="text-blue-100">Ensolarado</p>
                </div>
                <div className="text-sm text-blue-100">
                  <p>Condições ideais para coleta</p>
                  <p>Umidade: 65%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
