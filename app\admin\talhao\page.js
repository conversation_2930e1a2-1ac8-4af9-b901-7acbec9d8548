"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { AlertDialogUI } from "@/components/alertDialog";
import { useEffect } from "react";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import { PaginationUI } from "@/components/pagination";
import TalhaoService from "@/lib/services/talhaoService";
import FazendaService from "@/lib/services/fazendaService";
import AccordionWithTables from "@/components/accordion/accordion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Search } from "lucide-react";
import AccordionWithTables from "@/components/accordion/accordion";

export default function Ta<PERSON><PERSON>() {
    const searchParams = useSearchParams();
    const router = useRouter();
    const [fazenda, setFazenda] = useState([]);
    const { toast } = useToast();
    const [showDialog, setShowDialog] = useState(false);
    const [confirmCallback, setConfirmCallback] = useState(null);
    const [totalPage, setTotalPage] = useState(0);
    const [loading, setLoading] = useState(false);
    const currentPage = Number(searchParams.get("page")) || 1;

    // Estados para filtros
    const [filtroFazendaID, setFiltroFazendaID] = useState("");
    const [listaFazendas, setListaFazendas] = useState([]);
    const [fazendaSearch, setFazendaSearch] = useState("");
    const currentPage = Number(searchParams.get("page")) || 1

    const editarTalhao = (id) => {
        router.push(`/admin/talhao/editar/${id}`);
    };

    const deletar = async (id) => {
        setShowDialog(true);
        setConfirmCallback(() => async () => {
            setLoading(true);
            const talhaoService = new TalhaoService();
            const deletar = await talhaoService.DeletarTalhao(id);
            if (!deletar) {
                setLoading(false);
                setShowDialog(false);
                return toast({
                    title: "Erro",
                    description: "Erro ao deletar talhão",
                    variant: "destructive",
                });
            }

            toast({
                title: "Sucesso",
                description: "Talhão deletado com sucesso",
            });
            setShowDialog(false);
            setLoading(false);
            fetchTalhao(searchParams);
        });
    };

    const fetchTalhao = async (params) => {
        setLoading(true);
        const talhaoService = new TalhaoService();
        const queryString = params.toString();
        const talhao = await talhaoService.ListarTalhao(queryString);
            fetchTalhao();
        });
    };

    const fetchTalhao = async (page) => {
        setLoading(true);
        const talhaoService = new TalhaoService();
        const talhao = await talhaoService.ListarTalhao(page);
        if (!talhao) {
            setLoading(false);
            return toast({
                title: "Erro",
                description: "Erro ao buscar talhão",
                variant: "destructive"
            });
        }
        setFazenda(talhao.items);
        setTotalPage(talhao.totalPages);
        setLoading(false);
    };

    // Buscar lista de fazendas para o filtro
    const fetchFazendas = async () => {
        try {
            const fazendaService = new FazendaService();
            const fazendas = await fazendaService.ListarTodasFazendas();
            if (fazendas) {
                setListaFazendas(fazendas);
            }
        } catch (error) {
            toast({
                title: "Erro",
                description: "Erro ao carregar lista de fazendas para filtro",
                variant: "destructive"
            });
        }
    };

    // Função para aplicar filtros
    const aplicarFiltros = () => {
        const params = new URLSearchParams(searchParams);

        // Adicionar filtro de fazenda aos parâmetros
        if (filtroFazendaID) params.set("fazendaID", filtroFazendaID);
        else params.delete("fazendaID");

        // Resetar para página 1 ao filtrar
        params.set("page", 1);

        router.push(`${window.location.pathname}?${params.toString()}`);
    };

    // Função para limpar filtros
    const limparFiltros = () => {
        setFiltroFazendaID("");
        setFazendaSearch("");

        const params = new URLSearchParams();
        params.set("page", 1);
        router.push(`${window.location.pathname}?${params.toString()}`);
    };

    // Filtrar fazendas para o select
    const filterFazendas = fazendaSearch
        ? listaFazendas.filter(fazenda =>
            fazenda.nome.toLowerCase().includes(fazendaSearch.toLowerCase())
        )
        : listaFazendas;

    useEffect(() => {
        fetchFazendas();
    }, []);

    useEffect(() => {
        const params = new URLSearchParams(searchParams);
        params.set("page", currentPage);
        router.push(`${window.location.pathname}?${params.toString()}`);
    }, []);

    useEffect(() => {
        fetchTalhao(searchParams);
    }, [searchParams]);

    return (
        <div className="container max-w-full justify-center items-center mx-auto p-6">
        setTotalPage(talhao.totalPages)
        setLoading(false);
    };

    useEffect(() => {
        const params = new URLSearchParams();
        params.set("page", currentPage);
        router.push(`${window.location.pathname}?${params.toString()}`)
    }, []);

    useEffect(() => {
        fetchTalhao(searchParams)
    }, [currentPage, searchParams]);

    return (
        <div className="container  max-w-full justify-center items-center mx-auto p-6">
            <AlertDialogUI
                title="Confirmação de exclusão"
                description="Deseja realmente deletar esta talhão?"
                showDialog={showDialog}
                setShowDialog={setShowDialog}
                onConfirm={confirmCallback}
            />
            <div className="mb-8 flex justify-between items-center">
                <div>
                    <h1 className="mt-4 text-3xl font-bold">Talhões</h1>
                    <p className="text-muted-foreground">Lista de talhões cadastrados</p>
                    <p className="text-muted-foreground">Lista de talhões cadastradas</p>
                </div>
                <div className="flex flex-row justify-center items-center gap-2">
                    <Link className="flex items-center justify-center" href="/admin/talhao/novo">
                        <Button className="px-4">Novo Talhão</Button>
                    </Link>
                </div>
            </div>

            {/* Seção de filtros */}
            <div className="mb-6 p-4 border rounded-lg bg-gray-50">
                <h2 className="text-lg font-medium mb-3">Filtros</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label htmlFor="filtroFazenda">Fazenda</Label>
                        <Select
                            value={filtroFazendaID}
                            onValueChange={(value) => {
                                setFiltroFazendaID(value);
                                const fazendaSelecionada = listaFazendas.find(f => f.id === value);
                                if (fazendaSelecionada) {
                                    setFazendaSearch(fazendaSelecionada.nome);
                                }
                            }}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Selecione uma fazenda..." />
                            </SelectTrigger>
                            <SelectContent>
                                <Input
                                    type="text"
                                    placeholder="Buscar fazenda..."
                                    value={fazendaSearch}
                                    onChange={(e) => setFazendaSearch(e.target.value)}
                                    className="p-2"
                                />
                                {filterFazendas.length > 0 ? (
                                    filterFazendas.map((fazenda) => (
                                        <SelectItem key={fazenda.id} value={fazenda.id}>
                                            {fazenda.nome}
                                        </SelectItem>
                                    ))
                                ) : (
                                    <p className="p-2 text-gray-500">Nenhuma fazenda encontrada.</p>
                                )}
                            </SelectContent>
                        </Select>
                    </div>
                </div>
                <div className="flex justify-end mt-3 gap-2">
                    <Button variant="outline" onClick={limparFiltros}>
                        Limpar
                    </Button>
                    <Button onClick={aplicarFiltros}>
                        <Search className="w-4 h-4 mr-2" />
                        Filtrar
                    </Button>
                </div>
            </div>

            {loading ? (
                <div className="flex justify-center items-center">
                    <Spinner className="text-black" message="Carregando..." />
                </div>
            ) : (
                <>
                    <AccordionWithTables data={fazenda} editarTalhao={editarTalhao} deletarTalhao={deletar} />
                    <div className="mt-4 flex justify-end items-center">
                        <PaginationUI
                            totalPage={totalPage}
                        />
                    </div>
                </>
            )}
        </div>
    );
}
}
