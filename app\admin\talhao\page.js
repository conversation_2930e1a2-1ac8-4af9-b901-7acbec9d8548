"use client";
import AccordionWithTables from "@/components/accordion/accordion";
import { AlertDialogUI } from "@/components/alertDialog";
import { PaginationUI } from "@/components/pagination";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import FazendaService from "@/lib/services/fazendaService";
import TalhaoService from "@/lib/services/talhaoService";
import { Filter, Grid3X3, Map, Plus, Search, X } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

function TalhaoContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [fazenda, setFazenda] = useState([]);
  const { toast } = useToast();
  const [showDialog, setShowDialog] = useState(false);
  const [confirmCallback, setConfirmCallback] = useState(null);
  const [totalPage, setTotalPage] = useState(0);
  const [loading, setLoading] = useState(false);

  // Estados para filtros e visualização
  const [filtroFazendaID, setFiltroFazendaID] = useState("");
  const [listaFazendas, setListaFazendas] = useState([]);
  const [fazendaSearch, setFazendaSearch] = useState("");
  const [viewMode, setViewMode] = useState("accordion"); // "accordion" ou "cards"

  const editarTalhao = (id) => {
    router.push(`/admin/talhao/editar/${id}`);
  };

  const deletar = async (id) => {
    setShowDialog(true);
    setConfirmCallback(() => async () => {
      setLoading(true);
      const talhaoService = new TalhaoService();
      const deletar = await talhaoService.DeletarTalhao(id);
      if (!deletar) {
        setLoading(false);
        setShowDialog(false);
        return toast({
          title: "Erro",
          description: "Erro ao deletar talhão",
          variant: "destructive",
        });
      }

      toast({
        title: "Sucesso",
        description: "Talhão deletado com sucesso",
      });
      setShowDialog(false);
      setLoading(false);
      fetchTalhao(searchParams);
    });
  };

  const fetchTalhao = async (params) => {
    setLoading(true);
    const talhaoService = new TalhaoService();
    const queryString = params.toString();
    const talhao = await talhaoService.ListarTalhao(queryString);
    if (!talhao) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao buscar talhão",
        variant: "destructive",
      });
    }
    setFazenda(talhao.items);
    setTotalPage(talhao.totalPages);
    setLoading(false);
  };

  // Buscar lista de fazendas para o filtro
  const fetchFazendas = async () => {
    try {
      const fazendaService = new FazendaService();
      const fazendas = await fazendaService.ListarTodasFazendas();
      if (fazendas) {
        setListaFazendas(fazendas);
      }
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao carregar lista de fazendas para filtro",
        variant: "destructive",
      });
    }
  };

  // Função para aplicar filtros
  const aplicarFiltros = () => {
    const params = new URLSearchParams(searchParams);

    // Adicionar filtro de fazenda aos parâmetros
    if (filtroFazendaID) params.set("fazendaID", filtroFazendaID);
    else params.delete("fazendaID");

    // Resetar para página 1 ao filtrar
    params.set("page", 1);

    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  // Função para limpar filtros
  const limparFiltros = () => {
    setFiltroFazendaID("");
    setFazendaSearch("");

    const params = new URLSearchParams();
    params.set("page", 1);
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  // Filtrar fazendas para o select
  const filterFazendas = fazendaSearch
    ? listaFazendas.filter((fazenda) =>
        fazenda.nome.toLowerCase().includes(fazendaSearch.toLowerCase())
      )
    : listaFazendas;

  useEffect(() => {
    fetchFazendas();
  }, []);

  useEffect(() => {
    fetchFazendas();
  }, []);

  useEffect(() => {
    fetchTalhao(searchParams);
  }, [searchParams]);

  const currentDate = new Date().toLocaleDateString("pt-BR", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <div className="min-h-screen p-4">
      <AlertDialogUI
        title="Confirmação de exclusão"
        description="Deseja realmente deletar este talhão?"
        showDialog={showDialog}
        setShowDialog={setShowDialog}
        onConfirm={confirmCallback}
      />

      {/* Header moderno */}
      <div className="mb-4 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Gestão de Talhões
              </h1>
              <p className="text-gray-600 text-sm">
                {currentDate} • Gerencie seus talhões cadastrados
              </p>
            </div>
            <div className="mt-2 lg:mt-0 flex items-center space-x-4">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-lg flex items-center space-x-2 text-sm">
                <Map className="h-4 w-4" />
                <span>{fazenda.length} Talhões</span>
              </div>
              <Link href="/admin/talhao/novo">
                <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center space-x-2">
                  <Plus className="h-4 w-4" />
                  <span>Novo Talhão</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Seção de filtros moderna */}
      <div className="mb-4 animate-slideInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <div className="p-1 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg">
                <Filter className="h-5 w-5 text-white" />
              </div>
              <div>
                <h2 className="text-base font-bold text-gray-900">
                  Filtros de Busca
                </h2>
                <p className="text-gray-600 text-xs">
                  Refine sua pesquisa de talhões
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="group">
              <Label
                htmlFor="filtroFazenda"
                className="text-sm font-medium text-gray-700 mb-1 block"
              >
                Fazenda
              </Label>
              <Select
                value={filtroFazendaID}
                onValueChange={(value) => {
                  setFiltroFazendaID(value);
                  const fazendaSelecionada = listaFazendas.find(
                    (f) => f.id === value
                  );
                  if (fazendaSelecionada) {
                    setFazendaSearch(fazendaSelecionada.nome);
                  }
                }}
              >
                <SelectTrigger className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9">
                  <SelectValue placeholder="Selecione uma fazenda..." />
                </SelectTrigger>
                <SelectContent>
                  <Input
                    type="text"
                    placeholder="Buscar fazenda..."
                    value={fazendaSearch}
                    onChange={(e) => setFazendaSearch(e.target.value)}
                    className="p-2"
                  />
                  {filterFazendas.length > 0 ? (
                    filterFazendas.map((fazenda) => (
                      <SelectItem key={fazenda.id} value={fazenda.id}>
                        {fazenda.nome}
                      </SelectItem>
                    ))
                  ) : (
                    <p className="p-2 text-gray-500">
                      Nenhuma fazenda encontrada.
                    </p>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-2">
            <Button
              variant="outline"
              onClick={limparFiltros}
              className="border-gray-300 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2 h-9"
            >
              <X className="w-4 h-4" />
              <span>Limpar Filtros</span>
            </Button>
            <Button
              onClick={aplicarFiltros}
              className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white transition-all duration-200 transform hover:scale-105 flex items-center space-x-2 h-9"
            >
              <Search className="w-4 h-4" />
              <span>Aplicar Filtros</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="animate-fadeInUp">
        {loading ? (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
            <div className="flex flex-col justify-center items-center space-y-3">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                <Spinner className="text-white" size="medium" />
              </div>
              <div className="text-center">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  Carregando talhões...
                </h3>
                <p className="text-gray-600 text-sm">
                  Aguarde enquanto buscamos os dados
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                    <Map className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">
                      Lista de Talhões
                    </h3>
                    <p className="text-gray-600 text-xs">
                      {fazenda.length} talhão{fazenda.length !== 1 ? "ões" : ""}{" "}
                      encontrado{fazenda.length !== 1 ? "s" : ""}
                    </p>
                  </div>
                </div>

                {/* Botões de alternância de visualização */}
                <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
                  <Button
                    size="sm"
                    variant={viewMode === "accordion" ? "default" : "ghost"}
                    onClick={() => setViewMode("accordion")}
                    className={`transition-all duration-200 h-8 ${
                      viewMode === "accordion"
                        ? "bg-gradient-to-r from-emerald-500 to-teal-500 text-white shadow-lg"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <Grid3X3 className="h-3 w-3 mr-1" />
                    Accordion
                  </Button>
                  <Button
                    size="sm"
                    variant={viewMode === "cards" ? "default" : "ghost"}
                    onClick={() => setViewMode("cards")}
                    className={`transition-all duration-200 h-8 ${
                      viewMode === "cards"
                        ? "bg-white shadow-sm text-gray-900"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <Map className="h-3 w-3 mr-1" />
                    Cards
                  </Button>
                </div>
              </div>
            </div>

            {fazenda.length === 0 ? (
              <div className="p-8 text-center">
                <div className="flex flex-col items-center space-y-3">
                  <div className="p-3 bg-gray-100 rounded-full">
                    <Map className="h-8 w-8 text-gray-400" />
                  </div>
                  <div>
                    <h3 className="text-base font-medium text-gray-900 mb-1">
                      Nenhum talhão encontrado
                    </h3>
                    <p className="text-gray-600 text-sm">
                      Não há talhões cadastrados ou que correspondam aos filtros
                      aplicados.
                    </p>
                  </div>
                  <Link href="/admin/talhao/novo">
                    <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white">
                      <Plus className="h-4 w-4 mr-2" />
                      Cadastrar Primeiro Talhão
                    </Button>
                  </Link>
                </div>
              </div>
            ) : (
              <div className="p-4">
                <AccordionWithTables
                  data={fazenda}
                  editarTalhao={editarTalhao}
                  deletarTalhao={deletar}
                />
              </div>
            )}

            {totalPage > 1 && (
              <div className="p-4 border-t border-gray-200 bg-gray-50/50">
                <div className="flex justify-end items-center">
                  <PaginationUI totalPage={totalPage} />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default function Talhao() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen p-4">
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
            <div className="flex flex-col justify-center items-center space-y-3">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                <Spinner className="text-white" size="medium" />
              </div>
              <div className="text-center">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  Carregando talhões...
                </h3>
                <p className="text-gray-600 text-sm">
                  Aguarde enquanto buscamos os dados
                </p>
              </div>
            </div>
          </div>
        </div>
      }
    >
      <TalhaoContent />
    </Suspense>
  );
}
