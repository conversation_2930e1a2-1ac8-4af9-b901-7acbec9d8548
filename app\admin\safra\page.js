"use client";
import { AlertDialogUI } from "@/components/alertDialog";
import { PaginationUI } from "@/components/pagination";
import Tables from "@/components/tables/Tables";
import { Button } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import ClienteService from "@/lib/services/clienteService";
import FazendaService from "@/lib/services/fazendaService";
import SafraService from "@/lib/services/safraService";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Activity,
  Calendar,
  Filter,
  Grid3X3,
  List,
  Pencil,
  Plus,
  Search,
  Trash2,
  Users,
  X,
} from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

function SafraContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [fazenda, setFazenda] = useState([]);
  const { toast } = useToast();
  const [showDialog, setShowDialog] = useState(false);
  const [confirmCallback, setConfirmCallback] = useState(null);
  const [totalPage, setTotalPage] = useState(0);
  const [loading, setLoading] = useState(false);

  // Estados para filtros e visualização
  const [filtroFazendaID, setFiltroFazendaID] = useState("");
  const [filtroClienteID, setFiltroClienteID] = useState("");
  const [filtroDataInicio, setFiltroDataInicio] = useState(null);
  const [filtroDataFim, setFiltroDataFim] = useState(null);
  const [viewMode, setViewMode] = useState("cards"); // "table" ou "cards"

  // Estados para as listas de seleção
  const [listaFazendas, setListaFazendas] = useState([]);
  const [listaClientes, setListaClientes] = useState([]);

  // Estados para busca nos selects
  const [fazendaSearch, setFazendaSearch] = useState("");
  const [clienteSearch, setClienteSearch] = useState("");

  // Colunas da tabela
  const columns = [
    {
      headerName: "Fazenda",
      field: "fazenda",
      renderCell: (params) => params.row.fazenda.nome,
    },
    {
      headerName: "Cliente",
      field: "cliente",
      renderCell: (params) => params.row.cliente.nome,
    },
    {
      headerName: "Data inicio",
      field: "dataInicio",
      renderCell: (params) =>
        new Date(params.row.dataInicio).toLocaleDateString(),
    },
    {
      headerName: "Data termino",
      field: "dataFim",
      renderCell: (params) =>
        params.row.dataFim
          ? new Date(params.row.dataFim).toLocaleDateString()
          : "Não Informado",
    },
    {
      headerName: "Ações",
      field: "acoes",
      renderCell: (params) => (
        <div className="flex justify-center gap-2">
          <Button
            size="sm"
            onClick={() => editarFazenda(params.row.id)}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white transition-all duration-200 transform hover:scale-105 border-0"
          >
            <Pencil className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            onClick={() => deletarFazenda(params.row.id)}
            className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white transition-all duration-200 transform hover:scale-105 border-0"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ];

  const editarFazenda = (id) => {
    router.push(`/admin/safra/editar/${id}`);
  };

  const deletarFazenda = async (id) => {
    setShowDialog(true);
    setConfirmCallback(() => async () => {
      setLoading(true);
      const safraService = new SafraService();
      const deletar = await safraService.DeletarSafra(id);
      if (!deletar) {
        setLoading(false);
        setShowDialog(false);
        return toast({
          title: "Erro",
          description: "Erro ao deletar safra",
          variant: "destructive",
        });
      }

      toast({
        title: "Sucesso",
        description: "Safra deletado com sucesso",
      });
      setShowDialog(false);
      setLoading(false);
      fetchSafra(searchParams);
    });
  };

  // Função para aplicar filtros
  const aplicarFiltros = () => {
    const params = new URLSearchParams(searchParams);

    // Adicionar filtros aos parâmetros
    if (filtroFazendaID) params.set("fazendaID", filtroFazendaID);
    else params.delete("fazendaID");

    if (filtroClienteID) params.set("clienteID", filtroClienteID);
    else params.delete("clienteID");

    if (filtroDataInicio) {
      const dataFormatada = new Date(filtroDataInicio)
        .toISOString()
        .split("T")[0];
      params.set("dataInicio", dataFormatada);
    } else {
      params.delete("dataInicio");
    }

    if (filtroDataFim) {
      const dataFormatada = new Date(filtroDataFim).toISOString().split("T")[0];
      params.set("dataFim", dataFormatada);
    } else {
      params.delete("dataFim");
    }

    // Resetar para página 1 ao filtrar
    params.set("page", 1);

    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  // Função para limpar filtros
  const limparFiltros = () => {
    setFiltroFazendaID("");
    setFiltroClienteID("");
    setFiltroDataInicio(null);
    setFiltroDataFim(null);
    setFazendaSearch("");
    setClienteSearch("");

    const params = new URLSearchParams();
    params.set("page", 1);
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  const fetchSafra = async (params) => {
    setLoading(true);
    const safraService = new SafraService();

    // Converter o objeto URLSearchParams para string de consulta
    const queryString = params.toString();

    const safras = await safraService.ListarSafra(queryString);
    if (!safras) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao buscar safras",
        variant: "destructive",
      });
    }
    setFazenda(safras.items);
    setTotalPage(safras.totalPages);
    setLoading(false);
  };

  // Buscar listas de fazendas e clientes para os filtros
  const fetchListas = async () => {
    try {
      // Buscar fazendas
      const fazendaService = new FazendaService();
      const fazendas = await fazendaService.ListarTodasFazendas();
      if (fazendas) {
        setListaFazendas(fazendas);
      }

      // Buscar clientes
      const clienteService = new ClienteService();
      const clientes = await clienteService.ListarTodosClientes();
      if (clientes) {
        setListaClientes(clientes);
      }
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao carregar listas para filtros",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchListas();
  }, []);

  useEffect(() => {
    fetchSafra(searchParams);
  }, [searchParams]);

  // Filtrar fazendas e clientes para os selects
  const filterFazendas = fazendaSearch
    ? listaFazendas.filter((fazenda) =>
        fazenda.nome.toLowerCase().includes(fazendaSearch.toLowerCase())
      )
    : listaFazendas;

  const filterClientes = clienteSearch
    ? listaClientes.filter((cliente) =>
        cliente.nome.toLowerCase().includes(clienteSearch.toLowerCase())
      )
    : listaClientes;

  const currentDate = new Date().toLocaleDateString("pt-BR", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <div className="min-h-screen p-4">
      <AlertDialogUI
        title="Confirmação de exclusão"
        description="Deseja realmente deletar esta safra?"
        showDialog={showDialog}
        setShowDialog={setShowDialog}
        onConfirm={confirmCallback}
      />

      {/* Header moderno */}
      <div className="mb-4 animate-fadeInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Gestão de Safras
              </h1>
              <p className="text-gray-600 text-sm">
                {currentDate} • Gerencie suas safras cadastradas
              </p>
            </div>
            <div className="mt-2 lg:mt-0 flex items-center space-x-4">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-lg flex items-center space-x-2 text-sm">
                <Activity className="h-4 w-4" />
                <span>{fazenda.length} Safras</span>
              </div>
              <Link href="/admin/safra/novo">
                <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center space-x-2">
                  <Plus className="h-4 w-4" />
                  <span>Nova Safra</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Seção de filtros moderna */}
      <div className="mb-4 animate-slideInUp">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <div className="p-1 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg">
                <Filter className="h-5 w-5 text-white" />
              </div>
              <div>
                <h2 className="text-base font-bold text-gray-900">
                  Filtros Avançados
                </h2>
                <p className="text-gray-600 text-xs">
                  Refine sua pesquisa de safras
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="group">
              <Label
                htmlFor="filtroFazenda"
                className="text-sm font-medium text-gray-700 mb-1 block"
              >
                Fazenda
              </Label>
              <Select
                value={filtroFazendaID}
                onValueChange={(value) => {
                  setFiltroFazendaID(value);
                  const fazendaSelecionada = listaFazendas.find(
                    (f) => f.id === value
                  );
                  if (fazendaSelecionada) {
                    setFazendaSearch(fazendaSelecionada.nome);
                  }
                }}
              >
                <SelectTrigger className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9">
                  <SelectValue placeholder="Selecione uma fazenda..." />
                </SelectTrigger>
                <SelectContent>
                  <Input
                    type="text"
                    placeholder="Buscar fazenda..."
                    value={fazendaSearch}
                    onChange={(e) => setFazendaSearch(e.target.value)}
                    className="p-2"
                  />
                  {filterFazendas.length > 0 ? (
                    filterFazendas.map((fazenda) => (
                      <SelectItem key={fazenda.id} value={fazenda.id}>
                        {fazenda.nome}
                      </SelectItem>
                    ))
                  ) : (
                    <p className="p-2 text-gray-500">
                      Nenhuma fazenda encontrada.
                    </p>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="group">
              <Label
                htmlFor="filtroCliente"
                className="text-sm font-medium text-gray-700 mb-1 block"
              >
                Cliente
              </Label>
              <Select
                value={filtroClienteID}
                onValueChange={(value) => {
                  setFiltroClienteID(value);
                  const clienteSelecionado = listaClientes.find(
                    (c) => c.id === value
                  );
                  if (clienteSelecionado) {
                    setClienteSearch(clienteSelecionado.nome);
                  }
                }}
              >
                <SelectTrigger className="bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9">
                  <SelectValue placeholder="Selecione um cliente..." />
                </SelectTrigger>
                <SelectContent>
                  <Input
                    type="text"
                    placeholder="Buscar cliente..."
                    value={clienteSearch}
                    onChange={(e) => setClienteSearch(e.target.value)}
                    className="p-2"
                  />
                  {filterClientes.length > 0 ? (
                    filterClientes.map((cliente) => (
                      <SelectItem key={cliente.id} value={cliente.id}>
                        {cliente.nome}
                      </SelectItem>
                    ))
                  ) : (
                    <p className="p-2 text-gray-500">
                      Nenhum cliente encontrado.
                    </p>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="group">
              <Label
                htmlFor="filtroDataInicio"
                className="text-sm font-medium text-gray-700 mb-1 block"
              >
                Data Início
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className="w-full justify-start text-left font-normal bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    {filtroDataInicio ? (
                      format(filtroDataInicio, "PPP", { locale: ptBR })
                    ) : (
                      <span>Selecione uma data</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={filtroDataInicio}
                    onSelect={setFiltroDataInicio}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="group">
              <Label
                htmlFor="filtroDataFim"
                className="text-sm font-medium text-gray-700 mb-1 block"
              >
                Data Término
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className="w-full justify-start text-left font-normal bg-white/50 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 h-9"
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    {filtroDataFim ? (
                      format(filtroDataFim, "PPP", { locale: ptBR })
                    ) : (
                      <span>Selecione uma data</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={filtroDataFim}
                    onSelect={setFiltroDataFim}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-2">
            <Button
              variant="outline"
              onClick={limparFiltros}
              className="border-gray-300 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2 h-9"
            >
              <X className="w-4 h-4" />
              <span>Limpar Filtros</span>
            </Button>
            <Button
              onClick={aplicarFiltros}
              className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white transition-all duration-200 transform hover:scale-105 flex items-center space-x-2 h-9"
            >
              <Search className="w-4 h-4" />
              <span>Aplicar Filtros</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="animate-fadeInUp">
        {loading ? (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
            <div className="flex flex-col justify-center items-center space-y-3">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                <Spinner className="text-white" size="medium" />
              </div>
              <div className="text-center">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  Carregando safras...
                </h3>
                <p className="text-gray-600 text-sm">
                  Aguarde enquanto buscamos os dados
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                    <Activity className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">
                      Lista de Safras
                    </h3>
                    <p className="text-gray-600 text-xs">
                      {fazenda.length} safra{fazenda.length !== 1 ? "s" : ""}{" "}
                      encontrada{fazenda.length !== 1 ? "s" : ""}
                    </p>
                  </div>
                </div>

                {/* Botões de alternância de visualização */}
                <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
                  <Button
                    size="sm"
                    variant={viewMode === "cards" ? "default" : "ghost"}
                    onClick={() => setViewMode("cards")}
                    className={`transition-all duration-200 h-8 ${
                      viewMode === "cards"
                        ? "bg-gradient-to-r from-emerald-500 to-teal-500 text-white shadow-lg"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <Grid3X3 className="h-3 w-3 mr-1" />
                    Cards
                  </Button>
                  <Button
                    size="sm"
                    variant={viewMode === "table" ? "default" : "ghost"}
                    onClick={() => setViewMode("table")}
                    className={`transition-all duration-200 h-8 ${
                      viewMode === "table"
                        ? "bg-white shadow-sm text-gray-900"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <List className="h-3 w-3 mr-1" />
                    Tabela
                  </Button>
                </div>
              </div>
            </div>

            {fazenda.length === 0 ? (
              <div className="p-8 text-center">
                <div className="flex flex-col items-center space-y-3">
                  <div className="p-3 bg-gray-100 rounded-full">
                    <Activity className="h-8 w-8 text-gray-400" />
                  </div>
                  <div>
                    <h3 className="text-base font-medium text-gray-900 mb-1">
                      Nenhuma safra encontrada
                    </h3>
                    <p className="text-gray-600 text-sm">
                      Não há safras cadastradas ou que correspondam aos filtros
                      aplicados.
                    </p>
                  </div>
                  <Link href="/admin/safra/novo">
                    <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white">
                      <Plus className="h-4 w-4 mr-2" />
                      Cadastrar Primeira Safra
                    </Button>
                  </Link>
                </div>
              </div>
            ) : viewMode === "cards" ? (
              <div className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {fazenda.map((item, index) => (
                    <div
                      key={item.id}
                      className="bg-white/80 backdrop-blur-sm rounded-lg shadow-md border border-white/20 p-4 hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] animate-fadeInUp"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <div className="p-1.5 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                            <Activity className="h-4 w-4 text-white" />
                          </div>
                          <div>
                            <h3 className="text-base font-bold text-gray-900">
                              {item.fazenda.nome}
                            </h3>
                            <p className="text-xs text-gray-600">
                              Safra #{item.id.slice(0, 8)}
                            </p>
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <Button
                            size="sm"
                            onClick={() => editarFazenda(item.id)}
                            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white transition-all duration-200 transform hover:scale-105 border-0 h-7 w-7 p-0"
                          >
                            <Pencil className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => deletarFazenda(item.id)}
                            className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white transition-all duration-200 transform hover:scale-105 border-0 h-7 w-7 p-0"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 text-gray-600">
                          <Users className="h-3 w-3" />
                          <span className="text-sm">{item.cliente.nome}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-gray-600">
                          <Calendar className="h-3 w-3" />
                          <span className="text-sm">
                            {new Date(item.dataInicio).toLocaleDateString()} -{" "}
                            {item.dataFim
                              ? new Date(item.dataFim).toLocaleDateString()
                              : "Em andamento"}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Tables data={fazenda} columns={columns} />
              </div>
            )}

            {totalPage > 1 && (
              <div className="p-4 border-t border-gray-200 bg-gray-50/50">
                <div className="flex justify-end items-center">
                  <PaginationUI totalPage={totalPage} />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default function Safra() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen p-4">
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
            <div className="flex flex-col justify-center items-center space-y-3">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full">
                <Spinner className="text-white" size="medium" />
              </div>
              <div className="text-center">
                <h3 className="text-base font-medium text-gray-900 mb-1">
                  Carregando safras...
                </h3>
                <p className="text-gray-600 text-sm">
                  Aguarde enquanto buscamos os dados
                </p>
              </div>
            </div>
          </div>
        </div>
      }
    >
      <SafraContent />
    </Suspense>
  );
}
