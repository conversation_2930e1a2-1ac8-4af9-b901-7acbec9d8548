"use client";
import Tables from "@/components/tables/Tables";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Pencil, Trash2, Search, Calendar } from "lucide-react";
import { Pencil, Trash2 } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { AlertDialogUI } from "@/components/alertDialog";
import { useEffect } from "react";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";
import { PaginationUI } from "@/components/pagination";
import FazendaService from "@/lib/services/fazendaService";
import SafraService from "@/lib/services/safraService";
import ClienteService from "@/lib/services/clienteService";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Calendario } from "@/components/calendario";

export default function Safra() {
    const searchParams = useSearchParams();
    const router = useRouter();
    const [fazenda, setFazenda] = useState([]);
    const { toast } = useToast();
    const [showDialog, setShowDialog] = useState(false);
    const [confirmCallback, setConfirmCallback] = useState(null);
    const [totalPage, setTotalPage] = useState(0);
    const [loading, setLoading] = useState(false);
    const currentPage = Number(searchParams.get("page")) || 1;

    // Estados para os filtros
    const [filtroFazendaID, setFiltroFazendaID] = useState("");
    const [filtroClienteID, setFiltroClienteID] = useState("");
    const [filtroDataInicio, setFiltroDataInicio] = useState(null);
    const [filtroDataFim, setFiltroDataFim] = useState(null);

    // Estados para as listas de seleção
    const [listaFazendas, setListaFazendas] = useState([]);
    const [listaClientes, setListaClientes] = useState([]);

    // Estados para busca nos selects
    const [fazendaSearch, setFazendaSearch] = useState("");
    const [clienteSearch, setClienteSearch] = useState("");

    // Colunas da tabela
    const columns = [
        { headerName: "Fazenda", field: "fazenda", renderCell: (params) => params.row.fazenda.nome },
        { headerName: "Cliente", field: "cliente", renderCell: (params) => params.row.cliente.nome },
    const currentPage = Number(searchParams.get("page")) || 1

    const columns = [
        {headerName: "Fazenda", field: "fazenda", renderCell: (params) => params.row.fazenda.nome},
        {headerName: "Cliente", field: "cliente", renderCell: (params) => params.row.cliente.nome},
        { headerName: "Data inicio", field: "dataInicio", renderCell: (params) => new Date(params.row.dataInicio).toLocaleDateString() },
        { headerName: "Data termino", field: "dataFim", renderCell: (params) => params.row.dataFim ? new Date(params.row.dataFim).toLocaleDateString() : "Não Informado" },
        {
            headerName: "Ações",
            field: "acoes",
            renderCell: (params) => (
                <div className="flex justify-center gap-3">
                    <Button size="sm" onClick={() => editarFazenda(params.row.id)}>
                        <Pencil className="w-4 h-4" />
                    </Button>
                    <Button size="sm" onClick={() => deletarFazenda(params.row.id)}>
                        <Trash2 className="w-4 h-4" />
                    </Button>
                </div>
            ),
        },
    ];

    const editarFazenda = (id) => {
        router.push(`/admin/safra/editar/${id}`);
    };

    const deletarFazenda = async (id) => {
        setShowDialog(true);
        setConfirmCallback(() => async () => {
            setLoading(true);
            const safraService = new SafraService();
            const deletar = await safraService.DeletarSafra(id);
            if (!deletar) {
                setLoading(false);
                setShowDialog(false);
                return toast({
                    title: "Erro",
                    description: "Erro ao deletar safra",
                    variant: "destructive",
                });
            }

            toast({
                title: "Sucesso",
                description: "Safra deletado com sucesso",
            });
            setShowDialog(false);
            setLoading(false);
            fetchSafra(searchParams);
        });
    };

    // Função para aplicar filtros
    const aplicarFiltros = () => {
        const params = new URLSearchParams(searchParams);

        // Adicionar filtros aos parâmetros
        if (filtroFazendaID) params.set("fazendaID", filtroFazendaID);
        else params.delete("fazendaID");

        if (filtroClienteID) params.set("clienteID", filtroClienteID);
        else params.delete("clienteID");

        if (filtroDataInicio) {
            const dataFormatada = new Date(filtroDataInicio).toISOString().split('T')[0];
            params.set("dataInicio", dataFormatada);
        } else {
            params.delete("dataInicio");
        }

        if (filtroDataFim) {
            const dataFormatada = new Date(filtroDataFim).toISOString().split('T')[0];
            params.set("dataFim", dataFormatada);
        } else {
            params.delete("dataFim");
        }

        // Resetar para página 1 ao filtrar
        params.set("page", 1);

        router.push(`${window.location.pathname}?${params.toString()}`);
    };

    // Função para limpar filtros
    const limparFiltros = () => {
        setFiltroFazendaID("");
        setFiltroClienteID("");
        setFiltroDataInicio(null);
        setFiltroDataFim(null);
        setFazendaSearch("");
        setClienteSearch("");

        const params = new URLSearchParams();
        params.set("page", 1);
        router.push(`${window.location.pathname}?${params.toString()}`);
    };

    const fetchSafra = async (params) => {
        setLoading(true);
        const safraService = new SafraService();

        // Converter o objeto URLSearchParams para string de consulta
        const queryString = params.toString();

        const safras = await safraService.ListarSafra(queryString);
            fetchSafra();
        });
    };


    const fetchSafra = async (page) => {
        setLoading(true);
        const safraService = new SafraService();
        const safras = await safraService.ListarSafra(page);
        if (!safras) {
            setLoading(false);
            return toast({
                title: "Erro",
                description: "Erro ao buscar safras",
                variant: "destructive"
            });
        }
        setFazenda(safras.items);
        setTotalPage(safras.totalPages);
        setLoading(false);
    };

    // Buscar listas de fazendas e clientes para os filtros
    const fetchListas = async () => {
        try {
            // Buscar fazendas
            const fazendaService = new FazendaService();
            const fazendas = await fazendaService.ListarTodasFazendas();
            if (fazendas) {
                setListaFazendas(fazendas);
            }

            // Buscar clientes
            const clienteService = new ClienteService();
            const clientes = await clienteService.ListarTodosClientes();
            if (clientes) {
                setListaClientes(clientes);
            }
        } catch (error) {
            toast({
                title: "Erro",
                description: "Erro ao carregar listas para filtros",
                variant: "destructive"
            });
        }
    };

    useEffect(() => {
        fetchListas();
    }, []);

    useEffect(() => {
        fetchSafra(searchParams);
    }, [searchParams]);

    // Filtrar fazendas e clientes para os selects
    const filterFazendas = fazendaSearch
        ? listaFazendas.filter(fazenda =>
            fazenda.nome.toLowerCase().includes(fazendaSearch.toLowerCase())
        )
        : listaFazendas;

    const filterClientes = clienteSearch
        ? listaClientes.filter(cliente =>
            cliente.nome.toLowerCase().includes(clienteSearch.toLowerCase())
        )
        : listaClientes;

    return (
        <div className="container max-w-full justify-center items-center mx-auto p-6">
        setTotalPage(safras.totalPages)
        setLoading(false);
    };

    useEffect(() => {
        const params = new URLSearchParams();
        params.set("page", currentPage);
        router.push(`${window.location.pathname}?${params.toString()}`)
    }, []);

    useEffect(() => {
        fetchSafra(searchParams)
    }, [currentPage, searchParams]);


    return (
        <div className="container  max-w-full justify-center items-center mx-auto p-6">
            <AlertDialogUI
                title="Confirmação de exclusão"
                description="Deseja realmente deletar esta safra?"
                showDialog={showDialog}
                setShowDialog={setShowDialog}
                onConfirm={confirmCallback}
            />
            <div className="mb-8 flex justify-between items-center">
                <div>
                    <h1 className="mt-4 text-3xl font-bold">Safras</h1>
                    <p className="text-muted-foreground">Lista de safras cadastradas</p>
                </div>
                <div className="flex flex-row justify-center items-center gap-2">
                    <Link className="flex items-center justify-center" href="/admin/safra/novo">
                        <Button className="px-4">Nova Safra</Button>
                    </Link>
                </div>
            </div>

            {/* Seção de filtros */}
            <div className="mb-6 p-4 border rounded-lg bg-gray-50">
                <h2 className="text-lg font-medium mb-3">Filtros</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <Label htmlFor="filtroFazenda">Fazenda</Label>
                        <Select
                            value={filtroFazendaID}
                            onValueChange={(value) => {
                                setFiltroFazendaID(value);
                                const fazendaSelecionada = listaFazendas.find(f => f.id === value);
                                if (fazendaSelecionada) {
                                    setFazendaSearch(fazendaSelecionada.nome);
                                }
                            }}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Selecione uma fazenda..." />
                            </SelectTrigger>
                            <SelectContent>
                                <Input
                                    type="text"
                                    placeholder="Buscar fazenda..."
                                    value={fazendaSearch}
                                    onChange={(e) => setFazendaSearch(e.target.value)}
                                    className="p-2"
                                />
                                {filterFazendas.length > 0 ? (
                                    filterFazendas.map((fazenda) => (
                                        <SelectItem key={fazenda.id} value={fazenda.id}>
                                            {fazenda.nome}
                                        </SelectItem>
                                    ))
                                ) : (
                                    <p className="p-2 text-gray-500">Nenhuma fazenda encontrada.</p>
                                )}
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Label htmlFor="filtroCliente">Cliente</Label>
                        <Select
                            value={filtroClienteID}
                            onValueChange={(value) => {
                                setFiltroClienteID(value);
                                const clienteSelecionado = listaClientes.find(c => c.id === value);
                                if (clienteSelecionado) {
                                    setClienteSearch(clienteSelecionado.nome);
                                }
                            }}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Selecione um cliente..." />
                            </SelectTrigger>
                            <SelectContent>
                                <Input
                                    type="text"
                                    placeholder="Buscar cliente..."
                                    value={clienteSearch}
                                    onChange={(e) => setClienteSearch(e.target.value)}
                                    className="p-2"
                                />
                                {filterClientes.length > 0 ? (
                                    filterClientes.map((cliente) => (
                                        <SelectItem key={cliente.id} value={cliente.id}>
                                            {cliente.nome}
                                        </SelectItem>
                                    ))
                                ) : (
                                    <p className="p-2 text-gray-500">Nenhum cliente encontrado.</p>
                                )}
                            </SelectContent>
                        </Select>
                    </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <Label htmlFor="filtroDataInicio">Data Início</Label>
                        <Popover>
                            <PopoverTrigger asChild>
                                <Button
                                    variant={"outline"}
                                    className="w-full justify-start text-left font-normal"
                                >
                                    <Calendar className="mr-2 h-4 w-4" />
                                    {filtroDataInicio ? (
                                        format(filtroDataInicio, "PPP", { locale: ptBR })
                                    ) : (
                                        <span>Selecione uma data</span>
                                    )}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                                <CalendarComponent
                                    mode="single"
                                    selected={filtroDataInicio}
                                    onSelect={setFiltroDataInicio}
                                    initialFocus
                                />
                            </PopoverContent>
                        </Popover>
                    </div>
                    <div>
                        <Label htmlFor="filtroDataFim">Data Término</Label>
                        <Popover>
                            <PopoverTrigger asChild>
                                <Button
                                    variant={"outline"}
                                    className="w-full justify-start text-left font-normal"
                                >
                                    <Calendar className="mr-2 h-4 w-4" />
                                    {filtroDataFim ? (
                                        format(filtroDataFim, "PPP", { locale: ptBR })
                                    ) : (
                                        <span>Selecione uma data</span>
                                    )}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                                <CalendarComponent
                                    mode="single"
                                    selected={filtroDataFim}
                                    onSelect={setFiltroDataFim}
                                    initialFocus
                                />
                            </PopoverContent>
                        </Popover>
                    </div>
                </div>
                <div className="flex justify-end mt-3 gap-2">
                    <Button variant="outline" onClick={limparFiltros}>
                        Limpar
                    </Button>
                    <Button onClick={aplicarFiltros}>
                        <Search className="w-4 h-4 mr-2" />
                        Filtrar
                    </Button>
                </div>
            </div>

            {loading ? (
                <div className="flex justify-center items-center">
                    <Spinner className="text-black" message="Carregando..." />
                </div>
            ) : (
                <>
                    <Tables data={fazenda} columns={columns} />
                    <div className="mt-4 flex justify-end items-center">
                        <PaginationUI
                            totalPage={totalPage}
                        />
                    </div>
                </>
            )}
        </div>
    );
}
}
