"use client";
import { Controller } from "react-hook-form";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import ColorPicker from "@/components/pickerColor/picker";

import { useEffect } from "react";

export default function ColorConfigForm({ disabled = false, initialValues, control, errors, setValue }) {
  useEffect(() => {
    if(initialValues && Array.isArray(initialValues) && initialValues.length > 0) {
      const muitoBaixo = initialValues.find(item => item.nome === "Muito Baixo até");
      const baixo = initialValues.find(item => item.nome === "Baixo até");
      const medio = initialValues.find(item => item.nome === "Médio até");
      const alto = initialValues.find(item => item.nome === "Alto até");

      if (muitoBaixo) {
        setValue("muitoBaixoValor", muitoBaixo.limite.toString());
        setValue("muitoBaixoCor", muitoBaixo.corHex);
      }

      if (baixo) {
        setValue("baixoValor", baixo.limite.toString());
        setValue("baixoCor", baixo.corHex);
      }

      if (medio) {
        setValue("medioValor", medio.limite.toString());
        setValue("medioCor", medio.corHex);
      }

      if (alto) {
        setValue("altoValor", alto.limite.toString());
        setValue("altoCor", alto.corHex);
      }
    }
  }, [initialValues, setValue]);

  return (
    <div className="grid grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label>Muito Baixo até</Label>
        <div className="flex gap-2">
          <Controller
            name="muitoBaixoValor"
            control={control}
            render={({ field }) => (
              <Input
                type="number"
                placeholder="0.0"
                disabled={disabled}
                {...field}
              />
            )}
          />
          <Controller
            name="muitoBaixoCor"
            control={control}
            render={({ field: { value, onChange } }) => (
              <ColorPicker
                selectedColor={value}
                onColorChange={onChange}
                disabled={disabled}
              />
            )}
          />
        </div>
        {errors.muitoBaixoCor && <p className="text-red-500 text-sm">*{errors.muitoBaixoCor.message}</p>}
      </div>

      <div className="space-y-2">
        <Label>Baixo até</Label>
        <div className="flex gap-2">
          <Controller
            name="baixoValor"
            control={control}
            render={({ field }) => (
              <Input
                type="number"
                placeholder="0.0"
                disabled={disabled}
                {...field}
              />
            )}
          />
          <Controller
            name="baixoCor"
            control={control}
            render={({ field: { value, onChange } }) => (
              <ColorPicker
                selectedColor={value}
                onColorChange={onChange}
                disabled={disabled}
              />
            )}
          />
        </div>
        {errors.baixoCor && <p className="text-red-500 text-sm">*{errors.baixoCor.message}</p>}
      </div>

      <div className="space-y-2">
        <Label>Médio até</Label>
        <div className="flex gap-2">
          <Controller
            name="medioValor"
            control={control}
            render={({ field }) => (
              <Input
                type="number"
                placeholder="0.0"
                disabled={disabled}
                {...field}
              />
            )}
          />
          <Controller
            name="medioCor"
            control={control}
            render={({ field: { value, onChange } }) => (
              <ColorPicker
                selectedColor={value}
                onColorChange={onChange}
                disabled={disabled}
              />
            )}
          />
        </div>
        {errors.medioCor && <p className="text-red-500 text-sm">*{errors.medioCor.message}</p>}
      </div>

      <div className="space-y-2">
        <Label>Alto até</Label>
        <div className="flex gap-2">
          <Controller
            name="altoValor"
            control={control}
            render={({ field }) => (
              <Input
                type="number"
                placeholder="0.0"
                disabled={disabled}
                {...field}
              />
            )}
          />
          <Controller
            name="altoCor"
            control={control}
            render={({ field: { value, onChange } }) => (
              <ColorPicker
                selectedColor={value}
                onColorChange={onChange}
                disabled={disabled}
              />
            )}
          />
        </div>
        {errors.altoCor && <p className="text-red-500 text-sm">*{errors.altoCor.message}</p>}
      </div>
    </div>
  );
}
