import { Input } from "../ui/input";
import PickerColor from "@/components/pickerColor/picker";

export default function MineralForm({ mineral, control, register, errors, setValue, index, watch }) {
  const selectedColor = watch(`limites.${index}.cor`);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-3 gap-4">
        <div className="flex flex-col">
          <label htmlFor={`limites.${index}.minimo`} className="mb-2">De</label>
          <Input
            type="number"
            min={0}
            placeholder="0.0"
            {...register(`limites.${index}.minimo`)}
          />
          {errors?.limites?.[index]?.minimo && (
            <span className="text-red-500 text-sm">*{errors.limites[index].minimo.message}</span>
          )}
        </div>

        <div className="flex flex-col">
          <label htmlFor={`limites.${index}.maximo`} className="mb-2">Até</label>
          <Input
            type="number"
            min={0}
            placeholder="0.0"
            {...register(`limites.${index}.maximo`)}
          />
          {errors?.limites?.[index]?.maximo && (
            <span className="text-red-500 text-sm">*{errors.limites[index].maximo.message}</span>
          )}
        </div>

        <div className="flex flex-col">
          <label className="mb-2">Cor</label>
          <PickerColor
            selectedColor={selectedColor || "#ffffff"}
            onColorChange={(color) => setValue(`limites.${index}.cor`, color)}
          />
          {errors?.limites?.[index]?.cor && (
            <span className="text-red-500 text-sm">*{errors.limites[index].cor.message}</span>
          )}
        </div>
      </div>
    </div>
  );
}
