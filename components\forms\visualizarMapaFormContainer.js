"use client";
import { useToast } from "@/hooks/use-toast";
import { visualizarMapaSchema } from "@/lib/schema/zod";
import ClienteService from "@/lib/services/clienteService";
import FazendaService from "@/lib/services/fazendaService";
import FuncionarioService from "@/lib/services/funcionarioService";
import GerarPontosService from "@/lib/services/gerarPontosService";
import TalhaoService from "@/lib/services/talhaoService";
import VisualizarMapaService from "@/lib/services/visualizarMapa";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "../ui/button";
import { Spinner } from "../ui/spinner";
import MapaContainer from "./MapaContainer";
import VisualizarForm from "./visualizarForm";

export default function VisualizarMapaFormContainer() {
  const { toast } = useToast();
  const router = useRouter();
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { visualizarMapaSchema } from "@/lib/schema/zod";
import VisualizarForm from "./visualizarForm";
import MapaContainer from "./MapaContainer";
import { useState, useEffect } from "react";
import { Button } from "../ui/button";
import { Spinner } from "../ui/spinner";
import VisualizarMapaService from "@/lib/services/visualizarMapa";
import { useToast } from "@/hooks/use-toast";
import FuncionarioService from "@/lib/services/funcionarioService";

export default function VisualizarMapaFormContainer({ initialValue }) {
  const { toast } = useToast();
  const {
    register,
    control,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(visualizarMapaSchema),
    defaultValues: {
      tipoAnalise: [],
    },
    formState: { errors }
  } = useForm({
    resolver: zodResolver(visualizarMapaSchema)
  });

  const [tipoColeta, setTipoColeta] = useState("");
  const [quantidade, setQuantidade] = useState(1);
  const [quantidadePontos, setQuantidadePontos] = useState(10);
  const [mapInitialValue, setMapInitialValue] = useState({});
  const [originalTalhao, setOriginalTalhao] = useState(null);
  const [coletaGerada, setColetaGerada] = useState(false);
  const [loading, setLoading] = useState(false);
  const [funcionarios, setFuncionarios] = useState([]);
  const [clientes, setClientes] = useState([]);
  const [fazendas, setFazendas] = useState([]);
  const [talhoes, setTalhoes] = useState([]);
  const [fazendaID, setFazendaID] = useState("");
  const [talhao, setTalhao] = useState({});
  const [selectedHexagons, setSelectedHexagons] = useState([]);

  // Adicionar refs para importação de pontos GeoJSON
  const pointsFileInputRef = useRef(null);

  const fecthCliente = async () => {
    setLoading(true);
    const clienteService = new ClienteService();
    const clientes = await clienteService.ListarTodosClientes();
    setClientes(clientes);
    setLoading(false);
  };

  const fecthFazenda = async () => {
    setLoading(true);
    const fazendaService = new FazendaService();
    const fazendas = await fazendaService.ListarTodasFazendas();
    setFazendas(fazendas);
    setLoading(false);
  };

  const fecthTalhao = async (id) => {
    setLoading(true);
    const talhaoService = new TalhaoService();
    const talhoes = await talhaoService.ObterTalhaoPorFazenda(id);
    setTalhoes(talhoes.talhoes);
    setLoading(false);
  };

  const fecthTalhaoPorId = async (id) => {
    setLoading(true);
    const talhaoService = new TalhaoService();
    const talhao = await talhaoService.ObterTalhao(id);

    setMapInitialValue(talhao);
    setOriginalTalhao(talhao); // Salva o talhão original para poder resetar o mapa
    setColetaGerada(false); // Reseta o estado de coleta gerada quando um novo talhão é selecionado
    setLoading(false);
  };

  const fecthFuncionarios = async () => {
    const funcionarioService = new FuncionarioService();
    const data = await funcionarioService.listarFuncionarios();

    setFuncionarios(data.items);
  };

  useEffect(() => {
    if (talhao) {
      fecthTalhaoPorId(talhao);
    }
  }, [talhao]);

  useEffect(() => {
    if (fazendaID) {
      fecthTalhao(fazendaID);
    }
  }, [fazendaID]);

  useEffect(() => {
    if (talhao) {
      setMapInitialValue(talhao);
      setOriginalTalhao(talhao); // Salva o talhão original para poder resetar o mapa
      setColetaGerada(false); // Reseta o estado de coleta gerada quando um novo talhão é selecionado
    }
  }, [talhao]);

  useEffect(() => {
    fecthCliente();
    fecthFazenda();
    fecthFuncionarios();
  }, []);

  // Nova função para formatar coordenadas
  const transformarCoordenadas = (coordenadas) => {
    const coordsFormatadas = coordenadas.map((coord) => [coord.lng, coord.lat]);
  const [mapInitialValue, setMapInitialValue] = useState({});
  const [loading, setLoading] = useState(false);
  const [funcionarios, setFuncionarios] = useState([]);

  // Nova função para formatar coordenadas
  const transformarCoordenadas = (coordenadas) => {
    const coordsFormatadas = coordenadas.map(coord => [coord.lng, coord.lat]);

    if (coordsFormatadas.length > 0) {
      coordsFormatadas.push(coordsFormatadas[0]);
    }

    return {
      polygon: {
        type: "FeatureCollection",
        features: [
          {
            type: "Feature",
            geometry: {
              type: "Polygon",
              coordinates: [coordsFormatadas],
            },
          },
        ],
      },
      hectares: parseInt(quantidade),
    };
  };

  const gerarPontos = async (coordenadas) => {
    try {
      const gerarPontosService = new GerarPontosService();

      // Prepara os dados para a API de geração de pontos
      let obj = {
        qtdPontosNaArea: parseInt(quantidadePontos) || 10,
        geoJsonAreas: coordenadas,
      };

      // Chama a API para gerar os pontos
      const pontosGerados = await gerarPontosService.GerarPontos(obj);

      if (!pontosGerados) {
        throw new Error("Erro ao gerar pontos");
      }

      return pontosGerados;
    } catch (error) {
      console.error("Erro ao gerar pontos:", error);
      toast({
        title: "Erro",
        description: "Erro ao gerar pontos na área",
        variant: "destructive",
      });
      return null;
    }
  };

  const handleGerarColeta = async () => {
    // Verifica se a coleta já foi gerada
    if (coletaGerada) {
      return toast({
        title: "Aviso",
        description:
          "Uma coleta já foi gerada. Clique em 'Resetar Mapa' para gerar uma nova coleta.",
        variant: "warning",
      });
    }

    // Limpa os hexágonos selecionados ao gerar uma nova coleta
    setSelectedHexagons([]);

              coordinates: [coordsFormatadas]
            }
          }
        ]
      },
      hectares: parseInt(quantidade)
    };
  };

  const handleGerarColeta = async () => {
    if (!tipoColeta || !quantidade) {
      return toast({
        title: "Erro",
        description: "Selecione o tipo de coleta e a quantidade de grid",
        variant: "destructive",
      });
    }

    // Verifica se temos um talhão original salvo antes de gerar a coleta
    if (
      !originalTalhao ||
      !originalTalhao.talhoes ||
      !originalTalhao.talhoes[0]
    ) {
      return toast({
        title: "Erro",
        description: "Não foi possível encontrar o talhão para gerar a coleta",
        variant: "destructive",
        variant: "destructive"
      });
    }

    setLoading(true);
    const visualizarMapaService = new VisualizarMapaService();

    try {
      // Converte as coordenadas para o formato exigido pela API
      const talhaoFormatado = transformarCoordenadas(
        mapInitialValue.talhoes[0].coordenadas
      );
      const data = await visualizarMapaService.GerarColeta(talhaoFormatado);
      if (!data) {
        throw new Error("Erro ao gerar coleta");
      }
      // Gera os pontos com base nos hexágonos gerados
      console.log(data);
      const pontos = await gerarPontos(data);

      // Adiciona os pontos ao objeto de dados
      if (pontos) {
        data.points = pontos;
      }

      setMapInitialValue(data);
      setColetaGerada(true); // Marca que a coleta foi gerada

      toast({
        title: "Sucesso",
        description:
          "Coleta e pontos gerados com sucesso. Use o botão 'Resetar Mapa' para limpar.",
      const talhaoFormatado = transformarCoordenadas(initialValue.talhoes[0].coordenadas);
      const data = await visualizarMapaService.GerarColeta(
        talhaoFormatado,
      );

      if (!data) {
        throw new Error("Erro ao gerar coleta");
      }

      setMapInitialValue(data);

      toast({
        title: "Sucesso",
        description: "Coleta gerada com sucesso",
      });
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro",
        description: "Erro ao gerar coleta",
        variant: "destructive",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResetarMapa = () => {
    // Reseta o mapa para o estado original do talhão, removendo os hexágonos e números
    if (originalTalhao) {
      setMapInitialValue(originalTalhao);
      setColetaGerada(false); // Permite gerar uma nova coleta
      setSelectedHexagons([]); // Limpa os hexágonos selecionados
      toast({
        title: "Sucesso",
        description: "Mapa resetado com sucesso",
      });
    } else {
      toast({
        title: "Aviso",
        description: "Não há um mapa original para resetar",
        variant: "destructive",
      });
    }
  };

  // Função para lidar com a seleção de hexágonos
  const handleHexagonSelect = (hexagonId) => {
    setSelectedHexagons((prev) => {
      // Se o hexágono já está selecionado, remove-o da lista
      if (prev.includes(hexagonId)) {
        return prev.filter((id) => id !== hexagonId);
      }
      // Caso contrário, adiciona-o à lista
      return [...prev, hexagonId];
    });
  };

  // Função para mesclar hexágonos selecionados
  const handleMesclarHexagonos = () => {
    // Verifica se há pelo menos 2 hexágonos selecionados
    if (selectedHexagons.length < 2) {
      return toast({
        title: "Aviso",
        description: "Selecione pelo menos 2 hexágonos para mesclar",
        variant: "warning",
      });
    }

    // Verifica se temos features no mapInitialValue
    if (!mapInitialValue.features || !Array.isArray(mapInitialValue.features)) {
      return toast({
        title: "Erro",
        description: "Não foi possível encontrar hexágonos para mesclar",
        variant: "destructive",
      });
    }

    try {
      // Cria uma cópia do mapInitialValue para não modificar o original diretamente
      const updatedMap = JSON.parse(JSON.stringify(mapInitialValue));

      // Encontra os hexágonos selecionados
      const selectedFeatures = updatedMap.features.filter((feature) =>
        selectedHexagons.includes(feature.properties?.id)
      );

      if (selectedFeatures.length < 2) {
        throw new Error(
          "Não foi possível encontrar todos os hexágonos selecionados"
        );
      }

      // Pega o primeiro hexágono como base para o mesclado
      const baseFeature = selectedFeatures[0];

      // Cria um novo polígono mesclando todos os pontos dos hexágonos selecionados
      // Usamos um Set para evitar pontos duplicados
      const allCoordinates = new Set();

      // Adiciona todas as coordenadas de todos os hexágonos selecionados
      selectedFeatures.forEach((feature) => {
        feature.geometry.coordinates[0].forEach((coord) => {
          // Converte para string para poder usar no Set
          allCoordinates.add(JSON.stringify(coord));
        });
      });

      // Converte de volta para array de coordenadas
      const uniqueCoords = Array.from(allCoordinates).map((coord) =>
        JSON.parse(coord)
      );

      // Ordena as coordenadas para formar um polígono válido
      // Isso é importante para garantir que o polígono mesclado tenha uma forma adequada
      // Usamos uma abordagem simples: ordenamos os pontos em relação ao centro

      // Calcula o centro dos pontos
      const center = uniqueCoords.reduce(
        (acc, coord) => {
          acc[0] += coord[0] / uniqueCoords.length;
          acc[1] += coord[1] / uniqueCoords.length;
          return acc;
        },
        [0, 0]
      );

      // Ordena os pontos em sentido horário em relação ao centro
      uniqueCoords.sort((a, b) => {
        const angleA = Math.atan2(a[1] - center[1], a[0] - center[0]);
        const angleB = Math.atan2(b[1] - center[1], b[0] - center[0]);
        return angleA - angleB;
      });

      // Determina o novo ID lógico para o hexágono mesclado
      // Primeiro, converte todos os IDs para números (se possível)
      const numericIds = selectedHexagons
        .map((id) => {
          // Tenta converter para número, se não for possível, retorna null
          const numId = parseInt(id);
          return isNaN(numId) ? null : numId;
        })
        .filter((id) => id !== null); // Remove os IDs que não são números

      // Se todos os IDs forem numéricos, mantém a sequência lógica
      let newId;
      if (numericIds.length === selectedHexagons.length) {
        // Ordena os IDs numéricos
        numericIds.sort((a, b) => a - b);

        // Encontra o menor ID para usar como ID do hexágono mesclado
        const minId = numericIds[0];

        // O novo ID é o menor ID entre os selecionados
        newId = minId.toString();

        // Exemplo: se mesclarmos 5 e 6, o novo ID será 5
        // Assim, a sequência ficará 1, 2, 3, 4, 5 (onde 5 é o resultado da mesclagem de 5 e 6)
      } else {
        // Se algum ID não for numérico, usa um ID baseado no timestamp
        newId = `merged_${Date.now()}`;
      }

      // Cria um novo feature com as coordenadas mescladas
      const mergedFeature = {
        ...baseFeature,
        properties: {
          ...baseFeature.properties,
          id: newId, // Usa o ID lógico determinado
          merged: true, // Marca como mesclado
          originalIds: selectedHexagons, // Guarda os IDs originais
        },
        geometry: {
          ...baseFeature.geometry,
          coordinates: [uniqueCoords], // Usa as coordenadas mescladas
        },
      };

      // Remove os hexágonos selecionados e adiciona o mesclado
      updatedMap.features = updatedMap.features.filter(
        (feature) => !selectedHexagons.includes(feature.properties?.id)
      );
      updatedMap.features.push(mergedFeature);

      // Atualiza o mapa
      setMapInitialValue(updatedMap);

      // Limpa a seleção
      setSelectedHexagons([]);

      // Formata os IDs para a mensagem de sucesso
      const idsFormatted = selectedHexagons.sort().join(", ");

      toast({
        title: "Sucesso",
        description: `Hexágonos ${idsFormatted} mesclados com sucesso em um único hexágono com ID ${newId}`,
      });
    } catch (error) {
      console.error("Erro ao mesclar hexágonos:", error);
      toast({
        title: "Erro",
        description: "Erro ao mesclar hexágonos: " + error.message,
        variant: "destructive",
      });
    }
  };

    setMapInitialValue(initialValue);
    toast({
      title: "Sucesso",
      description: "Mapa resetado com sucesso",
    });
  };

  useEffect(() => {
    setMapInitialValue((prev) => ({
      ...prev,
      tipoColeta,
      quantidade,
    }));
  }, [tipoColeta, quantidade]);

  const onSubmit = async (data) => {
    setLoading(true);
    const visualizarMapaService = new VisualizarMapaService();
    let obj = {
      ...data,
      geoJson: mapInitialValue,
      talhaoID: talhao,
    };
    const cadastrar = await visualizarMapaService.CadastrarColeta(obj);
    if (!cadastrar) {
      setLoading(false);
      return toast({
        title: "Erro",
        description: "Erro ao cadastrar coleta",
        variant: "destructive",
      });
    }
    toast({
      title: "Sucesso",
      description: "Coleta cadastrada com sucesso",
    });
    setLoading(false);
    router.push("/admin/visualizarmapa");
  };

  // Nova função para importar pontos de coleta GeoJSON
  const handleImportPoints = () => {
    pointsFileInputRef.current?.click();
  };

  const handlePointsFileChange = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.endsWith(".geojson") && !file.name.endsWith(".json")) {
      toast({
        title: "Erro",
        description:
          "Por favor, selecione um arquivo GeoJSON válido (.geojson ou .json)",
        variant: "destructive",
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const geoJSON = JSON.parse(e.target.result);
        processPointsGeoJSON(geoJSON);
      } catch (error) {
        console.error("Erro ao processar arquivo GeoJSON:", error);
        toast({
          title: "Erro",
          description:
            "Arquivo GeoJSON inválido. Verifique a estrutura do arquivo.",
          variant: "destructive",
        });
      }
    };
    reader.readAsText(file);

    // Limpa o input para permitir reimportação do mesmo arquivo
    event.target.value = "";
  };

  const processPointsGeoJSON = (geoJSON) => {
    try {
      let points = [];
      let validPointsCount = 0;
      let invalidPointsCount = 0;

      const processFeature = (feature) => {
        const { geometry, properties } = feature;

        if (
          geometry &&
          geometry.type === "Point" &&
          Array.isArray(geometry.coordinates)
        ) {
          const [lng, lat] = geometry.coordinates;

          // Valida se as coordenadas são números válidos
          if (
            typeof lng === "number" &&
            typeof lat === "number" &&
            !isNaN(lng) &&
            !isNaN(lat) &&
            lng >= -180 &&
            lng <= 180 &&
            lat >= -90 &&
            lat <= 90
          ) {
            points.push({
              geometry: {
                type: "Point",
                coordinates: [lng, lat],
              },
              properties: {
                id:
                  properties?.id ||
                  properties?.name ||
                  properties?.nome ||
                  `Ponto ${validPointsCount + 1}`,
                name:
                  properties?.name ||
                  properties?.nome ||
                  `Ponto de Coleta ${validPointsCount + 1}`,
                ...properties,
              },
            });
            validPointsCount++;
          } else {
            invalidPointsCount++;
            console.warn(
              "Coordenadas inválidas encontradas:",
              geometry.coordinates
            );
          }
        } else {
          invalidPointsCount++;
          console.warn("Geometria não é do tipo Point:", geometry);
        }
      };

      // Processa diferentes tipos de estrutura GeoJSON
      if (
        geoJSON.type === "FeatureCollection" &&
        Array.isArray(geoJSON.features)
      ) {
        geoJSON.features.forEach(processFeature);
      } else if (geoJSON.type === "Feature") {
        processFeature(geoJSON);
      } else if (geoJSON.type === "Point") {
        processFeature({ geometry: geoJSON, properties: {} });
      } else {
        throw new Error("Estrutura GeoJSON não suportada para pontos");
      }

      if (validPointsCount === 0) {
        toast({
          title: "Aviso",
          description: "Nenhum ponto válido encontrado no arquivo GeoJSON",
          variant: "destructive",
        });
        return;
      }

      // Adiciona os pontos ao mapa atual
      setMapInitialValue((prevValue) => ({
        ...prevValue,
        points: points,
      }));

      // Se há pontos e não há talhão, centraliza o mapa no primeiro ponto
      if (
        points.length > 0 &&
        (!mapInitialValue.talhoes || mapInitialValue.talhoes.length === 0)
      ) {
        const firstPoint = points[0];
        const [lng, lat] = firstPoint.geometry.coordinates;

        setMapInitialValue((prevValue) => ({
          ...prevValue,
          points: points,
          center: { lat, lng },
        }));
      }

      // Mostra feedback para o usuário
      let message = `${validPointsCount} ponto(s) de coleta importado(s) com sucesso`;
      if (invalidPointsCount > 0) {
        message += `. ${invalidPointsCount} ponto(s) inválido(s) foram ignorados`;
      }

      toast({
        title: "Sucesso",
        description: message,
      });

      console.log(`Pontos importados:`, points);
    } catch (error) {
      console.error("Erro ao processar pontos GeoJSON:", error);
      toast({
        title: "Erro",
        description: `Erro ao processar pontos: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  // Função para limpar pontos importados
  const handleClearPoints = () => {
    setMapInitialValue((prevValue) => ({
      ...prevValue,
      points: [],
    }));

    toast({
      title: "Sucesso",
      description: "Pontos de coleta removidos do mapa",
    });
  };
  const fecthFuncionarios = async () => {
    const funcionarioService = new FuncionarioService();
    const data = await funcionarioService.listarFuncionarios();
    console.log(data);
    setFuncionarios(data.items || []);
  }

  useEffect(() => {
    fecthFuncionarios();  
    if (initialValue) {
      setMapInitialValue(initialValue);
    }
  }, [initialValue]);

  const onSubmit = async (data) => {
    let obj = {
      ...data,
      geoJson: mapInitialValue,
      talhaoID: initialValue.talhoes[0].id
    }
    console.log(obj);
  }

  return (
    <>
      {loading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
          <Spinner message="carregando..." className="text-white w-10 h-10" />
        </div>
      )}
      <form onSubmit={handleSubmit(onSubmit)}>
        <VisualizarForm
          control={control}
          register={register}
          funcionarios={funcionarios}
          errors={errors}
          setValue={setValue}
          defaultValues={mapInitialValue}
          onChangeColeta={setTipoColeta}
          onChangeQuantidade={setQuantidade}
          onChangePontos={(value) => setQuantidadePontos(value)}
          clientes={clientes}
          fazendas={fazendas}
          talhoes={talhoes}
          onFazendaSelect={setFazendaID}
          onTalhaoSelect={setTalhao}
          defaultValues={initialValue}
          onChangeColeta={setTipoColeta}
          onChangeQuantidade={setQuantidade}
        >
          <div className="mt-4">
            <div className="flex justify-end gap-2 mb-4">
              <Button
                onClick={handleClearPoints}
                variant="outline"
                type="button"
                disabled={
                  !mapInitialValue.points || mapInitialValue.points.length === 0
                }
              >
                Limpar Pontos
              </Button>
              <Button
                onClick={handleImportPoints}
                variant="outline"
                type="button"
              >
                Importar Pontos GeoJSON
              </Button>
              <Button
                onClick={handleResetarMapa}
                variant="outline"
                type="button"
              >
                Resetar Mapa
              </Button>
              <Button
                onClick={handleGerarColeta}
                type="button"
                disabled={loading || coletaGerada}
              >
                Gerar Coleta
              </Button>
              <Button
                onClick={handleMesclarHexagonos}
                type="button"
                variant="secondary"
                disabled={loading || selectedHexagons.length < 2}
              >
                {selectedHexagons.length === 0
                  ? "Selecione Hexágonos"
                  : selectedHexagons.length === 1
                  ? "Selecione mais Hexágonos"
                  : `Mesclar ${selectedHexagons.length} Hexágonos`}
              </Button>
            </div>
            <MapaContainer
              initialValue={mapInitialValue}
              onHexagonSelect={handleHexagonSelect}
              selectedHexagons={selectedHexagons}
              mapMode="select"
            />
                disabled={loading}
              >
                Gerar Coleta
              </Button>
            </div>
            <MapaContainer initialValue={mapInitialValue} />
          </div>
        </VisualizarForm>
        <Button type="submit" className="mt-4 w-24" disabled={loading}>
          {loading ? <Spinner /> : "Cadastrar"}
        </Button>
      </form>

      {/* Input oculto para seleção de arquivo de pontos GeoJSON */}
      <input
        ref={pointsFileInputRef}
        type="file"
        accept=".geojson,.json"
        onChange={handlePointsFileChange}
        style={{ display: "none" }}
      />
    </>
  );
}
