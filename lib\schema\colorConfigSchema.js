import { z } from 'zod';

export const colorConfigSchema = z.object({
  muitoBaixoValor: z.string().optional(),
  baixoValor: z.string().optional(),
  medioValor: z.string().optional(),
  altoValor: z.string().optional(),
  muitoBaixoCor: z.string().min(1, { message: "Cor obrigatória" }),
  baixoCor: z.string().min(1, { message: "Cor obrigatória" }),
  medioCor: z.string().min(1, { message: "Cor obrigatória" }),
  altoCor: z.string().min(1, { message: "Cor obrigatória" }),
});
