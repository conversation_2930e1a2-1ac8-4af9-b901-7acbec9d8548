export class FormatUtils {
  static formatCPF(cpf) {
    if (!cpf) return '';
    const cleaned = cpf.replace(/\D/g, '');
    return cleaned.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  }

  static formatCNPJ(cnpj) {
    if (!cnpj) return '';
    const cleaned = cnpj.replace(/\D/g, '');
    return cleaned.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
  }

  static formatPhone(phone) {
    if (!phone) return '';
    const cleaned = phone.replace(/\D/g, '');
    
    if (cleaned.length === 10) {
      return cleaned.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    
    if (cleaned.length === 11) {
      return cleaned.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    }
    
    return phone;
  }

  static formatCurrency(value, currency = 'BRL') {
    if (value === null || value === undefined) return '';
    
    const number = Number(value);
    if (isNaN(number)) return '';
    
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency
    }).format(number);
  }

  static formatNumber(value, decimals = 2) {
    if (value === null || value === undefined) return '';
    
    const number = Number(value);
    if (isNaN(number)) return '';
    
    return new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(number);
  }

  static formatDate(date, format = 'dd/MM/yyyy') {
    if (!date) return '';
    
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return '';
    
    const day = String(dateObj.getDate()).padStart(2, '0');
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const year = dateObj.getFullYear();
    
    switch (format) {
      case 'dd/MM/yyyy':
        return `${day}/${month}/${year}`;
      case 'MM/dd/yyyy':
        return `${month}/${day}/${year}`;
      case 'yyyy-MM-dd':
        return `${year}-${month}-${day}`;
      default:
        return `${day}/${month}/${year}`;
    }
  }

  static formatDateTime(date) {
    if (!date) return '';
    
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return '';
    
    const formattedDate = this.formatDate(dateObj);
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    
    return `${formattedDate} ${hours}:${minutes}`;
  }

  static formatArea(area, unit = 'ha') {
    if (area === null || area === undefined) return '';
    
    const number = Number(area);
    if (isNaN(number)) return '';
    
    return `${this.formatNumber(number)} ${unit}`;
  }

  static formatCoordinate(coordinate, decimals = 6) {
    if (coordinate === null || coordinate === undefined) return '';
    
    const number = Number(coordinate);
    if (isNaN(number)) return '';
    
    return this.formatNumber(number, decimals);
  }

  static formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  static formatPercentage(value, decimals = 1) {
    if (value === null || value === undefined) return '';
    
    const number = Number(value);
    if (isNaN(number)) return '';
    
    return `${this.formatNumber(number * 100, decimals)}%`;
  }

  static truncateText(text, maxLength = 50) {
    if (!text) return '';
    
    if (text.length <= maxLength) return text;
    
    return text.substring(0, maxLength) + '...';
  }

  static capitalizeFirst(text) {
    if (!text) return '';
    
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  }

  static capitalizeWords(text) {
    if (!text) return '';
    
    return text.split(' ')
      .map(word => this.capitalizeFirst(word))
      .join(' ');
  }

  static removeSpecialChars(text) {
    if (!text) return '';
    
    return text.replace(/[^\w\s]/gi, '');
  }

  static onlyNumbers(text) {
    if (!text) return '';
    
    return text.replace(/\D/g, '');
  }

  static formatMineralValue(value, unit = '') {
    if (value === null || value === undefined) return '';
    
    const number = Number(value);
    if (isNaN(number)) return '';
    
    const formatted = this.formatNumber(number, 2);
    return unit ? `${formatted} ${unit}` : formatted;
  }

  static formatRange(min, max, unit = '') {
    const formattedMin = this.formatNumber(min, 2);
    const formattedMax = this.formatNumber(max, 2);
    const range = `${formattedMin} - ${formattedMax}`;
    
    return unit ? `${range} ${unit}` : range;
  }
}

export const maskCPF = (value) => {
  return FormatUtils.formatCPF(value);
};

export const maskCNPJ = (value) => {
  return FormatUtils.formatCNPJ(value);
};

export const maskPhone = (value) => {
  return FormatUtils.formatPhone(value);
};

export const maskCurrency = (value) => {
  return FormatUtils.formatCurrency(value);
};
