"use client";
import * as XLSX from 'xlsx';
import { useState, useRef } from 'react';
import { Table, TableBody, TableCell, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, ScrollBar } from "@/components/ui/scroll-area";
import { Upload, Download, FileSpreadsheet, Save } from "lucide-react";
import { Input } from "@/components/ui/input";

export default function ExcelEditor() {
  const [data, setData] = useState([]);
  const [fileName, setFileName] = useState("");
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef(null);

  const handleUpload = (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    processFile(file);
  };

  const processFile = (file) => {
    setFileName(file.name);
    const reader = new FileReader();
    reader.onload = (event) => {
      const workbook = XLSX.read(event.target?.result, { type: 'binary' });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      const raw = XLSX.utils.sheet_to_json(sheet, { header: 1 });
      const maxCols = Math.max(...raw.map(row => row.length));
      const padded = raw.map(row => {
        const newRow = [...row];
        while (newRow.length < maxCols) {
          newRow.push('');
        }
        return newRow;
      });
      setData(padded);

    };
    reader.readAsBinaryString(file);
  };


  const handleDownload = () => {
    const ws = XLSX.utils.aoa_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Planilha');
    XLSX.writeFile(wb, fileName || 'planilha-editada.xlsx');
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    const file = e.dataTransfer.files[0];
    if (file && (file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.type === "application/vnd.ms-excel")) {
      processFile(file);
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-4">
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <div
            onClick={() => fileInputRef.current?.click()}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            className={`
              flex items-center gap-2 px-4 py-2 border-2 border-dashed rounded-lg 
              cursor-pointer transition-colors duration-200
              ${isDragging ? 'border-primary bg-primary/10' : 'border-muted hover:bg-muted'}
            `}
          >
            <Upload className="w-4 h-4" />
            <span>{fileName || "Arraste um arquivo Excel ou clique para selecionar"}</span>
            <input
              ref={fileInputRef}
              type="file"
              accept=".xlsx,.xls"
              onChange={handleUpload}
              className="hidden"
            />
          </div>
        </div>
        {data.length > 0 && (
          <Button
            onClick={handleDownload}
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            Baixar Excel
          </Button>
        )}
      </div>

      {data.length > 0 && (
        <div className="border rounded-lg shadow-sm">
          <ScrollArea className="h-[calc(100vh-200px)] w-full">
            <div className="min-w-max">
              <Table>
                <TableHeader className="sticky top-0 bg-background z-10">
                  <TableRow>
                    {data[0].map((_, index) => (
                      <TableCell
                        key={index}
                        className="font-bold text-center bg-muted/50"
                      >
                        {`Coluna ${index + 1}`}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.map((row, rowIndex) => (
                    <TableRow key={rowIndex}>
                      {row.map((cell, colIndex) => (
                        <TableCell key={`${rowIndex}-${colIndex}`} className="p-0">
                          <Input
                            value={cell || ''}
                            onChange={(e) => {
                              const updated = [...data];
                              updated[rowIndex][colIndex] = e.target.value;
                              setData(updated);
                            }}
                            className="border-0 focus:ring-0 text-center h-10"
                          />
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </div>
      )}

      {!data.length && (
        <div
          className="flex flex-col items-center justify-center h-[400px] border-2 border-dashed rounded-lg"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <FileSpreadsheet className="w-16 h-16 text-muted-foreground mb-4" />
          <p className="text-muted-foreground">
            Arraste um arquivo Excel ou clique para fazer upload
          </p>
        </div>
      )}

      <div className="flex mt-4">
        <Button
          onClick={handleDownload}
          className="flex items-center gap-2"
        >
          Salvar Excel
        </Button>
      </div>
    </div>
  );
}
