# HTTP Client with Interceptor

This directory contains the HTTP client implementation with an interceptor pattern for the application.

## Files

- `httpClient.js` - The main HTTP client used throughout the application
- `httpInterceptor.js` - The interceptor class that allows modifying requests and responses
- `interceptorExample.js` - Examples of how to use the interceptor

## How to Use the Interceptor

The interceptor allows you to add global request and response processing. This is useful for:

- Adding authentication headers to all requests
- Handling error responses globally
- Logging requests and responses
- Implementing loading indicators
- Refreshing tokens
- And more...

### Adding Request Interceptors

Request interceptors are executed before a request is sent. They receive the request configuration and should return the modified configuration.

```javascript
import interceptor from './lib/http/httpInterceptor';

// Add a request interceptor
const interceptorId = interceptor.addRequestInterceptor((config) => {
  // Modify the config
  config.headers = {
    ...config.headers,
    'X-Custom-Header': 'Value'
  };
  
  return config;
});
```

### Adding Response Interceptors

Response interceptors are executed after a response is received. They receive the response and should return the modified response.

```javascript
// Add a response interceptor
const interceptorId = interceptor.addResponseInterceptor(async (response) => {
  // Clone the response to avoid consuming it
  const clonedResponse = response.clone();
  
  // Handle response
  if (!response.ok) {
    // Handle error globally
    console.error('API Error:', response.status);
  }
  
  return response;
});
```

### Removing Interceptors

You can remove interceptors when they're no longer needed:

```javascript
// Remove interceptors
interceptor.removeRequestInterceptor(interceptorId);
interceptor.removeResponseInterceptor(interceptorId);
```

## Common Use Cases

### Authentication

```javascript
// Add authentication to all requests
interceptor.addRequestInterceptor((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers = {
      ...config.headers,
      'Authorization': `Bearer ${token}`
    };
  }
  return config;
});
```

### Global Error Handling

```javascript
// Handle errors globally
interceptor.addResponseInterceptor(async (response) => {
  if (response.status === 401) {
    // Redirect to login
    window.location.href = '/login';
  }
  return response;
});
```

### Loading Indicators

```javascript
let activeRequests = 0;

// Track active requests
interceptor.addRequestInterceptor((config) => {
  activeRequests++;
  showLoadingIndicator();
  return config;
});

interceptor.addResponseInterceptor((response) => {
  activeRequests--;
  if (activeRequests === 0) {
    hideLoadingIndicator();
  }
  return response;
});
```

## Integration with Services

The HTTP client with interceptor is already integrated with all services. You don't need to modify any service code to use the interceptor functionality.
