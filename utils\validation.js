import { ValidationRules } from '@/types';

export class ValidationUtils {
  static isRequired(value) {
    return value !== null && value !== undefined && value !== '';
  }

  static isEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static hasMinLength(value, minLength) {
    return value && value.length >= minLength;
  }

  static hasMaxLength(value, maxLength) {
    return !value || value.length <= maxLength;
  }

  static isPositiveNumber(value) {
    const num = Number(value);
    return !isNaN(num) && num > 0;
  }

  static isValidCoordinate(lat, lng) {
    const latitude = Number(lat);
    const longitude = Number(lng);
    
    return !isNaN(latitude) && 
           !isNaN(longitude) && 
           latitude >= -90 && 
           latitude <= 90 && 
           longitude >= -180 && 
           longitude <= 180;
  }

  static isValidCPF(cpf) {
    if (!cpf) return false;
    
    cpf = cpf.replace(/[^\d]/g, '');
    
    if (cpf.length !== 11) return false;
    if (/^(\d)\1{10}$/.test(cpf)) return false;
    
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cpf.charAt(i)) * (10 - i);
    }
    
    let remainder = 11 - (sum % 11);
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cpf.charAt(9))) return false;
    
    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cpf.charAt(i)) * (11 - i);
    }
    
    remainder = 11 - (sum % 11);
    if (remainder === 10 || remainder === 11) remainder = 0;
    
    return remainder === parseInt(cpf.charAt(10));
  }

  static isValidCNPJ(cnpj) {
    if (!cnpj) return false;
    
    cnpj = cnpj.replace(/[^\d]/g, '');
    
    if (cnpj.length !== 14) return false;
    if (/^(\d)\1{13}$/.test(cnpj)) return false;
    
    let length = cnpj.length - 2;
    let numbers = cnpj.substring(0, length);
    let digits = cnpj.substring(length);
    let sum = 0;
    let pos = length - 7;
    
    for (let i = length; i >= 1; i--) {
      sum += numbers.charAt(length - i) * pos--;
      if (pos < 2) pos = 9;
    }
    
    let result = sum % 11 < 2 ? 0 : 11 - sum % 11;
    if (result !== parseInt(digits.charAt(0))) return false;
    
    length = length + 1;
    numbers = cnpj.substring(0, length);
    sum = 0;
    pos = length - 7;
    
    for (let i = length; i >= 1; i--) {
      sum += numbers.charAt(length - i) * pos--;
      if (pos < 2) pos = 9;
    }
    
    result = sum % 11 < 2 ? 0 : 11 - sum % 11;
    
    return result === parseInt(digits.charAt(1));
  }

  static validateField(value, rules) {
    const errors = [];
    
    if (rules.required && !this.isRequired(value)) {
      errors.push(ValidationRules.REQUIRED);
    }
    
    if (rules.email && value && !this.isEmail(value)) {
      errors.push(ValidationRules.EMAIL);
    }
    
    if (rules.minLength && value && !this.hasMinLength(value, rules.minLength)) {
      errors.push(ValidationRules.MIN_LENGTH(rules.minLength));
    }
    
    if (rules.maxLength && !this.hasMaxLength(value, rules.maxLength)) {
      errors.push(ValidationRules.MAX_LENGTH(rules.maxLength));
    }
    
    if (rules.positiveNumber && value && !this.isPositiveNumber(value)) {
      errors.push(ValidationRules.POSITIVE_NUMBER);
    }
    
    return errors;
  }

  static validateForm(formData, validationSchema) {
    const errors = {};
    
    Object.keys(validationSchema).forEach(fieldName => {
      const fieldValue = formData[fieldName];
      const fieldRules = validationSchema[fieldName];
      const fieldErrors = this.validateField(fieldValue, fieldRules);
      
      if (fieldErrors.length > 0) {
        errors[fieldName] = fieldErrors[0];
      }
    });
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
}

export const createValidationSchema = (fields) => {
  return fields.reduce((schema, field) => {
    schema[field.name] = field.rules || {};
    return schema;
  }, {});
};

export const getFieldError = (errors, fieldName) => {
  return errors[fieldName] || null;
};

export const hasFieldError = (errors, fieldName) => {
  return Boolean(errors[fieldName]);
};
