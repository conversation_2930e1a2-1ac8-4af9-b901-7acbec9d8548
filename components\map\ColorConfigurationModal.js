"use client";
import { useFieldArray, useForm } from 'react-hook-form';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, ModalFooter } from '@/components/base/Modal';
import { Button } from '@/components/base/Button';
import { Input } from '@/components/base/Input';

const ColorRangeField = ({ 
  field, 
  index, 
  register, 
  watch, 
  setValue, 
  onRemove,
  canRemove 
}) => (
  <div className="relative border p-4 rounded-md shadow-sm bg-muted">
    <div className="grid grid-cols-3 gap-4">
      <Input
        type="number"
        min={0}
        placeholder="De"
        {...register(`limites.${index}.minimo`)}
        label="De"
      />

      <Input
        type="number"
        min={0}
        placeholder="Até"
        {...register(`limites.${index}.maximo`)}
        label="Até"
      />

      <div className="flex flex-col">
        <label className="text-sm font-medium mb-2">Cor</label>
        <input
          type="color"
          value={watch(`limites.${index}.cor`) || "#ffffff"}
          onChange={(e) => setValue(`limites.${index}.cor`, e.target.value)}
          className="w-full h-10 border rounded-md cursor-pointer"
        />
      </div>
    </div>
    
    {canRemove && (
      <Button
        type="button"
        variant="ghost"
        size="sm"
        className="absolute top-2 right-2"
        onClick={() => onRemove(index)}
      >
        ×
      </Button>
    )}
  </div>
);

const ColorRangeList = ({ 
  fields, 
  register, 
  watch, 
  setValue, 
  remove, 
  append 
}) => (
  <div className="space-y-4">
    {fields.map((field, index) => (
      <ColorRangeField
        key={field.id}
        field={field}
        index={index}
        register={register}
        watch={watch}
        setValue={setValue}
        onRemove={remove}
        canRemove={fields.length > 1}
      />
    ))}
    
    <Button
      type="button"
      variant="outline"
      onClick={() => append({ minimo: "", maximo: "", cor: "#ffffff" })}
    >
      Adicionar Intervalo
    </Button>
  </div>
);

export const ColorConfigurationModal = ({
  open,
  onOpenChange,
  selectedMineral,
  onSave
}) => {
  const { control, register, watch, setValue, handleSubmit, reset } = useForm({
    defaultValues: {
      limites: [
        { minimo: "0", maximo: "2", cor: "#FF0000" },
        { minimo: "3", maximo: "4", cor: "#00FF00" },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "limites",
  });

  const handleSave = (data) => {
    onSave(data.limites);
    onOpenChange(false);
  };

  const handleCancel = () => {
    reset();
    onOpenChange(false);
  };

  return (
    <Modal open={open} onOpenChange={onOpenChange}>
      <form onSubmit={handleSubmit(handleSave)}>
        <ModalHeader
          title={`Configuração de Cores para ${selectedMineral}`}
          description="Configure os intervalos de valores e cores para o mineral selecionado."
        />
        
        <ModalBody>
          <ColorRangeList
            fields={fields}
            register={register}
            watch={watch}
            setValue={setValue}
            remove={remove}
            append={append}
          />
        </ModalBody>
        
        <ModalFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
          >
            Cancelar
          </Button>
          <Button type="submit">
            Salvar Configuração
          </Button>
        </ModalFooter>
      </form>
    </Modal>
  );
};

export default ColorConfigurationModal;
