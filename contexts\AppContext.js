"use client";
import { createContext, useContext, useReducer, useCallback } from 'react';
import { LoadingState, UserRole } from '@/types';

// Initial State
const initialState = {
  user: null,
  isAuthenticated: false,
  loading: LoadingState.IDLE,
  error: null,
  notifications: [],
  theme: 'light',
  sidebarOpen: true,
  currentPage: null
};

// Action Types
const ActionTypes = {
  SET_USER: 'SET_USER',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  ADD_NOTIFICATION: 'ADD_NOTIFICATION',
  REMOVE_NOTIFICATION: 'REMOVE_NOTIFICATION',
  CLEAR_NOTIFICATIONS: 'CLEAR_NOTIFICATIONS',
  SET_THEME: 'SET_THEME',
  TOGGLE_SIDEBAR: 'TOGGLE_SIDEBAR',
  SET_SIDEBAR_OPEN: 'SET_SIDEBAR_OPEN',
  SET_CURRENT_PAGE: 'SET_CURRENT_PAGE',
  LOGOUT: 'LOGOUT'
};

// Reducer
const appReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: Boolean(action.payload),
        loading: LoadingState.SUCCESS
      };

    case ActionTypes.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };

    case ActionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: LoadingState.ERROR
      };

    case ActionTypes.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    case ActionTypes.ADD_NOTIFICATION:
      return {
        ...state,
        notifications: [...state.notifications, action.payload]
      };

    case ActionTypes.REMOVE_NOTIFICATION:
      return {
        ...state,
        notifications: state.notifications.filter(
          notification => notification.id !== action.payload
        )
      };

    case ActionTypes.CLEAR_NOTIFICATIONS:
      return {
        ...state,
        notifications: []
      };

    case ActionTypes.SET_THEME:
      return {
        ...state,
        theme: action.payload
      };

    case ActionTypes.TOGGLE_SIDEBAR:
      return {
        ...state,
        sidebarOpen: !state.sidebarOpen
      };

    case ActionTypes.SET_SIDEBAR_OPEN:
      return {
        ...state,
        sidebarOpen: action.payload
      };

    case ActionTypes.SET_CURRENT_PAGE:
      return {
        ...state,
        currentPage: action.payload
      };

    case ActionTypes.LOGOUT:
      return {
        ...initialState,
        theme: state.theme,
        sidebarOpen: state.sidebarOpen
      };

    default:
      return state;
  }
};

// Context
const AppContext = createContext();

// Provider Component
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Actions
  const setUser = useCallback((user) => {
    dispatch({ type: ActionTypes.SET_USER, payload: user });
  }, []);

  const setLoading = useCallback((loading) => {
    dispatch({ type: ActionTypes.SET_LOADING, payload: loading });
  }, []);

  const setError = useCallback((error) => {
    dispatch({ type: ActionTypes.SET_ERROR, payload: error });
  }, []);

  const clearError = useCallback(() => {
    dispatch({ type: ActionTypes.CLEAR_ERROR });
  }, []);

  const addNotification = useCallback((notification) => {
    const id = Date.now().toString();
    dispatch({ 
      type: ActionTypes.ADD_NOTIFICATION, 
      payload: { ...notification, id } 
    });
    return id;
  }, []);

  const removeNotification = useCallback((id) => {
    dispatch({ type: ActionTypes.REMOVE_NOTIFICATION, payload: id });
  }, []);

  const clearNotifications = useCallback(() => {
    dispatch({ type: ActionTypes.CLEAR_NOTIFICATIONS });
  }, []);

  const setTheme = useCallback((theme) => {
    dispatch({ type: ActionTypes.SET_THEME, payload: theme });
  }, []);

  const toggleSidebar = useCallback(() => {
    dispatch({ type: ActionTypes.TOGGLE_SIDEBAR });
  }, []);

  const setSidebarOpen = useCallback((open) => {
    dispatch({ type: ActionTypes.SET_SIDEBAR_OPEN, payload: open });
  }, []);

  const setCurrentPage = useCallback((page) => {
    dispatch({ type: ActionTypes.SET_CURRENT_PAGE, payload: page });
  }, []);

  const logout = useCallback(() => {
    dispatch({ type: ActionTypes.LOGOUT });
  }, []);

  // Computed values
  const isAdmin = state.user?.role === UserRole.ADMIN;
  const isFuncionario = state.user?.role === UserRole.FUNCIONARIO;
  const isCliente = state.user?.role === UserRole.CLIENTE;
  const isLoading = state.loading === LoadingState.LOADING;
  const hasError = Boolean(state.error);
  const hasNotifications = state.notifications.length > 0;

  const value = {
    // State
    ...state,
    
    // Computed
    isAdmin,
    isFuncionario,
    isCliente,
    isLoading,
    hasError,
    hasNotifications,
    
    // Actions
    setUser,
    setLoading,
    setError,
    clearError,
    addNotification,
    removeNotification,
    clearNotifications,
    setTheme,
    toggleSidebar,
    setSidebarOpen,
    setCurrentPage,
    logout
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// Hook
export const useApp = () => {
  const context = useContext(AppContext);
  
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  
  return context;
};
