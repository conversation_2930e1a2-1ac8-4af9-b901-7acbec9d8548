"use client";

import { useState } from "react";
import { Palette } from "lucide-react";

// Escala de cores do vermelho ao verde (ruim → bom)
const colors = [
    "#DC2626", // vermelho escuro - muito ruim
    "#EF4444", // vermelho médio
    "#F97316", // laranja escuro
    "#FB923C", // laranja claro
    "#FACC15", // amarelo
    "#A3E635", // verde-limão
    "#86EFAC", // verde claro
    "#4ADE80", // verde médio
    "#22C55E", // verde
    "#16A34A", // verde escuro
    "#15803D", // verde profundo - excelente
];

export default function ColorPicker({ selectedColor, onColorChange, disabled }) {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <div className="relative">
            <button
                disabled={disabled}
                className="flex items-center justify-center w-10 h-10 rounded-md border"
                type="button"
                onClick={() => setIsOpen(!isOpen)}
                style={{ backgroundColor: selectedColor || '#fff' }}
            >
                <Palette className="w-4 h-4" />
            </button>

            {isOpen && (
                <div className="absolute mt-2 p-2 bg-white border rounded-md shadow-lg flex gap-2 z-[100] flex-wrap w-64">
                    {colors.map((color) => (
                        <button
                            key={color}
                            type="button"
                            aria-label={color}
                            className="w-6 h-6 rounded-md border hover:scale-110 transition-transform"
                            style={{ backgroundColor: color }}
                            onClick={() => {
                                onColorChange(color);
                                setIsOpen(false);
                            }}
                            title={color}
                        />
                    ))}
                </div>
            )}
        </div>
    );
}
