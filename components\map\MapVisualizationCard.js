"use client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/base/Button';
import { Settings } from 'lucide-react';
import MapaContainer from '@/components/forms/MapaContainer';
import Histogram from '@/components/charts/Histogram';

const MineralSelector = ({ 
  selectedMineral, 
  minerais, 
  onMineralChange,
  onConfigureColors 
}) => (
  <div className="flex gap-2">
    <Select value={selectedMineral} onValueChange={onMineralChange}>
      <SelectTrigger className="flex-1">
        <SelectValue placeholder="Selecione um mineral" />
      </SelectTrigger>
      <SelectContent>
        {minerais.map((mineral, index) => (
          <SelectItem key={index} value={mineral}>
            {mineral}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
    
    {selectedMineral && (
      <Button
        variant="outline"
        onClick={onConfigureColors}
        icon={<Settings className="w-4 h-4" />}
      >
        Configurar Cores
      </Button>
    )}
  </div>
);

const MapContainer = ({ 
  geoJsonData, 
  selectedMineral, 
  data, 
  mineralColorConfig 
}) => {
  const mapData = geoJsonData?.geojson?.pontos?.features
    ? {
        type: "FeatureCollection",
        features: geoJsonData.geojson.pontos.features.filter(
          f => f.properties?.type === "hexagon"
        ),
      }
    : { type: "FeatureCollection", features: [] };

  return (
    <MapaContainer
      initialValue={mapData}
      selectedMineral={selectedMineral}
      data={data}
      mineralColorConfig={mineralColorConfig}
      mapMode="view"
    />
  );
};

const LegendPanel = ({ 
  selectedMineral, 
  mineralColorConfig, 
  getMineralStats, 
  data 
}) => {
  if (!selectedMineral) return null;

  const stats = getMineralStats(data, selectedMineral);

  return (
    <div className="w-60 bg-white rounded-lg shadow p-4 text-sm">
      <p className="font-semibold mb-2">
        Legenda - {selectedMineral}
      </p>
      
      {mineralColorConfig.map((faixa, idx) => (
        <div className="flex items-center mb-1" key={idx}>
          <div 
            className="w-4 h-4 rounded mr-2" 
            style={{ backgroundColor: faixa.cor }} 
          />
          <span>{faixa.minimo} - {faixa.maximo}</span>
        </div>
      ))}
      
      <div className="text-xs mt-2 space-y-1">
        <p>Máxima: {stats.max?.toFixed(2) || '-'}</p>
        <p>Média: {stats.avg?.toFixed(2) || '-'}</p>
        <p>Mínima: {stats.min?.toFixed(2) || '-'}</p>
      </div>
    </div>
  );
};

const MapVisualizationContent = ({ 
  geoJsonData,
  selectedMineral,
  data,
  mineralColorConfig,
  getMineralStats
}) => (
  <div className="flex flex-row gap-6">
    <div className="flex-1">
      <MapContainer
        geoJsonData={geoJsonData}
        selectedMineral={selectedMineral}
        data={data}
        mineralColorConfig={mineralColorConfig}
      />
      <Histogram 
        data={data} 
        selectedAttribute={selectedMineral}
        title={`Distribuição de ${selectedMineral}`}
      />
    </div>
    
    <LegendPanel
      selectedMineral={selectedMineral}
      mineralColorConfig={mineralColorConfig}
      getMineralStats={getMineralStats}
      data={data}
    />
  </div>
);

export const MapVisualizationCard = ({
  geoJsonData,
  selectedMineral,
  minerais,
  data,
  mineralColorConfig,
  getMineralStats,
  onMineralChange,
  onConfigureColors,
  loading
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Selecione um Mineral</CardTitle>
        <CardDescription>
          Escolha um mineral para visualização no mapa.
        </CardDescription>
        <MineralSelector
          selectedMineral={selectedMineral}
          minerais={minerais}
          onMineralChange={onMineralChange}
          onConfigureColors={onConfigureColors}
        />
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="h-[400px] flex justify-center items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <MapVisualizationContent
            geoJsonData={geoJsonData}
            selectedMineral={selectedMineral}
            data={data}
            mineralColorConfig={mineralColorConfig}
            getMineralStats={getMineralStats}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default MapVisualizationCard;
